<#-- 应用参数 -->
<#global appParams = {
  "charset"      : "utf-8", <#-- 字符集 -->
  "title"        : "专业 OBE 助手", <#-- 系统名称 -->
  "shortTitle"   : "", <#-- 系统简短名称，建议 8 个汉字以内，用于左侧导航顶部，如果为空会自动使用 title -->
  "copyright"    : "东软教育科技集团有限公司", <#-- 版权信息 -->
  "logo"         : "/images/logo_obe.png", <#-- 系统 LOGO 文件，200x200，PNG 格式 -->
  "colors"       : ["#1488CC", "#2B32B2"], <#-- 主题色，双色渐变，例如：["#1488CC", "#2B32B2"] -->
  "container"    : "container", <#-- 页面默认容器样式类，通常为 container 或 container-fluid -->
  "sideNav"      : true, <#-- 侧边导航菜单，如果为 false 则使用顶部导航菜单 -->
  "sideNavTheme" : "light", <#-- 侧边导航菜单主题，dark 或着 light -->
  "vendors"      : [
    "jquery", "tabler", "fontawesome", "pnotify", "ekko-lightbox",
    "jquery.form", "jquery.validate", "jquery.fullscreen", "bootstrap-select",
    "lodash", "vue", "elementui", "moment.js"
  ], <#-- 核心组件，注意顺序 -->
  "moduleDefault": {
    "rootPath"   : "/modules", <#-- 存放全部模块的根路径 -->
    "configFile" : "config.ftl", <#-- 模块配置文件名称 -->
    <#-- 以下默认参数可在模块内重新设置 -->
    "libFile"    : "lib.ftl", <#-- 模块库文件名称 -->
    "actionFile" : "action.ftl", <#-- 模块 Action 文件名称 -->
    "actionUrl"  : "/common/module_action.ftl" <#-- 模块 Action 通用调用地址 -->
  }, <#-- 模块默认配置 -->
  "mergeVendors" : false, <#-- 是否合并组件，通常为 false -->
  "version"      : "2.0", <#-- 系统版本号 -->
  "mobileMode"   : false, <#-- 是否移动模式 -->
  "devMode"      : true <#-- 是否开发模式，正式部署后应改为 false -->
}>

<#-- 应用主菜单 -->
<#global appMenus = {
  "home":
    {
      "name": "首页",
      "icon": "fe-home",
      "link": homepage
    },
  "base":
    {
      "name": "基础数据",
      "icon": "fe-settings",
      "link": "/base/base_index.ftl"
    },
  "major":
    {
      "name": "专业数据",
      "icon": "fe-book",
      "link": "/major/major_index.ftl"
    },
  "course":
    {
      "name": "存疑课程",
      "icon": "fe-help-circle",
      "link": "/base/course_invalid.ftl"
    },
  "compare":
    {
      "name": "数据对比",
      "icon": "fe-database",
      "link": "/compare/compare_index.ftl"
    }
}>

<#-- 导航右侧用户菜单 -->
<#global appUserMenus = [
  {
    "name": "退出系统",
    "link": "/user/loginout_action.ftl?action=logout",
    "icon": "log-out",
    "attrs": {
      "data-ajax-submit": true,
      "data-ajax-after-func": "autoAction('${base}${homepage}', false)"
    }
  }
]>
