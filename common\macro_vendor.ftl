<#-- 第三方组件路径 -->
<#assign vendorRoot = "/vendors">

<#-- 第三方组件信息 -->
<#assign appVendors = {

  <#-- 常规组件 -->
  "jquery": {
    "js": ["/jquery-3.7.1.min.js", "/jquery-migrate-3.5.2.min.js"]
  },
  "tabler": {
    "css": ["/tabler.min.css", "/tabler-icons.min.css"],
    "js": [
      "/js/popper.min.js",
      "/js/bootstrap.min.js",
      "/js/selectize.min.js",
      "/js/tabler.js"
    ]
  },
  "fontawesome": {
    "css": ["/font-awesome.min.css"]
  },
  "pnotify": {
    "css": ["/animate.min.css"],
    "js": ["/PNotify.all.js", "/pnotify.patch.js"]
  },
  "jquery.form": {
    "js": ["/jquery.form.min.js"]
  },
  "jquery.validate": {
    "js": ["/jquery.validate.min.js", "/messages_zh.js", "/jquery.validate.patch.js"]
  },
  "jquery.fullscreen": {
    "js": ["/jquery.fullscreen-min.js"]
  },
  "ekko-lightbox": {
    "css": ["/ekko-lightbox.css"],
    "js": ["/ekko-lightbox.js"]
  },
  "bootstrap-select": {
    "css": ["/bootstrap-select.min.css"],
    "js": ["/bootstrap-select.min.js", "/defaults-zh_CN.js"]
  },
  "lodash": {
    "js": ["/lodash.min.js"]
  },
  "vue": {
    "js": [appParams.devMode?then("/vue.js", "/vue.min.js"), "/vue-router.min.js"]
  },
  "elementui": {
    "css": ["/index.css", "/patch.css"],
    "js": ["/validator_messages.js", "/index.js"]
  },

  <#-- 扩展组件 -->
  "sortable": {
    "js": ["/Sortable.min.js", "/jquery-sortable.js", "/vuedraggable.umd.min.js"]
  },
  "datatables": {
    "css": ["/css/dataTables.all.css"],
    "js": [
      "/js/jquery.dataTables.min.js",
      "/js/jszip.min.js",
      "/js/dataTables.exts.min.js",
      "/js/datatables.patch.js"
    ]
  },
  "flexmark": {
    "css": ["/flexmark.css"],
    "js": ["/flexmark.js"]
  },
  "holmes": {
    "js": ["/holmes.min.js"]
  },
  "webuploader": {
    "css": ["/webuploader.patch.css"],
    "js": ["/webuploader.noimage.min.js", "/webuploader.patch.js"]
  },
  "moment.js": {
    "js": ["/moment.min.js", "/zh-cn.js", "/vue-moment.min.js", "/moment.patch.js"]
  },
  "tempus-dominus": {
    "css": ["/tempusdominus-bootstrap-4.min.css"],
    "js": ["/tempusdominus-bootstrap-4.min.js"]
  },
  "clipboard.js": {
    "js": ["/clipboard.min.js", "/vue-clipboard.min.js"]
  },
  "prism": {
    "css": ["/prism.css"],
    "js": ["/prism.js"]
  },
  "fr-editor": {
    "css": ["/css/froala_editor.pkgd.min.css", "/css/froala_style.min.css"],
    "js": ["/js/froala_editor.pkgd.min.js", "/js/zh_cn.js"]
  },
  "fr-view": {
    "path": "${vendorRoot}/fr-editor",
    "css": ["/css/froala_style.min.css"]
  },
  "video.js": {
    "css": ["/video-js.min.css"],
    "js": ["/video.min.js", "/zh-CN.js"]
  },
  "gojs": {
    "js": [
      appParams.devMode?then("/go-debug.js", "/go.js"),
      "/FishboneLayout.js",
      "/BalloonLink.js",
      "/GuidedDraggingTool.js"
    ]
  },
  "echarts": {
    "js": [
      "/echarts.min.js",
      "/index.min.js"
    ]
  },
  "swiper": {
    "css": ["/swiper-bundle.min.css"],
    "js": ["/swiper-bundle.min.js"]
  },
  "tdesign": {
    "css": ["/tdesign-icons.css", "/tdesign.min.css"],
    "js": ["/tdesign.min.js"]
  },
  "roadmap": {
    "css": ["/yozai.css", "/roadmap.css"],
    "js": ["/index.umd.js"]
  },
  "pinyin-pro": {
    "js": ["/index.js"]
  },
  "bootstrap-icons": {
    "css": ["/bootstrap-icons.min.css"]
  },
  "quasar": {
    "css": ["/quasar.min.css"],
    "js": ["/quasar.umd.min.js", "/config.js"]
  },
  "khos": {
    "css": ["/khos.css"]
  },
  "vue-advanced-chat": {
    "js": ["/vue-advanced-chat.umd.custom.js"]
  },
  "canvas-confetti": {
    "js": ["/confetti.browser.min.js"]
  },
  "relation-graph": {
    "js": ["/screenfull.min.js", "/html2canvas.min.js", "/relation-graph.umd.2.2.8.js"]
  },
  "three.js": {
    "js": ["/three.min.js"]
  },
  "3d-force-graph": {
    "js": ["/d3-dsv.min.js", "/dat.gui.min.js", "/d3-octree.min.js", "/d3-force-3d.min.js", "/3d-force-graph.min.js"]
  },
  "xlsx": {
    "js": ["/xlsx.full.min.js"]
  },
  "jsdiff": {
    "css": ["/diff2html.min.css"],
    "js": ["/diff.min.js", "/diff2html-ui-base.min.js"]
  },
  "g6": {
    "js": ["/g.umd.min.js", "/g6.min.js"]
  }
}>

<#-- 导入组件 -->
<#macro importVendors keys type merge=appParams.mergeVendors>
  <#-- 导入可合并文件 -->
  <#if merge>
    <#local mergeKeys = _.createSet()>
    <#local standaloneKeys = _.createSet()>
    <#list keys as key>
      <#if (appVendors[key].merge)!true>
        ${_.addToSet(mergeKeys, key)}
      <#else>
        ${_.addToSet(standaloneKeys, key)}
      </#if>
    </#list>
    <#if mergeKeys?has_content>
      <#local vendorsQueryString = mergeKeys?join('|')?url>
      <@import file="${vendorRoot}/app/merged.${type}.ftl?vendors=${vendorsQueryString}" type=type />
    </#if>
  <#else>
    <#local standaloneKeys = keys>
  </#if>

  <#-- 导入独立文件 -->
  <#list getVendorFiles(standaloneKeys, type) as file>
    <@import file=file type=type />
  </#list>
</#macro>

<#--
  合并前端第三方库
  keys : [ 要合并的第三方库数组 | sequence ]
  type : [ 内容类型 ] 合法选项：css / js
-->
<#macro mergeVendors keys type>
  <#list getVendorFiles(keys, type) as file>
    <#include file parse=false><#lt><#nt>
  </#list>
</#macro>

<#-- 导入组件文件 -->
<#macro import file type>
  <#if type == "css">
    <link rel="stylesheet" href="${base}${file}">
  <#elseif type == "js">
    <script src="${base}${file}"></script>
  </#if>
</#macro>

<#-- 获取 Vendor 包含的具体文件，自动去重 -->
<#function getVendorFiles keys type>
  <#local files = _.createSet()>
  <#list keys as key>
    <#if appVendors[key]??>
      <#-- 定义 vendor路径，如果没有自定义 path， 使用默认路径 + 库ID -->
      <#local vendorPath = (appVendors[key].path)!(vendorRoot + "/" + key)>
      <#list (appVendors[key][type])![] as file>
        ${_.addToSet(files, vendorPath + file)}
      </#list>
    </#if>
  </#list>
  <#return files>
</#function>
