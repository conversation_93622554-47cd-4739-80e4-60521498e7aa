<#global majid = ctxMap.majid>
<#global major = app.getMajor(majid)>
<@p.page title="${major.majid}-${major.majname}：专业学分统计">

  <#assign pageActions>
    <select class="form-control" onchange="location.href='?majid='+this.value">
      <#list sqlt.sqlQueryForList("obe.getMajors") as item>
        <option value="${item.majid}" ${(majid == item.majid)?then("selected", "")}>${item.majid} - ${item.majname}</option>
      </#list>
    </select>
  </#assign>

  <@p.menu module="major" subNavis=["major.credit_stats"] actions=pageActions>
  </@p.menu>

  <style>
    /* 针对 WebKit 内核的浏览器：Chrome, Safari, Edge, Opera */
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }

    /* 针对 Firefox 浏览器 */
    input[type=number] {
      -moz-appearance: textfield;
    }
  </style>

  <#-- 课程类别学分统计 -->
  <#assign creditStats = sqlt.sqlQueryForList("obe.getCreditStatsByCourseType", {"majid": majid})>
  <#assign title><b>课程类别学分统计</b></#assign>
  <#assign cardActions>
    <div class="mr-2">
      <span class="bg-yellow-lightest text-dark border rounded py-1 px-2 mr-1 font-weight-bold">黄色底色</span>学分信息可以手工调整，相关数据会自动重新计算
    </div>
  </#assign>
  <@p.card title=title actions=cardActions>
    <table class="table table-bordered table-vcenter table-sm">
      <thead>
        <tr>
          <th class="text-center text-nowrap align-middle" rowspan="2" style="width: 30%">课程类别</th>
          <th class="text-center text-nowrap align-middle" rowspan="2" style="width: 20%">课程性质</th>
          <th class="text-center text-nowrap align-middle" colspan="3">学分</th>
          <th class="text-center text-nowrap align-middle" rowspan="2" style="width: 20%">占总学分比例</th>
        </tr>
        <tr>
          <th class="text-center text-nowrap align-middle" style="width: 10%">理论</th>
          <th class="text-center text-nowrap align-middle" style="width: 10%">实践</th>
          <th class="text-center text-nowrap align-middle" style="width: 10%">合计</th>
        </tr>
      </thead>
      <tbody>
        <#list creditStats as item>
          <tr>
            <#if item.tanum == 1>
              <td class="text-center text-nowrap" rowspan="${item.tacount}">${item.typename!}</td>
            </#if>
            <td class="text-center text-nowrap">${item.attr!}</td>
            <td class="text-center text-nowrap"><input type="number" value="${item.credit_t}" data-attr="${item.attr}" data-credittype="t" style="height: auto; padding: 3px" class="font-weight-bold text-center form-control bg-yellow-lightest"></td>
            <td class="text-center text-nowrap"><input type="number" value="${item.credit_p}" data-attr="${item.attr}" data-credittype="p" style="height: auto; padding: 3px" class="font-weight-bold text-center form-control bg-yellow-lightest"></td>
            <td class="text-center text-nowrap row_credit"></td>
            <td class="text-center text-nowrap row_credit_percent"></td>
          </tr>
        </#list>
      </tbody>
    </table>

    <table class="table table-bordered text-center table-vcenter">
      <thead>
        <tr>
          <th rowspan="2" class="align-middle" style="width: 30%">总学分</th>
          <th colspan="2">课程性质</th>
          <th colspan="2">学分类型</th>
        </tr>
        <tr>
          <th style="width: 17.5%">必修</th>
          <th style="width: 17.5%">选修</th>
          <th style="width: 17.5%">理论</th>
          <th style="width: 17.5%">实践</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td rowspan="2" id="total_credit" class="font-weight-bold h1"></td>
          <td id="total_credit_req"></td>
          <td id="total_credit_opt"></td>
          <td id="total_credit_t"></td>
          <td id="total_credit_p"></td>
        </tr>
        <tr>
          <td id="total_credit_req_percent"></td>
          <td id="total_credit_opt_percent"></td>
          <td id="total_credit_t_percent"></td>
          <td id="total_credit_p_percent"></td>
        </tr>
      </tbody>
    </table>
  </@p.card>

  <@p.ready>
    // 格式化数字（保留一位小数）
    function formatNumberMath(num) {
      return Math.round(num * 10) / 10;
    }

    // 计算学分统计
    function calcStats() {
      var totalCredit = 0;
      var totalCreditReq = 0;
      var totalCreditOpt = 0;
      var totalCreditT = 0;
      var totalCreditP = 0;
      $("input[type=number]").each(function() {
        var val = parseFloat($(this).val());
        if (!isNaN(val)) {
          totalCredit += val;
          if ($(this).data("attr") == "必修") {
            totalCreditReq += val;
          } else {
            totalCreditOpt += val;
          }
          if ($(this).data("credittype") == "t") {
            totalCreditT += val;
          } else {
            totalCreditP += val;
          }
        }
      });
      $("#total_credit").text(formatNumberMath(totalCredit));
      $("#total_credit_req").text(formatNumberMath(totalCreditReq));
      $("#total_credit_opt").text(formatNumberMath(totalCreditOpt));
      $("#total_credit_t").text(formatNumberMath(totalCreditT));
      $("#total_credit_p").text(formatNumberMath(totalCreditP));
      $("#total_credit_req_percent").text(formatNumberMath(totalCreditReq / totalCredit * 100) + " %");
      $("#total_credit_opt_percent").text(formatNumberMath(totalCreditOpt / totalCredit * 100) + " %");
      $("#total_credit_t_percent").text(formatNumberMath(totalCreditT / totalCredit * 100) + " %");
      $("#total_credit_p_percent").text(formatNumberMath(totalCreditP / totalCredit * 100) + " %");
      // 低于 30% 高亮提示
      if (totalCreditP / totalCredit * 100 < 30) {
        $("#total_credit_p_percent").addClass("text-danger font-weight-bold");
        $("#total_credit_p_percent").addClass("bg-red-lightest");
      } else {
        $("#total_credit_p_percent").removeClass("text-danger font-weight-bold");
        $("#total_credit_p_percent").removeClass("bg-red-lightest");
      }
      $(".row_credit").each(function() {
        var creditT = parseFloat($(this).prev().prev().children().val());
        var creditP = parseFloat($(this).prev().children().val());
        if (!isNaN(creditT) && !isNaN(creditP)) {
          $(this).text(formatNumberMath(creditT + creditP));
        }
      });
      $(".row_credit_percent").each(function() {
        var credit = parseFloat($(this).prev().text());
        if (!isNaN(credit)) {
          $(this).text(formatNumberMath(credit / totalCredit * 100) + " %");
        }
      });
    }
    $(function() {
      calcStats();
      $("input[type=number]").change(calcStats);
    });
  </@p.ready>

  <#-- 课程学分一览表 -->
  <#assign courses = sqlt.sqlQueryForList("obe.getCoursesForCreditStats", {"majid": majid})>
  <#assign title><b>课程学分一览表</b></#assign>
  <#assign cardActions>
    <div class="mr-2 d-flex text-nowrap align-items-center">
      <div class="mr-2">
        <span class="tag tag-rounded ml-4">共&nbsp;<span id="courseCount">${courses?size}</span>&nbsp;门课程</span>
      </div>
      <select class="form-control form-control-sm" onchange="filterCourseList(this)">
        <option value="">全部课程</option>
        <#list creditStats as item>
          <option value="${item.typename}：${item.attr}">${item.typename}：${item.attr}</option>
        </#list>
      </select>
    </div>
  </#assign>
  <@p.card title=title actions=cardActions>
    <table class="table table-bordered table-vcenter table-hover table-sm" id="courseList">
      <thead>
        <tr>
          <th class="text-center text-nowrap" style="width: 5%">序号</th>
          <th class="text-center text-nowrap" style="width: 25%">课程类别</th>
          <th class="text-center text-nowrap" style="width: 10%">课程性质</th>
          <th class="text-nowrap" style="width: 40%">课程名称</th>
          <th class="text-center text-nowrap" style="width: 10%">理论学分</th>
          <th class="text-center text-nowrap" style="width: 10%">实践学分</th>
        </tr>
      </thead>
      <tbody>
        <#list courses as item>
          <tr data-mark="${item.typename}：${item.attr}">
            <td class="text-center text-nowrap">${item?counter}</td>
            <td class="text-center text-nowrap">${item.typename!}</td>
            <td class="text-center text-nowrap">${item.attr!}</td>
            <td class="text-nowrap">${item.coursename}</td>
            <td class="text-center text-nowrap">${item.r_credit_t!}</td>
            <td class="text-center text-nowrap">${item.r_credit_p!}</td>
          </tr>
        </#list>
      </tbody>
      <tfoot>
        <tr class="bg-gray-lightest">
          <th class="text-right text-nowrap" colspan="4">合计：</th>
          <th class="text-center text-nowrap" id="list_total_credit_t"></th>
          <th class="text-center text-nowrap" id="list_total_credit_p"></th>
        </tr>
      </tfoot>
    </table>
  </@p.card>

  <@p.ready>
    // 过滤课程列表
    function filterCourseList(src) {
      var val = $(src).val();
      if (val) {
        $("#courseList tbody tr").hide();
        $("#courseList tbody tr[data-mark='" + val + "']").show();
      } else {
        $("#courseList tbody tr").show();
      }
      calcListStats();
    }
    // 计算课程列表的合计
    function calcListStats() {
      var totalCreditT = 0;
      var totalCreditP = 0;
      $("#courseList tbody tr:visible").each(function() {
        if ($(this).find("td:eq(4)").text() != "") {
          totalCreditT += parseFloat($(this).find("td:eq(4)").text());
        }
        if ($(this).find("td:eq(5)").text() != "") {
          totalCreditP += parseFloat($(this).find("td:eq(5)").text());
        }
      });
      $("#list_total_credit_t").text(formatNumberMath(totalCreditT));
      $("#list_total_credit_p").text(formatNumberMath(totalCreditP));
      $("#courseCount").text($("#courseList tbody tr:visible").length);
    }
    // 初始化
    $(function() {
      calcListStats();
    });
  </@p.ready>

</@p.page>
