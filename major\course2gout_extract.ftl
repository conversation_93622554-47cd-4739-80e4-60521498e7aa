<#assign majid = ctxMap.majid>
<#assign major = app.getMajor(majid)>
<#assign extype = ctxMap.extype!"max">
<#assign extypes = {"calc": "加权", "max": "最值"}>
<@p.page title="课程支撑毕业要求情况-${major.majid}-${major.majname}">

  <#assign gradouts = sqlt.sqlQueryForList("obe.getGradouts", {"majid": majid, "goutlevel": 1})>
  <#assign courseList = sqlt.sqlQueryForList("obe.getRawMapCourses", {"majid": majid, "r_level": 2})>
  <#assign course2goutList = sqlt.sqlQueryForList("obe.extractCourse2Gout1", {"majid": majid})>
  <#assign course2goutMap = _.listToMap(course2goutList, "mapkey", "supplevel" + extype)>

  <style>
    body {
      background: #FFF;
    }
    table {
      border-collapse: collapse;
    }
    th, td {
      border: 1px solid #000;
      padding: 3px 5px;
      text-align: center;
      font-family: "宋体", "Microsoft YaHei", sans-serif;
    }
  </style>

  <table align="center">
    <thead>
      <tr>
        <th>课程名称 \ 毕业要求</th>
        <#list gradouts as gout>
          <th>毕业要求${gout.goutid}</th>
        </#list>
      </tr>
    </thead>
    <tbody>
      <#list courseList as course>
        <tr>
          <td nowrap>${course.coursename}</td>
          <#list gradouts as gout>
            <#assign suppval = course2goutMap[gout.goutid + '-' + course.coursename]!>
            <td>${suppval}</td>
          </#list>
        </tr>
      </#list>
    </tbody>
  </table>

</@p.page>
