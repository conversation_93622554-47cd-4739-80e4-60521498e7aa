<#assign majid = ctxMap.majid!>
<#if !majors??>
  <#assign majors = sqlt.sqlQueryForList("obe.getMajors")>
</#if>

<@p.card title="同一专业内对比">
  <@f.form id="compareForm1" action="compare_diff.ftl" ajaxSubmit=false method="get">
    <@f.ctrl tag="select" label="专业" name="majid" class="form-control" required=true size=6>
      <option value=""></option>
      <#list majors as item>
        <option value="${item.majid}" ${(majid == item.majid)?then("selected", "")}>${item.majid} - ${item.majname}</option>
      </#list>
    </@f.ctrl>
    <@f.ctrl tag="select" label="对比内容" name="type" required=true size=5>
      <option value=""></option>
      <#list app.compareTypes?filter(x -> x.for == 'single') as item>
        <option value="${item.id}" ${(ctxMap.type! == item.id)?then("selected", "")}>${item.name}</option>
      </#list>
    </@f.ctrl>
    <@f.ctrl tag="input" label="　" type="submit" class="btn btn-primary btn-block" groupClass="mb-0" value="确定" size=1 />
  </@f.form>
</@p.card>
