-- [autoInclude]
/* 尝试加载 SQL 执行文件中的 autoInclude */
<#compress>
  <#global _sqlFileName = .main_template_name?keep_before('.')>
  <#if _sqlFileName != '_common'>
    <#include "${_sqlFileName}.autoInclude" ignore_missing=true>
  </#if>
  <#global moduleId = moduleId!_sqlFileName>
</#compress>

-- [autoImport]
/* 尝试加载 SQL 执行文件中的 autoImport */
<#compress>
  <#if _sqlFileName != '_common'>
    <#include "${_sqlFileName}.autoImport" ignore_missing=true>
  </#if>
</#compress>
