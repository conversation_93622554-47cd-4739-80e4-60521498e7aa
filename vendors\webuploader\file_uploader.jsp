<%@page import="neo.lib.util.CommonUtils"%>
<%@page import="org.apache.commons.codec.digest.DigestUtils"%>
<%@page import="java.io.File"%>
<%@page import="java.util.HashMap"%>
<%@page import="java.util.Map"%>
<%@page import="java.util.Iterator"%>
<%@page import="java.util.List"%>
<%@page import="org.apache.commons.lang3.ArrayUtils"%>
<%@page import="org.apache.commons.lang3.RandomStringUtils"%>
<%@page import="org.apache.commons.fileupload.FileItem"%>
<%@page import="org.apache.commons.fileupload.servlet.ServletFileUpload"%>
<%@page import="org.apache.commons.fileupload.disk.DiskFileItemFactory"%>
<%@page import="com.fasterxml.jackson.databind.ObjectMapper"%>
<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%!// 内部签名 KEY
	static final String secretKey = "_SALT_FOR_FILE_UPLOAD_";
	// 允许的文件类型
	static final String[] allowedExts = new String[] {
			/* 图片 */
			"jpeg", "jpg", "gif", "png",
			/* 文档 */
			"doc", "docx", "xls", "xlsx", "ppt", "pptx", "pdf",
			/* 视频 */
			"mp4",
			/* 压缩包 */
			"zip", "rar", "7z" };
	// 上传文件的保存路径
	static final String configPath = "/files_upload";
	static final String dirTemp = "/files_upload/temp";
	// 过期时间（单位：秒)
	static final int expiredSecond = 1800;

	/* 获取当前时间戳 */
	long getTimestamp() {
		return System.currentTimeMillis() / 1000L;
	}

	/* 获取签名 */
	String getSign(String timestamp) {
		return DigestUtils.md5Hex(timestamp + secretKey);
	}%>
<%
	// 时间戳签名
	if (!ServletFileUpload.isMultipartContent(request)) {
		String currentTime = String.valueOf(getTimestamp());
		request.setAttribute("file_uploader_timestamp", currentTime);
		request.setAttribute("file_uploader_sign", getSign(currentTime));
		return;
	}

	request.setCharacterEncoding("UTF-8");
	response.setContentType("text/html; charset=UTF-8");

	Map jsonResult = new HashMap();
	String tempPath = application.getRealPath(dirTemp);
	String error = null; // 保存错误信息

	// 创建临时文件夹
	File dirTempFile = new File(tempPath);
	if (!dirTempFile.exists()) {
		dirTempFile.mkdirs();
	}

	DiskFileItemFactory factory = new DiskFileItemFactory();
	factory.setSizeThreshold(1 * 1024 * 1024); // 设定使用内存超过1M时，将产生临时文件并存储于临时目录中。
	factory.setRepository(new File(tempPath)); // 设定存储临时文件的目录。
	ServletFileUpload upload = new ServletFileUpload(factory);
	upload.setHeaderEncoding("UTF-8");
	try {
		List items = upload.parseRequest(request);
		Iterator itr = items.iterator();

		String sign = null;
		String timestamp = null;

		FileItem itemFile = null;

		while (itr.hasNext()) {
			FileItem item = (FileItem) itr.next();
			if (!item.isFormField()) {
				if (item.getSize() > 0) {
					itemFile = item;
				}
			} else if ("sign".equals(item.getFieldName())) {
				sign = item.getString();
			} else if ("timestamp".equals(item.getFieldName())) {
				timestamp = item.getString();
			}
		}

		if (sign == null || timestamp == null || !getSign(timestamp).equals(sign)) {
			error = "签名错误";
		} else if (getTimestamp() - Long.parseLong(timestamp) > expiredSecond) {
			error = "时间戳已过期";
		} else if (itemFile == null) {
			error = "未发现文件";
		}

		if (error == null) {
			String fileName = itemFile.getName();
			long fileSize = itemFile.getSize();
			String fileExt = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();

			if (ArrayUtils.contains(allowedExts, fileExt)) {

				// 文件保存目录路径
				String savePath = application.getRealPath(configPath);

				// 创建文件夹
				File dirFile = new File(savePath);
				if (!dirFile.exists()) {
					dirFile.mkdirs();
				}

				String newFileName = getTimestamp() + "_" + RandomStringUtils.randomAlphabetic(5).toLowerCase()
						+ "." + fileExt;

				File newFile = new File(savePath, newFileName);
				if (newFile.exists()) {
					newFile.delete();
				}
				itemFile.write(newFile);

				jsonResult.put("srcname", fileName);// 原始文件名
				jsonResult.put("servername", newFileName); // 服务器文件名
				jsonResult.put("link", request.getContextPath() + configPath + "/" + newFileName); // 文件完整访问地址
				jsonResult.put("size", fileSize); // 文件尺寸
				jsonResult.put("formatsize", CommonUtils.formatFileSize(fileSize)); // 文件尺寸（格式化，增强可读性）

			} else {
				error = "文件类型不支持";
			}
		}
	} catch (Exception e) {
		error = "未知错误";
	}

	if (error != null) {
		jsonResult.put("error", error);
	}

	out.clear();
	new ObjectMapper().writeValue(out, jsonResult);
%>
