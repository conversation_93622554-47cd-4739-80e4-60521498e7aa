<#ftl attributes={"public":true}>
<@p.page title="登录">
  <style>
    body {
      background: linear-gradient(to right, ${appParams.colors[0]}, ${appParams.colors[1]});
      display: flex;
    }

    .main {
      width: 966px;
      height: 565px;
      background: url("${base}/images/macbook.png") no-repeat bottom;
      margin: auto;
    }

    .main:before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url(${base}/images/waves.svg) no-repeat bottom;
      z-index: -1;
      pointer-events: none;
    }

    .card {
      width: 730px;
      height: 478px;
      margin: auto;
      margin-top: 28px;
    }

    div.fixed-height {
      height: 80px;
    }
  </style>

  <@p.ready>
    function loginSuccess() {
      location.href = "${base}${((ctxMap.url)!homepage)?replace("<|>","","r")?no_esc}";
    }
  </@p.ready>

  <div class="main">
    <div class="card">
      <div class="pt-5">
        <h1 class="d-flex justify-content-center mt-3">
          ${appParams.title}
        </h1>
        <div class="row">
          <div class="col-5 text-right pt-7">
            <img src="${base}${appParams.logo}" width="200" alt>
          </div>
          <div class="col-7">
            <div class="card-body w-75 ml-6 mt-5">
              <@f.form id="loginForm" ajaxAfterFunc="loginSuccess" action=_.safeUrl("loginout_action.ftl")>
                <input type="hidden" name="action" value="login">
                <@f.ctrl tag="input" label="用户名" groupClass="form-group fixed-height" type="text" name="login_name" required=true data\-msg\-required="请输入用户名" />
                <@f.ctrl tag="input" label="密码" groupClass="form-group fixed-height" type="password" name="password" required=true data\-msg\-required="请输入密码" />
                <@f.ctrl tag="input" type="submit" class="btn btn-primary" groupClass="form-group text-right" value="登录系统" />
              </@f.form>
            </div>
          </div>
        </div>
      </div>
      <div class="text-mute text-center mt-5">
        &copy; ${appParams.copyright}
        <span class="tag tag-rounded ml-2">Ver ${appParams.version}</span>
      </div>
    </div>
  </div>
</@p.page>
