.adm-block {
  display: block;
  width: 100%;
  border-radius: 6px;
  padding-left: 10px;
  margin-bottom: 1em;
  border: 1px solid;
  border-left-width: 4px;
  box-shadow: 2px 2px 6px #cdcdcd;
}

.adm-heading {
  display: block;
  font-weight: bold;
  font-size: 0.875rem;
  height: 1.8em;
  padding-top: 0.3em;
  padding-bottom: 2em;
  border-bottom: solid 1px;
  padding-left: 10px;
  margin-left: -10px;
}

.adm-body {
  display: block;
  padding-top: 1em;
  margin-left: 0.5em;
  margin-right: 1em;
}

.adm-heading>span {
  color: initial;
}

.adm-icon {
  height: 1.5rem;
  width: 1.5rem;
  display: inline-block;
  margin-bottom: 0.25em;
  vertical-align: middle;
  margin-right: 0.25em;
  margin-left: -0.25em;
}

.adm-hidden {
  display: none !important;
}

.adm-block.adm-collapsed>.adm-heading,
.adm-block.adm-open>.adm-heading {
  position: relative;
  cursor: pointer;
}

.adm-block.adm-collapsed>.adm-heading {
  margin-bottom: 0;
}

.adm-block.adm-collapsed .adm-body {
  display: none !important;
}

.adm-block.adm-open>.adm-heading:after,
.adm-block.adm-collapsed>.adm-heading:after {
  display: inline-block;
  position: absolute;
  top: calc(50% - .8em);
  right: 0.5em;
  font-size: 1.3em;
  content: '▼';
}

.adm-block.adm-collapsed>.adm-heading:after {
  right: 0.50em;
  top: calc(50% - .8em);
  transform: rotate(-90deg);
}

/* default scheme */

.adm-block {
  border-color: #ebebeb;
  border-bottom-color: #bfbfbf;
}

.adm-block.adm-abstract {
  border-left-color: #48C4FF;
}

.adm-block.adm-abstract .adm-heading {
  background: #E8F7FF;
  color: #48C4FF;
  border-bottom-color: #dbf3ff;
}

.adm-block.adm-abstract.adm-open>.adm-heading:after,
.adm-block.adm-abstract.adm-collapsed>.adm-heading:after {
  color: #80d9ff;
}


.adm-block.adm-bug {
  border-left-color: #F50057;
}

.adm-block.adm-bug .adm-heading {
  background: #FEE7EE;
  color: #F50057;
  border-bottom-color: #fcd9e4;
}

.adm-block.adm-bug.adm-open>.adm-heading:after,
.adm-block.adm-bug.adm-collapsed>.adm-heading:after {
  color: #f57aab;
}

.adm-block.adm-danger {
  border-left-color: #FE1744;
}

.adm-block.adm-danger .adm-heading {
  background: #FFE9ED;
  color: #FE1744;
  border-bottom-color: #ffd9e0;
}

.adm-block.adm-danger.adm-open>.adm-heading:after,
.adm-block.adm-danger.adm-collapsed>.adm-heading:after {
  color: #fc7e97;
}

.adm-block.adm-example {
  border-left-color: #7940ff;
}

.adm-block.adm-example .adm-heading {
  background: #EFEBFF;
  color: #7940ff;
  border-bottom-color: #e0d9ff;
}

.adm-block.adm-example.adm-open>.adm-heading:after,
.adm-block.adm-example.adm-collapsed>.adm-heading:after {
  color: #b199ff;
}

.adm-block.adm-fail {
  border-left-color: #FE5E5E;
}

.adm-block.adm-fail .adm-heading {
  background: #FFEEEE;
  color: #Fe5e5e;
  border-bottom-color: #ffe3e3;
}

.adm-block.adm-fail.adm-open>.adm-heading:after,
.adm-block.adm-fail.adm-collapsed>.adm-heading:after {
  color: #fcb1b1;
}

.adm-block.adm-faq {
  border-left-color: #5ED116;
}

.adm-block.adm-faq .adm-heading {
  background: #EEFAE8;
  color: #5ED116;
  border-bottom-color: #e6fadc;
}

.adm-block.adm-faq.adm-open>.adm-heading:after,
.adm-block.adm-faq.adm-collapsed>.adm-heading:after {
  color: #98cf72;
}

.adm-block.adm-info {
  border-left-color: #00B8D4;
}

.adm-block.adm-info .adm-heading {
  background: #E8F7FA;
  color: #00B8D4;
  border-bottom-color: #dcf5fa;
}

.adm-block.adm-info.adm-open>.adm-heading:after,
.adm-block.adm-info.adm-collapsed>.adm-heading:after {
  color: #83ced6;
}

.adm-block.adm-note {
  border-left-color: #448AFF;
}

.adm-block.adm-note .adm-heading {
  background: #EDF4FF;
  color: #448AFF;
  border-bottom-color: #e0edff;
}

.adm-block.adm-note.adm-open>.adm-heading:after,
.adm-block.adm-note.adm-collapsed>.adm-heading:after {
  color: #8cb8ff;
}

.adm-block.adm-quote {
  border-left-color: #9E9E9E;
}

.adm-block.adm-quote .adm-heading {
  background: #F4F4F4;
  color: #9E9E9E;
  border-bottom-color: #e8e8e8;
}

.adm-block.adm-quote.adm-open>.adm-heading:after,
.adm-block.adm-quote.adm-collapsed>.adm-heading:after {
  color: #b3b3b3;
}

.adm-block.adm-success {
  border-left-color: #1DCD63;
}

.adm-block.adm-success .adm-heading {
  background: #E9F8EE;
  color: #1DCD63;
  border-bottom-color: #dcf7e5;
}

.adm-block.adm-success.adm-open>.adm-heading:after,
.adm-block.adm-success.adm-collapsed>.adm-heading:after {
  color: #7acc98;
}

.adm-block.adm-tip {
  border-left-color: #01BFA5;
}

.adm-block.adm-tip .adm-heading {
  background: #E9F9F6;
  color: #01BFA5;
  border-bottom-color: #dcf7f2;
}

.adm-block.adm-tip.adm-open>.adm-heading:after,
.adm-block.adm-tip.adm-collapsed>.adm-heading:after {
  color: #7dd1c0;
}

.adm-block.adm-warning {
  border-left-color: #FF9001;
}

.adm-block.adm-warning .adm-heading {
  background: #FEF3E8;
  color: #FF9001;
  border-bottom-color: #Fef3e8;
}

.adm-block.adm-warning.adm-open>.adm-heading:after,
.adm-block.adm-warning.adm-collapsed>.adm-heading:after {
  color: #fcbb6a;
}

/* 自定义样式补丁 */

.text-wrap>svg:first-child+* {
  margin-top: 0;
}

div.markdown-content ol,
div.markdown ul {
  padding-left: 1.5em;
  line-height: 1.8;
}

div.markdown-toc h1 {
  display: none;
}

div.markdown-toc {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  max-height: 100vh;
  overflow-y: auto;
}

div.markdown-toc ul {
  padding-left: 1.2em;
  margin: 5px;
  list-style-type: none;
  font-size: 0.875rem;
}

ul.markdown-toc-list>li>a {
  font-weight: 700;
}

div.markdown-content li.task-list-item {
  margin-left: -1.2em;
  list-style-type: none;
}

div.markdown-content blockquote {
  font-style: normal;
  border-left: 0.25rem solid #dfe2e5;
  padding-left: 1rem;
}

div.markdown-content pre {
  margin-top: 1rem;
}

div.markdown-content>h1,
div.markdown-content>h2,
div.markdown-content>h3,
div.markdown-content>h4,
div.markdown-content>h5,
div.markdown-content>h6 {
  padding-top: 0.5em;
}

div.markdown-content>h1:first-child,
div.markdown-content>h2:first-child,
div.markdown-content>h3:first-child {
  padding-top: 0;
}

div.markdown-content :not(pre) code {
  color: #cd201f
}

div.markdown-content h2 {
  border-bottom: 1px solid rgba(0, 40, 100, .12);
  padding-bottom: 0.7em;
}

div.markdown-content table {
  font-size: 14px;
  font-family: Monaco, Consolas, "Liberation Mono", "Courier New", "Microsoft YaHei", monospace !important;
}

div.markdown-content table th,
div.markdown-content table td {
  font-size: 14px;
  padding: 5px 10px;
}

div.markdown-content table.col1-5 th:nth-child(1) { width: 5%; }
div.markdown-content table.col1-10 th:nth-child(1) { width: 10%; }
div.markdown-content table.col1-15 th:nth-child(1) { width: 15%; }
div.markdown-content table.col1-20 th:nth-child(1) { width: 20%; }
div.markdown-content table.col1-25 th:nth-child(1) { width: 25%; }
div.markdown-content table.col1-30 th:nth-child(1) { width: 30%; }
div.markdown-content table.col1-35 th:nth-child(1) { width: 35%; }
div.markdown-content table.col1-40 th:nth-child(1) { width: 40%; }
div.markdown-content table.col1-45 th:nth-child(1) { width: 45%; }
div.markdown-content table.col1-50 th:nth-child(1) { width: 50%; }
div.markdown-content table.col1-55 th:nth-child(1) { width: 55%; }
div.markdown-content table.col1-60 th:nth-child(1) { width: 60%; }
div.markdown-content table.col1-65 th:nth-child(1) { width: 65%; }
div.markdown-content table.col1-70 th:nth-child(1) { width: 70%; }
div.markdown-content table.col1-75 th:nth-child(1) { width: 75%; }
div.markdown-content table.col1-80 th:nth-child(1) { width: 80%; }
div.markdown-content table.col1-85 th:nth-child(1) { width: 85%; }
div.markdown-content table.col1-90 th:nth-child(1) { width: 90%; }
div.markdown-content table.col1-95 th:nth-child(1) { width: 95%; }
div.markdown-content table.col2-5 th:nth-child(2) { width: 5%; }
div.markdown-content table.col2-10 th:nth-child(2) { width: 10%; }
div.markdown-content table.col2-15 th:nth-child(2) { width: 15%; }
div.markdown-content table.col2-20 th:nth-child(2) { width: 20%; }
div.markdown-content table.col2-25 th:nth-child(2) { width: 25%; }
div.markdown-content table.col2-30 th:nth-child(2) { width: 30%; }
div.markdown-content table.col2-35 th:nth-child(2) { width: 35%; }
div.markdown-content table.col2-40 th:nth-child(2) { width: 40%; }
div.markdown-content table.col2-45 th:nth-child(2) { width: 45%; }
div.markdown-content table.col2-50 th:nth-child(2) { width: 50%; }
div.markdown-content table.col2-55 th:nth-child(2) { width: 55%; }
div.markdown-content table.col2-60 th:nth-child(2) { width: 60%; }
div.markdown-content table.col2-65 th:nth-child(2) { width: 65%; }
div.markdown-content table.col2-70 th:nth-child(2) { width: 70%; }
div.markdown-content table.col2-75 th:nth-child(2) { width: 75%; }
div.markdown-content table.col2-80 th:nth-child(2) { width: 80%; }
div.markdown-content table.col2-85 th:nth-child(2) { width: 85%; }
div.markdown-content table.col2-90 th:nth-child(2) { width: 90%; }
div.markdown-content table.col2-95 th:nth-child(2) { width: 95%; }
div.markdown-content table.col3-5 th:nth-child(3) { width: 5%; }
div.markdown-content table.col3-10 th:nth-child(3) { width: 10%; }
div.markdown-content table.col3-15 th:nth-child(3) { width: 15%; }
div.markdown-content table.col3-20 th:nth-child(3) { width: 20%; }
div.markdown-content table.col3-25 th:nth-child(3) { width: 25%; }
div.markdown-content table.col3-30 th:nth-child(3) { width: 30%; }
div.markdown-content table.col3-35 th:nth-child(3) { width: 35%; }
div.markdown-content table.col3-40 th:nth-child(3) { width: 40%; }
div.markdown-content table.col3-45 th:nth-child(3) { width: 45%; }
div.markdown-content table.col3-50 th:nth-child(3) { width: 50%; }
div.markdown-content table.col3-55 th:nth-child(3) { width: 55%; }
div.markdown-content table.col3-60 th:nth-child(3) { width: 60%; }
div.markdown-content table.col3-65 th:nth-child(3) { width: 65%; }
div.markdown-content table.col3-70 th:nth-child(3) { width: 70%; }
div.markdown-content table.col3-75 th:nth-child(3) { width: 75%; }
div.markdown-content table.col3-80 th:nth-child(3) { width: 80%; }
div.markdown-content table.col3-85 th:nth-child(3) { width: 85%; }
div.markdown-content table.col3-90 th:nth-child(3) { width: 90%; }
div.markdown-content table.col3-95 th:nth-child(3) { width: 95%; }
div.markdown-content table.col4-5 th:nth-child(4) { width: 5%; }
div.markdown-content table.col4-10 th:nth-child(4) { width: 10%; }
div.markdown-content table.col4-15 th:nth-child(4) { width: 15%; }
div.markdown-content table.col4-20 th:nth-child(4) { width: 20%; }
div.markdown-content table.col4-25 th:nth-child(4) { width: 25%; }
div.markdown-content table.col4-30 th:nth-child(4) { width: 30%; }
div.markdown-content table.col4-35 th:nth-child(4) { width: 35%; }
div.markdown-content table.col4-40 th:nth-child(4) { width: 40%; }
div.markdown-content table.col4-45 th:nth-child(4) { width: 45%; }
div.markdown-content table.col4-50 th:nth-child(4) { width: 50%; }
div.markdown-content table.col4-55 th:nth-child(4) { width: 55%; }
div.markdown-content table.col4-60 th:nth-child(4) { width: 60%; }
div.markdown-content table.col4-65 th:nth-child(4) { width: 65%; }
div.markdown-content table.col4-70 th:nth-child(4) { width: 70%; }
div.markdown-content table.col4-75 th:nth-child(4) { width: 75%; }
div.markdown-content table.col4-80 th:nth-child(4) { width: 80%; }
div.markdown-content table.col4-85 th:nth-child(4) { width: 85%; }
div.markdown-content table.col4-90 th:nth-child(4) { width: 90%; }
div.markdown-content table.col4-95 th:nth-child(4) { width: 95%; }
div.markdown-content table.col5-5 th:nth-child(5) { width: 5%; }
div.markdown-content table.col5-10 th:nth-child(5) { width: 10%; }
div.markdown-content table.col5-15 th:nth-child(5) { width: 15%; }
div.markdown-content table.col5-20 th:nth-child(5) { width: 20%; }
div.markdown-content table.col5-25 th:nth-child(5) { width: 25%; }
div.markdown-content table.col5-30 th:nth-child(5) { width: 30%; }
div.markdown-content table.col5-35 th:nth-child(5) { width: 35%; }
div.markdown-content table.col5-40 th:nth-child(5) { width: 40%; }
div.markdown-content table.col5-45 th:nth-child(5) { width: 45%; }
div.markdown-content table.col5-50 th:nth-child(5) { width: 50%; }
div.markdown-content table.col5-55 th:nth-child(5) { width: 55%; }
div.markdown-content table.col5-60 th:nth-child(5) { width: 60%; }
div.markdown-content table.col5-65 th:nth-child(5) { width: 65%; }
div.markdown-content table.col5-70 th:nth-child(5) { width: 70%; }
div.markdown-content table.col5-75 th:nth-child(5) { width: 75%; }
div.markdown-content table.col5-80 th:nth-child(5) { width: 80%; }
div.markdown-content table.col5-85 th:nth-child(5) { width: 85%; }
div.markdown-content table.col5-90 th:nth-child(5) { width: 90%; }
div.markdown-content table.col5-95 th:nth-child(5) { width: 95%; }
div.markdown-content table.col6-5 th:nth-child(6) { width: 5%; }
div.markdown-content table.col6-10 th:nth-child(6) { width: 10%; }
div.markdown-content table.col6-15 th:nth-child(6) { width: 15%; }
div.markdown-content table.col6-20 th:nth-child(6) { width: 20%; }
div.markdown-content table.col6-25 th:nth-child(6) { width: 25%; }
div.markdown-content table.col6-30 th:nth-child(6) { width: 30%; }
div.markdown-content table.col6-35 th:nth-child(6) { width: 35%; }
div.markdown-content table.col6-40 th:nth-child(6) { width: 40%; }
div.markdown-content table.col6-45 th:nth-child(6) { width: 45%; }
div.markdown-content table.col6-50 th:nth-child(6) { width: 50%; }
div.markdown-content table.col6-55 th:nth-child(6) { width: 55%; }
div.markdown-content table.col6-60 th:nth-child(6) { width: 60%; }
div.markdown-content table.col6-65 th:nth-child(6) { width: 65%; }
div.markdown-content table.col6-70 th:nth-child(6) { width: 70%; }
div.markdown-content table.col6-75 th:nth-child(6) { width: 75%; }
div.markdown-content table.col6-80 th:nth-child(6) { width: 80%; }
div.markdown-content table.col6-85 th:nth-child(6) { width: 85%; }
div.markdown-content table.col6-90 th:nth-child(6) { width: 90%; }
div.markdown-content table.col6-95 th:nth-child(6) { width: 95%; }
