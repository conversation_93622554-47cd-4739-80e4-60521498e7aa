<#global majid = ctxMap.majid>
<#global major = app.getMajor(majid)>
<@p.page title="${major.majid}-${major.majname}：专业课开设情况鱼骨图" extVendors=["g6"]>

  <style>
    html, body {
      overflow: hidden; /* 完全禁用滚动 */
      width: 100%;
      height: 100%;
    }
    body {
      background: #FFF;
    }
  </style>

  <!-- 准备一个容器 -->
  <div id="container" style="width: 100%; height: 100%"></div>

  <@p.ready file="fishbone.js" />

</@p.page>
