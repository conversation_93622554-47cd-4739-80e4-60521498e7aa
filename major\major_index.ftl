<@p.page title="专业数据统计表">

  <@p.menu module="major">
    <b>提示</b>：点击专业名称可查看专业详细信息。
  </@p.menu>

  <@p.card direct=true>
    <#assign majors = sqlt.sqlQueryForList("obe.getMajors", {"withStats": true})>
    <table class="table table-bordered table-vcenter mb-0 table-hover">
      <thead class="text-center">
        <tr>
          <th colspan="2">专业基本信息</th>
          <th colspan="4">毕业要求支撑培养目标</th>
          <th colspan="3">课程支撑毕业要求及指标点</th>
          <th colspan="2">其他</th>
        </tr>
        <tr>
          <th class="align-middle">专业代码</th>
          <th class="align-middle">专业名称</th>
          <th class="align-middle">培养<br>目标<br>数量</th>
          <th class="align-middle">毕业<br>要求<br>数量</th>
          <th class="align-middle">指标点<br>数量</th>
          <th class="align-middle">支撑情况</th>
          <th class="align-middle">课程总数量<br>合并 / 未合并</th>
          <th class="align-middle">支撑<br>毕业要求<br>课程数量</th>
          <th class="align-middle">支撑<br>指标点<br>课程数量</th>
          <#--  <th class="align-middle">支撑情况</th>  -->
          <th class="align-middle">学分统计</th>
          <th class="align-middle">专业课开设</th>
        </tr>
      </thead>
      <tbody>
        <#list majors as item>
          <#assign isMain = !item.majcode?has_content>
          <tr class="text-center">
            <td class="${isMain?then('font-weight-bold','')}">${item.majid}</td>
            <td class="text-left ${isMain?then('font-weight-bold','')}"><a href="major_detail.ftl?majid=${item.majid}">${item.majname}</a></td>
            <@app.warningByZero class="h5" val=item.eobjnum />
            <@app.warningByZero class="h5" val=item.gout1num />
            <@app.warningByZero class="h5" val=item.gout2num />
            <@app.warningByZero class="h5" val=item.gout2eobj1num+item.gout2eobj2num>
              <#if item.gout2eobj1num gt 0><span class="tag tag-success">毕业要求<#if item.gout2eobj2num gt 0>及指标点</#if></span></#if>
              <#if item.gout2eobj1num + item.gout2eobj2num == 0><span class="tag tag-red">尚未支撑</span></#if>
            </@app.warningByZero>
            <@app.warningByZero class="h5" val=item.coursenum>
              ${item.viewcoursenum} / ${item.coursenum}
            </@app.warningByZero>
            <@app.warningByZero class="h5" val=item.mapcourse1num matchClass="text-muted bg-gray-lightest" />
            <@app.warningByZero class="h5" val=item.mapcourse2num />
            <#--
            <@app.warningByZero class="h5" val=item.course2gout1num+item.course2gout2num>
              <#if item.course2gout1num gt 0>
                <span class="tag tag-success">毕业要求<#if item.course2gout2num gt 0>及指标点</#if></span>
              <#elseif item.course2gout2num gt 0>
                <span class="tag tag-success">指标点</span>
              </#if>
              <#if item.course2gout1num + item.course2gout2num == 0><span class="tag tag-red">尚未支撑</span></#if>
            </@app.warningByZero>
            -->
            <td class="text-center text-nowrap">
              <a href="credit_stats.ftl?majid=${item.majid}" class="btn btn-outline-primary btn-sm">学分统计</a>
            </td>
            <td class="text-center text-nowrap">
              <a href="term_courses.ftl?majid=${item.majid}" class="btn btn-outline-primary btn-sm">专业课开设</a>
            </td>
          </tr>
        </#list>
      </tbody>
    </table>
  </@p.card>

</@p.page>
