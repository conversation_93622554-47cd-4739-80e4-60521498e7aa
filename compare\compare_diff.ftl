<#assign compareType = app.compareTypes?filter(x -> x.id == ctxMap.type)[0]>

<@p.page title="对比结果" extVendors=["jsdiff"]>

  <@p.menu title="" module="compare" subNavis=["compare.compare_diff_" + compareType.for]>
  </@p.menu>

  <style>
    .d2h-file-wrapper {
      margin-bottom: 0;
    }
    .d2h-diff-table, .src-content {
      font-size: 16px;
      font-family: Monaco, Consolas, "Liberation Mono", "Courier New", "Microsoft YaHei", monospace;
    }
    .d2h-file-name {
      font-size: 16px;
      font-weight: bold;
    }
    .d2h-code-side-linenumber {
      cursor: default;
    }
  </style>

  <#-- 根据对比内容类型进行参数准备 -->
  <#switch compareType.id>
    <#on "eobj">
      <#assign sqlId1 = "obe.getEduobjs">
      <#assign sqlParams1 = {"majid": ctxMap.majid1}>
      <#assign sqlId2 = "obe.getEduobjs">
      <#assign sqlParams2 = {"majid": ctxMap.majid2}>
      <#macro renderItem item>${item.eobjid}.${item.eobjshortname?has_content?then("【${item.eobjshortname}】","")}${item.eobjname}</#macro>
    <#on "gout", "gout1", "gout2">
      <#assign sqlId1 = "obe.getGradouts">
      <#assign sqlParams1 = {"majid": ctxMap.majid1, "goutlevel": compareType.id?replace("gout", "")}>
      <#assign sqlId2 = "obe.getGradouts">
      <#assign sqlParams2 = {"majid": ctxMap.majid2, "goutlevel": compareType.id?replace("gout", "")}>
      <#macro renderItem item>${item.goutid} ${item.goutshortname?has_content?then("【${item.goutshortname}】","")}${item.goutname}</#macro>
    <#on "mapcourse1_mapcourse2">
      <#assign sqlId1 = "obe.getRawMapCourses">
      <#assign sqlParams1 = {"majid": ctxMap.majid, "r_level": "1"}>
      <#assign sqlId2 = "obe.getRawMapCourses">
      <#assign sqlParams2 = {"majid": ctxMap.majid, "r_level": "2"}>
      <#macro renderItem item>${item.r_coursename}</#macro>
    <#on "course_mapcourse_all1", "course_mapcourse_onlyreq1", "course_mapcourse_all2", "course_mapcourse_onlyreq2">
      <#assign sqlId1 = "obe.getViewCourses">
      <#assign sqlParams1 = {"majid": ctxMap.majid, "orderby": "coursename"}>
      <#assign sqlId2 = "obe.getRawMapCourses">
      <#assign sqlParams2 = {"majid": ctxMap.majid, "r_level": compareType.id[compareType.id?length - 1], "orderby": "coursename"}>
      <#if compareType.id?starts_with("course_mapcourse_onlyreq")>
        <#assign sqlParams1 += {"attr": "必修"}>
        <#assign sqlParams2 += {"attr": "必修"}>
      </#if>
      <#macro renderItem item>${item.coursename}${((item.attr!)=="选修")?then("【选修】","")}</#macro>
    <#default>
      ${_.throwException("errorCompareType")}
  </#switch>

  <#assign list1 = sqlt.sqlQueryForList(sqlId1, sqlParams1)>
  <#assign list2 = sqlt.sqlQueryForList(sqlId2, sqlParams2)>

  <div class="row d-none">
    <div class="col"><pre id="text1"><#list list1 as item><@renderItem item /><#sep>${"\n"}</#list></pre></div>
    <div class="col"><pre id="text2"><#list list2 as item><@renderItem item /><#sep>${"\n"}</#list></pre></div>
  </div>

  <#include "compare_form_${compareType.for}.ftl">

  <@p.card direct=true>
    <div id="diffResult" class="p-0"></div>
  </@p.card>

  <@p.ready>
    $(function () {
      var text1 = document.getElementById("text1").innerText;
      var text2 = document.getElementById("text2").innerText;
      var diffString = Diff.createPatch("${compareType.name}", text1, text2, "", "", {
        "context": Infinity
      });
      if (Diff.parsePatch(diffString)[0].hunks.length == 0) {
        $("#diffResult").html('<div class="alert alert-secondary mb-0">对比没有差异，相同内容如下：</div><pre class="mb-0 src-content bg-white">' + $("#text1").html() + '</pre>');
        return;
      }
      var targetElement = document.getElementById("diffResult");
      var configuration = {
        outputFormat: "side-by-side",
        drawFileList: false,
        matching: "lines",
        diffStyle: "char",
        fileContentToggle: false,
        highlight: false
      };
      var diff2htmlUi = new Diff2HtmlUI(targetElement, diffString, configuration);
      diff2htmlUi.draw();
    });
  </@p.ready>

</@p.page>
