<#ftl output_format="JSON">

<#-- 更新密码 -->
<#macro updatePassword>
  <#local user = sqlt.sqlQueryForMap("user.getUser", {"userid":loginUser.userid})>
  <#if _.md5(ctxMap.oldpassword)?upper_case != user.password?upper_case>
    <#assign actionResult = _.actionResult(1001, "原密码不正确")>
  <#elseif sqlt.sqlUpdate("user.updateUserPassword", {"userid":user.userid, "password":ctxMap.newpassword}) == 1>
    <#assign actionResult = _.actionResult(0, "密码修改成功")>
  <#else>
    <#assign actionResult = _.actionResult(1002, "未知错误，密码修改失败")>
  </#if>
</#macro>

<#-- 执行 Action -->
<@p.renderActionResult />
