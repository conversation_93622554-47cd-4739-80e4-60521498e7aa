<#---------- 系统功能配置 ---------->

<#-- 功能开关 -->
<#assign sysFuncs = {
}>

<#-- 错误提示 -->
<#assign sysErrors = {
  "noAccess": "无权访问",
  "errorCompareType": "未知的对比类型"
}>

<#-- 导航字典 -->
<#assign sysNavis = {
  "major.major_detail": ["专业数据详情", r"${base}/major/major_detail.ftl?majid=${ctxMap.majid}"],
  "major.credit_stats": ["专业学分统计", r"${base}/major/credit_stats.ftl?majid=${ctxMap.majid}"],
  "major.term_courses": ["专业课开设情况", r"${base}/major/term_courses.ftl?majid=${ctxMap.majid}"],
  "compare.compare_diff_single": ["对比结果（同一专业内对比）", r"${base}/compare/compare_diff.ftl?majid=${ctxMap.majid!}&majid1=${ctxMap.majid1!}&majid2=${ctxMap.majid2!}&type=${ctxMap.type}"],
  "compare.compare_diff_multi": ["对比结果（不同专业间对比）", r"${base}/compare/compare_diff.ftl?majid1=${ctxMap.majid1!}&majid2=${ctxMap.majid2!}&type=${ctxMap.type}"]
}>

<#---------- 通用业务宏 ----------->

<#-- 获取专业基本信息 -->
<#function getMajor majid>
  <#return sqlt.sqlQueryForMapOrNull("obe.getMajor", {"majid": majid})>
</#function>

<#-- 获取支撑程度样式 -->
<#assign suppLevelThemes = {
  "H": {"id":"H", "name":"强支撑", "text":"text-blue", "bg":"bg-azure-lighter"},
  "M": {"id":"M", "name":"中支撑", "text":"text-yellow", "bg":"bg-yellow-lightest"},
  "L": {"id":"L", "name":"弱支撑", "text":"text-gray", "bg":"bg-gray-lightest"}
}>
<#function getSuppLevelTheme level>
  <#if level?has_content>
    <#local theme = suppLevelThemes[level]>
    <#return theme.text + " " + theme.bg>
  <#else>
    <#return "">
  </#if>
</#function>

<#-- 根据匹配值进行特殊处理 -->
<#macro renderTagByVal val matchVal tag class matchClass>
  <#local text><#compress><#nested></#compress></#local>
  <#if !text?has_content>
    <#local text = val>
  </#if>
  <#if val == matchVal>
    <${tag} class="${class} ${matchClass}">${text}</${tag}>
  <#else>
    <${tag} class="${class}">${text}</${tag}>
  </#if>
</#macro>

<#-- 根据数量高亮提醒显示 -->
<#macro warningByZero val matchVal=0 tag="td" class="" matchClass="text-danger bg-red-lightest">
  <@renderTagByVal val matchVal tag class matchClass><#nested></@renderTagByVal>
</#macro>

<#-- 构建毕业要求或指标点完整名称 -->
<#function getGoutFullName gout>
  <#if gout.goutlevel == 1>
    <#local space = ". ">
  <#else>
    <#local space = " ">
  </#if>
  <#if gout.goutshortname?has_content>
    <#local shortname = "【" + gout.goutshortname + "】">
  </#if>
  <#return "${gout.goutid}${space}${shortname!}${gout.goutname}">
</#function>

<#---------- 数据对比 ---------->

<#-- 对比类型 -->
<#assign compareTypes = [
  {"id": "eobj", "name": "培养目标", "for": "multi"},
  {"id": "gout", "name": "毕业要求（一级）及指标点（二级）", "for": "multi"},
  {"id": "gout1", "name": "毕业要求（一级）", "for": "multi"},
  {"id": "gout2", "name": "指标点（二级）", "for": "multi"},
  {"id": "mapcourse1_mapcourse2", "name": "毕业要求（一级）与指标点（二级）的支撑课程", "for": "single"},
  {"id": "course_mapcourse_all1", "name": "课程与毕业要求支撑课程【全部课程】", "for": "single"},
  {"id": "course_mapcourse_onlyreq1", "name": "课程与毕业要求支撑课程【必修课程】", "for": "single"},
  {"id": "course_mapcourse_all2", "name": "课程与指标点支撑课程【全部课程】", "for": "single"},
  {"id": "course_mapcourse_onlyreq2", "name": "课程与指标点支撑课程【必修课程】", "for": "single"}
]>

<#---------- 其他 ---------->

<#-- 欢迎信息 -->
<#macro welcomeInfo>
  <style>
    .flex-fill {
      background: linear-gradient(rgba(255, 255, 255, 1), rgba(0, 128, 255, 0.2));
    }
  </style>
  <div style="height:calc(100vh - 180px); width:100%;" class="d-flex align-items-center">
    <div class="w-100">
      <div class="text-center font-weight-bold mt-6" style="font-size: 2.9rem">
        <span class="text-blue">${appParams.title}</span>
      </div>
      <div class="text-center mt-1 h4 text-muted">
        Majors OBE Mapping Assistant
      </div>
      <div class="d-flex align-items-center justify-content-center mt-6">
        <div class="ml-6"><@p.icon name="ti-user" ext="h2 mb-0 text-primary" /></div>
        <div class="h4 ml-2 mb-0"><span class="text-muted">${loginUser.login_name}</span>【${loginUser.name}】</div>
      </div>
      <#nested>
    </div>
  </div>
</#macro>

<#-- 通用提示信息（广州华商学院：计算机专业） -->
<#assign commonNotice080901>
  <div class="alert alert-warning mt-2 mb-0"><b>注意</b>：计算机科学与技术专业原始数据为权重，转换支撑强度规则如下：<b>0.1</b> 转为 <b>L</b>，<b>0.2</b> 转为 <b>M</b>，<b>0.3</b> 及以上转为 <b>H</b>。转换后出现的缺少 <b>H</b> 强支撑情况仅供参考。</div>
</#assign>

<#-- 问题信息汇总 -->
<#macro noticeInfo id>
<#switch id>
<#on "gout2eobj1-080902">
  <div class="alert alert-warning mt-2 mb-0">
    <b>建议：</b>人才培养方案中未给出支撑程度，目前全部按照 H 强支撑展示，建议根据实际情况对支撑程度进行区分（H/M/L）。
  </div>
</#switch>
</#macro>

<#-- 问题信息汇总（广州华商学院） -->
<#macro noticeInfo_gdhsc id>
<#switch id>
<#-- 开课单位 -->
<#--  <#on "depts">
  <div class="alert alert-danger"><b>问题</b>：开课单位原始信息中，<b>「就业指导中心」</b>与<b>「教师教育学院」</b>代码都是 JY，目前仅保留<b>「就业指导中心」</b>信息，建议代码应确保唯一性，避免歧义。</div>  -->
<#-- 毕业要求及指标点 -->
<#on "gouts-080902">
  <div class="alert alert-danger mt-2 mb-0"><b>问题</b>：指标点 4.3 未提供具体内容，请补充完整。</div>
<#on "gouts-120102">
  <div class="alert alert-warning mt-2 mb-0">
    <b>注意</b>：
    <ul>
      <li>已依据原始材料中对毕业要求分解情况进行了重新编号。</li>
      <li>一般情况下每项毕业要求应分解为 2~4 个能反映其内涵、可测量、可评价的指标点。目前的毕业要求和指标点划分情况颗粒度大小和数量上均不太符合要求。</li>
    </ul>
    以下为一个<b>非工科类</b>毕业要求范例，供参考。
    <ol class="pl-4 mb-0">
      <li><b>思想品德</b>：具有坚定正确的政治方向、良好的思想品德和健全的人格，热爱祖国，热爱人民，拥护中国共产党领导；具有科学精神、人文修养、文化品位、职业素养和进取精神；关心社会问题和国家发展，具有社会责任感，主动参与社会实践；能够传播中华优秀文化；了解国情民情社情，践行社会主义核心价值观。</li>
      <li><b>学科知识</b>：具备系统扎实的基础知识、跨学科知识、专业知识和专业技能；了解本专业及相关学科的历史、现状和前沿动态，掌握本专业的研究思路和研究方法，了解国内外相关政策法规和惯例。</li>
      <li><b>应用能力</b>：具有跨领域知识融通能力，能够综合运用相关知识和技能，分析和解决本专业或相关领域复杂问题，提出相应对策或方案，并对对策和方案的政策依据、社会环境和可能的社会影响进行分析。</li>
      <li><b>创新能力</b>：具有逻辑思维能力、批判精神和反思意识，能够运用本专业的研究思路和方法，组织开展调查与研究，能发现、辨析、总结、评价本专业及相关领域的现象和问题，形成个人判断、见解或对策，具有较强的创新创业能力。</li>
      <li><b>信息能力</b>：能够运用各类信息技术和工具，获取分析相关信息；能够熟练使用各软件和网上办公系统；能够使用相关模型进行分析和判断；能够使用信息技术解决本专业领域实际问题。</li>
      <li><b>沟通表达</b>：具有较强的沟通表达能力，能够使用准确规范的语言文字，逻辑清晰地表达观点；能够与同行和社会公众进行有效沟通，具有一定的宣传和传播能力。</li>
      <li><b>团队合作</b>：具有较强的组织、协调和管理能力，能够与团队成员和谐相处，协作完成复杂任务。</li>
      <li><b>国际视野</b>：理解和尊重世界文化的差异性和多样性，了解国际动态，关注本专业领域的全球重大问题，具有开展国际交流与合作的能力，能够传播中华优秀文化和中国智慧。</li>
      <li><b>学习发展</b>：具有自我规划、自我管理、自主学习和终身学习能力，能够通过不断学习，适应社会和个人高层次、可持续发展的需要。</li>
    </ol>
  </div>
<#-- 毕业要求支撑培养目标 -->
<#--  <#on "gout2eobj1-080906", "gout2eobj1-080906R">
  <div class="alert alert-danger mt-2 mb-0"><b>问题</b>：毕业要求 7 未对任何培养目标提供 H 强支撑，建议修改。</div>  -->
<#on "gout2eobj1-080910T">
  <div class="alert alert-danger mt-2 mb-0"><b>问题</b>：毕业要求 2 和 4 未对任何培养目标提供 H 强支撑，建议修改。</div>
<#-- 指标点支撑培养目标 -->
<#on "gout2eobj2">
  <div class="alert alert-warning mb-0"><b>注意</b>：通常用毕业要求支撑培养目标即可，不必再通过指标点支撑培养目标。如果同时设定了两种支撑关系，需确保支撑关系的合理性。</div>
<#on "gout2eobj2-080717T">
  <div class="alert alert-danger mt-2 mb-0"><b>问题</b>：
    <ul class="mb-0">
      <li>毕业要求 8 并未支撑培养目标 4，指标点 8.1 与培养目标 4 之间的支撑关系需进行确认。</li>
      <li>毕业要求 11 并未支撑培养目标 4，指标点 11.3 与培养目标 4 之间的支撑关系需进行确认。</li>
    </ul>
  </div>
<#-- 课程 -->
<#on "courses-080901H">
  <div class="alert alert-danger mt-2 mb-0"><b>问题</b>：必修课程<b>「编译原理」</b>未支撑任何指标点，需进行确认，可删除课程、调整课程性质为选修或补充支撑关系。</div>
<#on "courses-080906R">
  <div class="alert alert-danger mt-2 mb-0"><b>问题</b>：必修课程<b>「艺术设计基础」</b>未支撑任何指标点，需进行确认，可删除课程、调整课程性质为选修或补充支撑关系。</div>
<#on "courses-080901M">
  <div class="alert alert-danger mt-2 mb-0"><b>问题</b>：2 门必修课程<b>「Human Geography (G3)（人文地理）」「Geography of Pennsylvania (G3)（宾夕法尼亚州地理）」</b>未支撑任何指标点，需进行确认，可删除课程、调整课程性质为选修或补充支撑关系。</div>
<#-- 课程支撑指标点 -->
<#on "course2gout2-080901", "course2gout2-080901R">
  ${commonNotice080901}
  <div class="alert alert-danger mt-2 mb-0"><b>问题</b>：<b>「大学进阶英语」</b>课程仅 <b>A</b> 层学生学习，<b>B</b> 层不学，不建议单独进行映射，可以考虑合并入<b>「大学英语」</b>课程。</div>
<#on "course2gout2-080901G">
  ${commonNotice080901}
  <div class="alert alert-danger mt-2 mb-0"><b>问题</b>：<b>「IELTS」</b>课程仅 <b>AB</b> 层学生学习，<b>C</b> 层不学，不建议单独进行映射，可以考虑合并入<b>「大学英语」</b>课程。</div>
<#on "course2gout2-080901H">
  ${commonNotice080901}
<#on "course2gout2-080901M">
  ${commonNotice080901}
  <div class="alert alert-danger mt-2 mb-0"><b>问题</b>：参与映射的课程<b>「Interpersonal Communication G1（人际沟通 G1）」</b>未提供基本信息，需进行确认，可补充基本信息或删除映射。</div>
<#on "course2gout2-080902">
  <div class="alert alert-danger mt-2 mb-0"><b>问题</b>：
    <ul class="mb-0">
      <li>参与映射的 4 门课程<b>「低代码开发」「Python程序设计」「DevOps实践」「团队激励与沟通」</b>未提供基本信息，需进行确认，可补充基本信息或删除映射。</li>
      <li><b>「大学进阶英语」</b>课程仅 <b>A</b> 层学生学习，<b>B</b> 层不学，不建议单独进行映射，可以考虑合并入<b>「大学英语」</b>课程。</li>
      <li>有 <b>8</b> 项指标点缺失<b>必修</b>课 <b>H</b> 强支撑，有 <b>14</b> 门<b>必修</b>课程未提供 <b>H</b> 强支撑，建议修正。</li>
    </ul>
  </div>
<#on "course2gout2-080906">
  <div class="alert alert-danger mt-2 mb-0"><b>问题</b>：
    <ul class="mb-0">
      <li>指标点 <b>6.2</b> 缺失<b>必修</b>课 <b>H</b> 强支撑，建议修正。</li>
      <li>不同学期的同一门课程建议合并后统一进行映射，例如：<b>「形势与政策」「大学英语」「大学英语视听说」「大学体育」「大学生心理健康教育」「高等数学」</b>等。</li>
      <li><b>「大学进阶英语」</b>课程仅 <b>A</b> 层学生学习，<b>B</b> 层不学，不建议单独进行映射，可以考虑合并入<b>「大学英语」</b>课程。</li>
    </ul>
  </div>
<#on "course2gout2-080906R">
  <div class="alert alert-danger mt-2 mb-0"><b>问题</b>：
    <ul class="mb-0">
      <li>不同学期的同一门课程建议合并后统一进行映射，例如：<b>「形势与政策」「大学英语」「大学英语视听说」「大学体育」「大学生心理健康教育」「高等数学」</b>等。</li>
      <li><b>「大学进阶英语」</b>课程仅 <b>A</b> 层学生学习，<b>B</b> 层不学，不建议单独进行映射，可以考虑合并入<b>「大学英语」</b>课程。</li>
    </ul>
  </div>
<#on "course2gout2-080910T">
  <div class="alert alert-danger mt-2 mb-0"><b>问题</b>：
    <ul class="mb-0">
      <li>指标点 <b>2.1</b> 和 <b>5.3</b> 缺失<b>必修</b>课 <b>H</b> 强支撑，建议修正。</li>
      <li><b>「大学进阶英语」</b>课程仅 <b>A</b> 层学生学习，<b>B</b> 层不学，不建议单独进行映射，可以考虑合并入<b>「大学英语」</b>课程。</li>
    </ul>
  </div>
<#on "course2gout2-120102">
  <div class="alert alert-danger mt-2 mb-0"><b>问题</b>：
    <ul class="mb-0">
      <#--  <li>指标点 <b>5.3</b> 缺失<b>必修</b>课 <b>H</b> 强支撑，建议修正。</li>  -->
      <#--  <li><b>必修</b>课「<b>计算机网络</b>」未提供 <b>H</b> 强支撑，建议修正。</li>  -->
      <li>不同学期的同一门课程建议合并后统一进行映射，例如：<b>「大学生心理健康教育」「高等数学」</b>等。</li>
    </ul>
  </div>
</#switch>
</#macro>
