<#global majid = ctxMap.majid>
<#global major = app.getMajor(majid)>
<#-- 课程支撑毕业要求和指标点设置（通常为二选一） -->
<#global useCourse2Gout1 = false>
<#global useCourse2Gout2 = true>
<#if major.majnote!?contains("课程支撑毕业要求")>
  <#global useCourse2Gout1 = true>
  <#global useCourse2Gout2 = false>
</#if>
<@p.page title="${major.majid}-${major.majname}" extVendors=["datatables"]>

  <#assign pageActions>
    <select class="form-control" onchange="location.href='?majid='+this.value">
      <#list sqlt.sqlQueryForList("obe.getMajors") as item>
        <option value="${item.majid}" ${(majid == item.majid)?then("selected", "")}>${item.majid} - ${item.majname}</option>
      </#list>
    </select>
  </#assign>

  <@p.menu module="major" subNavis=["major.major_detail"] actions=pageActions>
  </@p.menu>

  <#-- 培养目标 -->
  <#assign eduobjs = sqlt.sqlQueryForList("obe.getEduobjs", {"majid": majid})>
  <#assign title><b>培养目标</b><span class="tag tag-rounded ml-4">共 ${eduobjs?size} 项</span></#assign>
  <@p.card title=title>
    <#if eduobjs?size == 0>
      <div class="alert alert-danger text-center font-weight-bold">暂无数据</div>
    <#else>
      <table class="table table-bordered table-vcenter table-sm table-hover">
        <thead>
          <tr>
            <th class="text-center text-nowrap">序号</th>
            <th class="text-center text-nowrap">简称</th>
            <th>详细内容</th>
          </tr>
        </thead>
        <tbody>
          <#list eduobjs as item>
            <tr>
              <td class="text-center text-nowrap">${item.eobjid}</td>
              <td class="text-center font-weight-bold text-nowrap">${item.eobjshortname!}</td>
              <td>${item.eobjname}</td>
            </tr>
          </#list>
        </tbody>
      </table>
    </#if>
  </@p.card>

  <#-- 毕业要求及指标点 -->
  <#global gradouts = sqlt.sqlQueryForList("obe.getGradouts", {"majid": majid})>
  <#global gradouts1 = gradouts?filter(x -> x.goutlevel == 1)>
  <#global gradouts2 = gradouts?filter(x -> x.goutlevel == 2)>
  <#assign invalidGouts = _.listToArray(gradouts?filter(x -> (x.goutshortname!) == '【缺失】' || x.goutname == '【缺失】'), "goutid")>
  <#assign title><b>毕业要求及指标点</b><span class="tag tag-rounded ml-4">共 ${gradouts?size} 项，其中毕业要求 ${gradouts1?size} 项，指标点 ${gradouts2?size} 项</span></#assign>
  <#assign actions>
    <#if gradouts1?size gt 0 && gradouts2?size == 0>
      <span class="tag tag-rounded tag-red mr-2">毕业要求未分解指标点</span>
    </#if>
    <#if invalidGouts?size gt 0>
      <span class="tag tag-rounded tag-red mr-2">有 ${invalidGouts?size} 项毕业要求或指标点信息缺失</span>
    </#if>
  </#assign>
  <@p.card title=title actions=actions>
    <#if gradouts?size == 0>
      <div class="alert alert-danger text-center font-weight-bold">暂无数据</div>
    <#else>
      <table class="table table-bordered table-vcenter table-sm table-hover">
        <thead>
          <tr>
            <th class="text-center text-nowrap">序号</th>
            <th class="text-center text-nowrap">简称</th>
            <th>详细内容</th>
          </tr>
        </thead>
        <tbody>
          <#list gradouts as item>
            <#assign bgClass = "">
            <#if item.goutlevel == 1>
              <#assign bgClass += " font-weight-bold">
              <#if !invalidGouts?seq_contains(item.goutid)>
                <#assign bgClass += " bg-blue-lightest">
              </#if>
            </#if>
            <#if invalidGouts?seq_contains(item.goutid)>
              <#assign bgClass += " bg-red-lighter text-danger">
            </#if>
            <tr class="${bgClass}">
              <td class="text-center text-nowrap">${item.goutid}</td>
              <td class="text-center text-nowrap">${item.goutshortname!}</td>
              <td>${item.goutname}</td>
            </tr>
          </#list>
        </tbody>
      </table>
    </#if>
    <@app.noticeInfo "gouts-${majid}" />
  </@p.card>

  <#-- 毕业要求或指标点支撑培养目标 -->
  <@p.ready>
    var g2eSuppLevelThemes = ${_.toJson(app.suppLevelThemes)?no_esc};
    function toggleG2ESuppLevel(el, goutlevel, supplevel) {
      var theme = g2eSuppLevelThemes[supplevel];
      var objs = $("td.gout2eobj" + goutlevel + supplevel);
      if ($(el).prop("checked")) {
        objs.html(supplevel).addClass(theme.text).addClass(theme.bg);
      } else {
        objs.html("").removeClass(theme.text).removeClass(theme.bg);
      }
    }
  </@p.ready>
  <@gout2eobj 1 />
  <@gout2eobj 2 />

  <#-- 课程 -->
  <#assign viewCourses = sqlt.sqlQueryForList("obe.getViewCourses", {"majid": majid, "withMapping": true, "orderby": "coursetype"})>
  <#assign groupedCourses = viewCourses?filter(x -> x.groupcoursenum gt 0)>
  <#assign title><b>课程</b><span class="tag tag-rounded ml-4">共 ${viewCourses?size} 门<#if groupedCourses?size gt 0>，其中包含 ${groupedCourses?size} 门组合课程</#if></span></#assign>
  <#assign actions>
    <#if useCourse2Gout1>
      <#assign uselessCourses = viewCourses?filter(x -> x.attr == "必修" && x.course2gout1num == 0)>
      <#if uselessCourses?size gt 0>
        <span class="tag tag-rounded tag-red mr-2">有 ${uselessCourses?size} 门必修课程尚未支撑毕业要求</span>
      </#if>
    </#if>
    <#if useCourse2Gout2>
      <#assign uselessCourses = viewCourses?filter(x -> x.attr == "必修" && x.course2gout2num == 0)>
      <#if uselessCourses?size gt 0>
        <span class="tag tag-rounded tag-red mr-2">有 ${uselessCourses?size} 门必修课程尚未支撑指标点</span>
      </#if>
    </#if>
  </#assign>
  <@p.card title=title actions=actions>
    <#if viewCourses?size == 0>
      <div class="alert alert-danger text-center font-weight-bold">暂无数据</div>
    <#else>
      <table class="table table-bordered table-vcenter table-hover table-sm">
        <thead>
          <tr>
            <th class="text-center text-nowrap">序号</th>
            <th class="text-center text-nowrap">课程类别</th>
            <th class="text-center text-nowrap">课程性质</th>
            <th class="text-nowrap">课程名称</th>
            <#if useCourse2Gout1>
              <th class="text-center text-nowrap">支撑毕业要求数量</th>
            </#if>
            <#if useCourse2Gout2>
              <th class="text-center text-nowrap">支撑指标点数量</th>
            </#if>
          </tr>
        </thead>
        <tbody>
          <#list viewCourses as item>
            <#assign warningClass = "">
            <#if item.attr == "必修" && ((useCourse2Gout1 && item.course2gout1num == 0) || (useCourse2Gout2 && item.course2gout2num == 0))>
              <#assign warningClass = "bg-red-lighter">
            </#if>
            <tr class="${warningClass}">
              <td class="text-center text-nowrap">${item?counter}</td>
              <td class="text-center text-nowrap">${item.typename!}</td>
              <td class="text-center text-nowrap">${item.attr}</td>
              <td class="text-nowrap">
                ${item.coursename}
                <#if item.groupcoursenum gt 0>
                  <span class="badge badge-primary px-2" style="cursor: pointer" data-toggle="tooltip" data-placement="right" data-html="true" title="${item.subcourses?eval_json?join('<br>')}">${item.groupcoursenum}</span>
                </#if>
              </td>
              <#if useCourse2Gout1>
                <td class="text-center text-nowrap">
                  <#if item.attr == "必修" && item.mapcourse1num gt 0>
                    <div class="tag tag-success">
                      毕业要求
                      <span class="tag-addon ${(item.course2gout1num gt 0)?then('','tag-gray')}">${item.course2gout1num}</span>
                    </div>
                  <#elseif item.attr == "选修" && item.mapcourse1num gt 0>
                    <div class="tag">
                      毕业要求
                      <span class="tag-addon">${item.course2gout1num}</span>
                    </div>
                  <#elseif item.attr == "必修" && item.mapcourse1num == 0>
                    <span class="tag tag-yellow">尚未支撑</span>
                  </#if>
                </td>
              </#if>
              <#if useCourse2Gout2>
                <td class="text-center text-nowrap">
                  <#if item.attr == "必修" && item.mapcourse2num gt 0>
                    <div class="tag tag-success">
                      指标点
                      <span class="tag-addon ${(item.course2gout2num gt 0)?then('','tag-gray')}">${item.course2gout2num}</span>
                    </div>
                  <#elseif item.attr == "选修" && item.mapcourse2num gt 0>
                    <div class="tag">
                      指标点
                      <span class="tag-addon">${item.course2gout2num}</span>
                    </div>
                  <#elseif item.attr == "必修" && item.mapcourse2num == 0>
                    <span class="tag tag-danger">尚未支撑</span>
                  </#if>
                </td>
              </#if>
            </tr>
          </#list>
        </tbody>
      </table>
    </#if>
    <@app.noticeInfo "courses-${majid}" />
  </@p.card>

  <#-- 课程支撑毕业要求或指标点 -->
  <#import "course2gout_common.ftl" as c2g>
  <@c2g.commons />
  <#-- 同时进行支撑课程信息匹配逻辑太复杂，仅开放课程支撑指标点 -->
  <#if useCourse2Gout1>
    <@c2g.course2gout 1 />
  </#if>
  <#if useCourse2Gout2>
    <@c2g.course2gout 2 />
  </#if>

</@p.page>

<#-- 毕业要求或指标点支撑培养目标 -->
<#macro gout2eobj goutlevel>
  <#local goutTitle = goutlevel?switch(1, "毕业要求", 2, "指标点", "未知")>
  <#local sqlParams = {"majid": majid, "goutlevel": goutlevel}>
  <#local gout2eobjList = sqlt.sqlQueryForList("obe.getGout2Eobj", sqlParams)>
  <#-- 如果没有进行指标点映射则不显示 -->
  <#if goutlevel == 2 && gout2eobjList?size == 0>
    <#return>
  </#if>
  <#-- 仅在存在指标点支撑培养目标映射时，检查两个层级是否存在不合理的映射 -->
  <#if goutlevel == 2>
    <#local checkResultMap = _.listToMap(sqlt.sqlQueryForList("obe.checkGE1&GE2", sqlParams), "mapkey")>
  </#if>
  <#local gout2eobjMap = _.listToMap(gout2eobjList, "mapkey", "supplevel")>
  <#local invalidEobjs = _.listToArray(sqlt.sqlQueryForList("obe.getGout2EobjInvalidEobjs", sqlParams), "eobjid")>
  <#local invalidGouts = _.listToArray(sqlt.sqlQueryForList("obe.getGout2EobjInvalidGouts", sqlParams), "goutid")>
  <#local title><b>${goutTitle}支撑培养目标</b><span class="tag tag-rounded ml-4">共 ${gout2eobjList?size} 项</span></#local>
  <#local actions>
    <#if invalidEobjs?size gt 0>
      <span class="tag tag-rounded tag-red mr-2">有 ${invalidEobjs?size} 项培养目标缺失 H 强支撑</span>
    </#if>
    <#if invalidGouts?size gt 0>
      <span class="tag tag-rounded tag-red mr-2">有 ${invalidGouts?size} 项${goutTitle}未提供 H 强支撑</span>
    </#if>
    <#if (checkResultMap!{})?keys?size gt 0>
      <span class="tag tag-rounded tag-red mr-2">有 ${checkResultMap?keys?size} 组指标点支撑关系不合理</span>
    </#if>
    <#if goutlevel != 1>
      <span class="tag tag-rounded tag-yellow mr-2">非必要</span>
    </#if>
  </#local>
  <@p.card title=title actions=actions>
    <#if gout2eobjList?size == 0>
      <div class="alert alert-danger text-center font-weight-bold">暂无数据</div>
    <#else>
      <div class="mb-2">
        图例及展示开关：
        <#list app.suppLevelThemes?values as item>
          <label class="custom-control custom-checkbox custom-control-inline">
            <input type="checkbox" class="custom-control-input" name="cbsupplevel" onchange="toggleG2ESuppLevel(this, ${goutlevel}, '${item.id}')" checked>
            <span class="custom-control-label font-weight-bold ${item.text} ${item.bg} px-2">${item.id} ${item.name} ${gout2eobjList?filter(x -> x.supplevel == item.id)?size} 项</span>
          </label>
        </#list>
      </div>
      <table class="table table-bordered table-vcenter table-sm">
        <tbody>
          <tr class="text-center text-nowrap bg-light">
            <td class="bg-light font-weight-bold" colspan="2">${goutTitle} \ 培养目标</td>
            <#list eduobjs as eobj>
              <#local bgClass = "">
              <#local tdTitle = "">
              <#if invalidEobjs?seq_contains(eobj.eobjid)>
                <#local bgClass = "bg-red-lighter text-danger">
                <#local tdTitle = "培养目标${eobj.eobjid}缺失 H 强支撑">
              </#if>
              <td class="${bgClass}" title="${tdTitle}"><span class="text-muted">培养目标 ${eobj.eobjid}</span><br><span class="font-weight-bold">${eobj.eobjshortname!}</span></td>
            </#list>
          </tr>
          <#list .vars["gradouts${goutlevel}"] as gout>
            <tr class="text-center text-nowrap">
              <#local bgClass = "bg-light">
              <#local tdTitle = "">
              <#if invalidGouts?seq_contains(gout.goutid)>
                <#local bgClass = "bg-red-lighter text-danger">
                <#local tdTitle = "${goutTitle} ${gout.goutid} 未提供 H 强支撑">
              </#if>
              <#if gout.goutshortname?has_content>
                <td class="text-right border-right-0 text-muted ${bgClass}" title="${tdTitle}">${goutTitle} ${gout.goutid}</td>
                <td class="text-left border-left-0 font-weight-bold ${bgClass}" title="${tdTitle}">${gout.goutshortname}</td>
              <#else>
                <td class="text-muted ${bgClass}" colspan="2" title="${tdTitle}">${goutTitle} ${gout.goutid}</td>
              </#if>
              <#list eduobjs as eobj>
                <#local supplevel = gout2eobjMap[gout.goutid + '-' + eobj.eobjid]!>
                <#local goutid1 = gout.goutid?keep_before(".")>
                <#local invalidResult = (checkResultMap[goutid1 + '-' + eobj.eobjid])!>
                <#if invalidResult?has_content>
                  <#local supplevelArray = ["", "L", "M", "H"]>
                  <#switch invalidResult.result>
                    <#on "NO_GE1">
                      <#local invalidInfo = "毕业要求 ${invalidResult.goutid}【并未】支撑培养目标 ${invalidResult.eobjid}">
                    <#on "NO_GE2">
                      <#local invalidInfo = "毕业要求 ${invalidResult.goutid}【已经】支撑培养目标 ${invalidResult.eobjid}">
                    <#on "LESS_GE2">
                      <#local invalidInfo = "指标点整体【低于】毕业要求 ${invalidResult.goutid} 支撑度【${supplevelArray[invalidResult.ge1_supplevel_int]}】">
                    <#on "MORE_GE2">
                      <#local invalidInfo = "指标点整体【超过】毕业要求 ${invalidResult.goutid} 支撑度【${supplevelArray[invalidResult.ge1_supplevel_int]}】">
                    <#default>
                      <#local invalidInfo = invalidResult>
                  </#switch>
                  <#-- 映射存在问题 -->
                  <td class="h5 bg-red-lighter text-danger" title="${invalidInfo}">
                    <span class="text-danger">${supplevel}</span>
                  </td>
                <#else>
                  <#-- 映射正常 -->
                  <td class="h5 ${app.getSuppLevelTheme(supplevel)} ${supplevel?has_content?then('gout2eobj' + goutlevel + supplevel, '')}">
                    ${supplevel}
                  </td>
                </#if>
              </#list>
            </tr>
          </#list>
        </tbody>
      </table>
    </#if>
    <@app.noticeInfo id="gout2eobj${goutlevel}" />
    <@app.noticeInfo id="gout2eobj${goutlevel}-${majid}" />
  </@p.card>
</#macro>
