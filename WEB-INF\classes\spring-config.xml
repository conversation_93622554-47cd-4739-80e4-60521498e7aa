<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:aop="http://www.springframework.org/schema/aop" xmlns:context="http://www.springframework.org/schema/context"
  xmlns:jee="http://www.springframework.org/schema/jee" xmlns:tx="http://www.springframework.org/schema/tx"
  xmlns:mvc="http://www.springframework.org/schema/mvc" xmlns:util="http://www.springframework.org/schema/util"
  xmlns:cache="http://www.springframework.org/schema/cache"
  xsi:schemaLocation="
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
		http://www.springframework.org/schema/jee http://www.springframework.org/schema/jee/spring-jee.xsd
		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
		http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc.xsd
		http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd
		http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache.xsd">

  <!-- DataSource (HikariCP or jndi) -->
  <bean id="dataSource" class="com.zaxxer.hikari.HikariDataSource" destroy-method="close">
    <property name="driverClassName" value="org.postgresql.Driver" />
    <property name="jdbcUrl" value="*************************************" />
    <property name="username" value="obe_cqnu" />
    <property name="password" value="obe.Mapping" />
    <property name="maximumPoolSize" value="2" />
  </bean>

  <!-- <jee:jndi-lookup id="dataSource" jndi-name="" /> -->

  <!-- WebInterceptor For FreeMarker Servlet -->
  <bean id="webInterceptor" class="neo.lib.web.WebInterceptor">
    <!-- Web 字符集 -->
    <property name="encoding" value="UTF-8" />
    <!-- [可选] 设定多个需登录后访问的路径，逗号分隔。 如系统无需登录，则取消此参数。 -->
    <property name="protectedPaths" value="/" />
    <!-- [可选] 设定无需登录即可访问的多个路径，逗号分隔。用于设定 protectedPaths 中的例外情况，比 protectedPaths 优先级高。 -->
    <property name="publicPaths" value="/vendors/" />
    <!-- [可选] 设定不能直接访问的多个路径，逗号分隔 -->
    <property name="privatePaths" value="/modules/" />
    <!-- [可选] 在当前登录用户 Map 中获取角色信息所用的 Key，默认为 role -->
    <!-- <property name="roleKey" value="role" /> -->
    <!-- [可选] 对路径增加访问角色限制，路径名需唯一，角色可以用逗号分隔 -->
    <!--
    <property name="rolePaths">
      <map>
        <entry key="/home/" value="ADMIN, USER" />
        <entry key="/manage/" value="ADMIN" />
      </map>
    </property>
    -->
    <!-- [可选] 设定根路径，要求所有 FTL 文件必须在此路径下方可访问，通常用于遗留系统改造 -->
    <!-- <property name="rootPath" value="/neoftl/" /> -->
  </bean>

  <!-- SqlManager -->
  <bean id="sqlManager" class="neo.lib.sql.SqlManager" init-method="init">
    <!-- 数据库方言，全称，小写 -->
    <property name="dialect" value="postgresql" />
    <!-- [可选] SQL 文件路径 -->
    <!-- <property name="sqlPath" value="/WEB-INF/sqls" /> -->
    <!-- [可选] SQL 文件扩展名 -->
    <!-- <property name="sqlExtName" value=".sql" /> -->
    <!-- [可选] SQL 文件字符集 -->
    <!-- <property name="encoding" value="UTF-8" /> -->
    <!-- [可选] 默认分页尺寸 -->
    <!-- <property name="defaultPageSize" value="10" /> -->
    <!-- [可选] 最大分页尺寸 -->
    <!-- <property name="maxPageSize" value="1000" /> -->
    <!-- [可选] FreeMarker 兼容版本 -->
    <property name="freemarkerVersion" value="2.3.34" />
    <!-- [可选] FreeMarker 引擎相关设置 -->
    <property name="freemarkerSettings">
      <value>
        auto_include=_common.autoInclude
        auto_import=_common.autoImport as m
        template_update_delay=3
        lazy_auto_imports=true
        lazy_imports=true
        number_format=0.##
      </value>
    </property>
  </bean>

  <!-- CacheManager -->
  <bean id="cacheManager" class="neo.lib.web.CacheManager">
    <!-- [可选] 缓存文件字符集 -->
    <!-- <property name="encoding" value="UTF-8" /> -->
    <!-- [可选] 缓存文件路径 -->
    <!-- <property name="cachePath" value="/files_cache/" /> -->
    <!-- [可选] 缓存文件扩展名 -->
    <!-- <property name="cacheExtName" value=".cache" /> -->
    <!-- [可选] 每组最大缓存数量 -->
    <!-- <property name="maxSize" value="1000" /> -->
  </bean>

</beans>
