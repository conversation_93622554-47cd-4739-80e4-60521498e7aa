<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://xmlns.jcp.org/xml/ns/javaee"
  xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee http://xmlns.jcp.org/xml/ns/javaee/web-app_4_0.xsd"
  version="4.0">

  <!-- Spring Params -->
  <context-param>
    <param-name>contextConfigLocation</param-name>
    <param-value>classpath:spring-*.xml</param-value>
  </context-param>

  <!-- Application Params : 系统首页 -->
  <context-param>
    <param-name>homepage</param-name>
    <param-value>/index.ftl</param-value>
  </context-param>

  <!-- Application Params : 用户登录页（全开放式应用可删除此参数） -->
  <context-param>
    <param-name>loginUrl</param-name>
    <param-value>/user/login.ftl</param-value>
  </context-param>

  <!-- Neo Web Context Listener -->
  <listener>
    <listener-class>neo.lib.web.ContextListener</listener-class>
  </listener>

  <!-- Spring Context Loader -->
  <listener>
    <listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
  </listener>

  <!-- FreeMarker Servlet -->
  <servlet>
    <servlet-name>freemarker</servlet-name>
    <servlet-class>neo.lib.web.WebFreemarkerServlet</servlet-class>

    <!-- FreemarkerServlet settings: -->
    <init-param>
      <param-name>TemplatePath</param-name>
      <param-value>/</param-value>
    </init-param>
    <init-param>
      <param-name>NoCache</param-name>
      <param-value>true</param-value>
    </init-param>
    <init-param>
      <param-name>ResponseCharacterEncoding</param-name>
      <param-value>fromTemplate</param-value>
    </init-param>
    <init-param>
      <param-name>ExceptionOnMissingTemplate</param-name>
      <param-value>false</param-value>
    </init-param>

    <!-- FreeMarker engine settings: -->
    <init-param>
      <param-name>incompatible_improvements</param-name>
      <param-value>2.3.34</param-value>
    </init-param>
    <init-param>
      <param-name>output_format</param-name>
      <param-value>HTMLOutputFormat</param-value>
    </init-param>
    <init-param>
      <param-name>template_exception_handler</param-name>
      <!-- Use "html_debug" during development! -->
      <param-value>rethrow</param-value>
    </init-param>
    <init-param>
      <param-name>template_update_delay</param-name>
      <!-- Use 0 during development! Consider what value you need otherwise. -->
      <!-- In Production: <param-value>30 s</param-value> -->
      <param-value>0</param-value>
    </init-param>
    <init-param>
      <param-name>default_encoding</param-name>
      <!-- The encoding of the template files: -->
      <param-value>UTF-8</param-value>
    </init-param>
    <init-param>
      <param-name>output_encoding</param-name>
      <param-value>UTF-8</param-value>
    </init-param>
    <init-param>
      <param-name>locale</param-name>
      <param-value>zh_CN</param-value>
    </init-param>
    <init-param>
      <param-name>number_format</param-name>
      <param-value>0.##</param-value>
    </init-param>
    <init-param>
      <param-name>date_format</param-name>
      <param-value>yyyy-MM-dd</param-value>
    </init-param>
    <init-param>
      <param-name>time_format</param-name>
      <param-value>HH:mm:ss</param-value>
    </init-param>
    <init-param>
      <param-name>datetime_format</param-name>
      <param-value>yyyy-MM-dd HH:mm:ss</param-value>
    </init-param>
    <init-param>
      <param-name>boolean_format</param-name>
      <param-value>c</param-value>
    </init-param>
    <init-param>
      <param-name>localized_lookup</param-name>
      <param-value>false</param-value>
    </init-param>
    <init-param>
      <param-name>lazy_auto_imports</param-name>
      <param-value>true</param-value>
    </init-param>
    <init-param>
      <param-name>lazy_imports</param-name>
      <param-value>true</param-value>
    </init-param>
    <init-param>
      <param-name>auto_include</param-name>
      <param-value>/common/config.ftl</param-value>
    </init-param>
    <init-param>
      <param-name>auto_import</param-name>
      <param-value>
        /common/macro_page.ftl as p,
        /common/macro_form.ftl as f,
        /common/function_util.ftl as _,
        /common/macro_app.ftl as app
      </param-value>
    </init-param>
    <load-on-startup>1</load-on-startup>
  </servlet>

  <!-- Mapping FTL -->
  <servlet-mapping>
    <servlet-name>freemarker</servlet-name>
    <url-pattern>*.ftl</url-pattern>
  </servlet-mapping>

  <!-- Welcome File -->
  <welcome-file-list>
    <welcome-file>index.html</welcome-file>
    <welcome-file>index.jsp</welcome-file>
  </welcome-file-list>

</web-app>
