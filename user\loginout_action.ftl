<#ftl output_format="JSON" attributes={"public":true}>

<#-- 登录 -->
<#macro login>
  <#if ctxMap.login_name == "obe" && ctxMap.password == "obe123">
    <#local user = {"id":0, "login_name":"obe", "name":"OBE 学院", "password":_.md5(ctxMap.password), "status":1, "role":"ADMIN"}>
  </#if>
  <#if !user?has_content>
    <#assign actionResult = _.actionResult(1001, "用户名不存在")>
  <#elseif _.md5(ctxMap.password) != user.password>
    <#assign actionResult = _.actionResult(1002, "密码错误")>
  <#elseif user.status == 0>
    <#assign actionResult = _.actionResult(1003, "用户帐号已停用")>
  <#else>
    ${request.getSession().setAttribute(_.getWebConstants().LOGIN_USER, user)}
    <#assign actionResult = _.actionResult(0, "登录成功")>
  </#if>
</#macro>

<#-- 登出 -->
<#macro logout>
  ${request.getSession().invalidate()}
</#macro>

<#-- 执行 Action -->
<@p.renderActionResult />
