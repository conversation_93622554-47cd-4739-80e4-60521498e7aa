.webuploader-container {
  position: relative;
}

.webuploader-element-invisible {
  position: absolute !important;
  clip: rect(1px 1px 1px 1px);
  /* IE6, IE7 */
  clip: rect(1px, 1px, 1px, 1px);
}

.webuploader-pick {
  position: relative;
  display: inline-block;
  cursor: pointer;
  background: #467fcf;
  font-weight: 600;
  font-size: .8125rem;
  min-width: 2.375rem;
  letter-spacing: .03em;
  padding: .375rem .75rem;
  border: 1px solid transparent;
  color: #fff;
  text-align: center;
  line-height: 1.84615385;
  border-radius: 3px;
  overflow: hidden;
  transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
}

.webuploader-pick-hover {
  background: #316cbe;
}

.webuploader-pick-disable {
  opacity: 0.6;
  pointer-events: none;
}
