<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>鱼骨图测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        #container {
            width: 100%;
            height: 600px;
            border: 1px solid #ccc;
        }
        h1 {
            text-align: center;
            color: #333;
        }
    </style>
</head>
<body>
    <h1>鱼骨图 - 带鱼尾节点</h1>
    <div id="container"></div>

    <!-- 引入G6库 -->
    <script src="https://unpkg.com/@antv/g@6/dist/index.umd.min.js"></script>
    <script src="https://unpkg.com/@antv/g6@5/dist/g6.min.js"></script>

    <script>
        // 将fishbone.js的内容嵌入到这里进行测试
        const { Text } = G;
        const { BaseTransform, ExtensionCategory, Graph, register, treeToGraphData } = G6;

        const data = {
          id: "Product Profitability\nBelow Expectations",
          children: [
            {
              id: "Problem Description",
              children: [
                { id: "Brand Sales Volume" },
                { id: "Market Capacity" },
                { id: "Brand Market Share" },
                { id: "Total Contribution Margin" },
              ],
            },
            {
              id: "Brand Positioning",
              children: [
                { id: "Packaging" },
                { id: "Brand Name" },
                { id: "Selling Price" },
                { id: "Product Specifications" },
              ],
            },
            {
              id: "Distribution Channels",
              children: [
                { id: "Region" },
                { id: "Channel" },
                { id: "Customer Type" },
                { id: "Sales Personnel Coverage" },
              ],
            },
            {
              id: "Market Awareness",
              children: [
                { id: "Regional Weighting" },
                { id: "Media Mix" },
                { id: "Advertising Investment" },
                { id: "Quality Perception" },
              ],
            },
            {
              id: "Trial Purchase",
              children: [
                { id: "In-store Display" },
                { id: "Promotion Type" },
                { id: "Timing of Promotion" },
                { id: "Supply Assurance" },
              ],
            },
            {
              id: "Repeat Purchase",
              children: [
                { id: "Consumer Profile" },
                { id: "Usage Occasion" },
                { id: "Frequency of Use" },
                { id: "Returns Due to Product Issues" },
              ],
            },
          ],
          // 添加鱼尾节点
          tail: {
            id: "Root Cause\nAnalysis",
          },
        };

        let textShape;
        const measureText = (style) => {
          if (!textShape) textShape = new Text({ style });
          textShape.attr(style);
          return textShape.getBBox().width;
        };

        class AssignColorByBranch extends BaseTransform {
          static defaultOptions = {
            colors: [
              "#1783FF",
              "#F08F56",
              "#D580FF",
              "#00C9C9",
              "#7863FF",
              "#DB9D0D",
              "#60C42D",
              "#FF80CA",
              "#2491B3",
              "#17C76F",
            ],
          };

          constructor(context, options) {
            super(
              context,
              Object.assign({}, AssignColorByBranch.defaultOptions, options)
            );
          }

          beforeDraw(input) {
            const nodes = this.context.model.getNodeData();

            if (nodes.length === 0) return input;

            let colorIndex = 0;
            const dfs = (nodeId, color) => {
              const node = nodes.find((datum) => datum.id == nodeId);
              if (!node) return;

              node.style ||= {};
              node.style.color =
                color || this.options.colors[colorIndex++ % this.options.colors.length];
              node.children?.forEach((childId) => dfs(childId, node.style?.color));
            };

            nodes
              .filter((node) => node.depth === 1)
              .forEach((rootNode) => dfs(rootNode.id));

            return input;
          }
        }

        class ArrangeEdgeZIndex extends BaseTransform {
          beforeDraw(input) {
            const { model } = this.context;
            const { nodes, edges } = model.getData();

            const oneLevelNodes = nodes.filter((node) => node.depth === 1);
            const oneLevelNodeIds = oneLevelNodes.map((node) => node.id);

            edges.forEach((edge) => {
              if (oneLevelNodeIds.includes(edge.target)) {
                edge.style ||= {};
                edge.style.zIndex =
                  oneLevelNodes.length -
                  oneLevelNodes.findIndex((node) => node.id === edge.target);
              }
            });

            return input;
          }
        }

        class PositionTailNode extends BaseTransform {
          afterLayout(input) {
            const { model } = this.context;
            const nodes = model.getNodeData();

            // 找到鱼头节点（depth为0且不是鱼尾）
            const headNode = nodes.find(node =>
              node.depth === 0 && !node.id.includes("Root Cause")
            );

            // 找到鱼尾节点
            const tailNode = nodes.find(node =>
              node.id.includes("Root Cause") || node.data?.isTail
            );

            if (headNode && tailNode) {
              // 将鱼尾节点放置在鱼头的左侧，完全相同的Y坐标（主线上）
              const headX = headNode.data?.x || 0;
              const headY = headNode.data?.y || 0;

              // 直接设置位置属性
              tailNode.data = tailNode.data || {};
              tailNode.data.x = headX - 500; // 增加距离，确保不重叠
              tailNode.data.y = headY; // 完全相同的Y坐标，确保在主线上

              // 同时设置节点的x, y属性（某些版本的G6可能需要这个）
              tailNode.x = headX - 500;
              tailNode.y = headY;

              console.log('Head position:', headX, headY);
              console.log('Tail position:', tailNode.data.x, tailNode.data.y);
            }

            return input;
          }
        }

        register(
          ExtensionCategory.TRANSFORM,
          "assign-color-by-branch",
          AssignColorByBranch
        );
        register(
          ExtensionCategory.TRANSFORM,
          "arrange-edge-z-index",
          ArrangeEdgeZIndex
        );
        register(
          ExtensionCategory.TRANSFORM,
          "position-tail-node",
          PositionTailNode
        );

        const getNodeSize = (id, depth, isTail = false) => {
          const FONT_FAMILY = "system-ui, sans-serif";
          // 鱼尾节点使用与鱼头相同的尺寸
          if (isTail || depth === 0) {
            return [
              measureText({
                text: id,
                fontSize: 24,
                fontWeight: "bold",
                fontFamily: FONT_FAMILY,
              }) + 80,
              90,
            ];
          }
          return depth === 1
            ? [
                measureText({ text: id, fontSize: 18, fontFamily: FONT_FAMILY }) + 50,
                42,
              ]
            : [2, 30];
        };

        // 自定义数据转换函数，处理鱼尾节点
        const customTreeToGraphData = (treeData) => {
          const graphData = treeToGraphData(treeData);

          // 如果有鱼尾节点，直接添加到图数据中
          if (treeData.tail) {
            const tailNode = {
              id: treeData.tail.id,
              depth: 0, // 与鱼头相同深度，确保在主线上
              data: { ...treeData.tail, isTail: true },
            };

            graphData.nodes.push(tailNode);

            // 添加从鱼头到鱼尾的连接边（主线延伸）
            const headNode = graphData.nodes.find(node =>
              node.depth === 0 && node.id !== treeData.tail.id
            );
            if (headNode) {
              graphData.edges.push({
                id: `${headNode.id}-${tailNode.id}`,
                source: headNode.id,
                target: tailNode.id,
                data: { isMainLine: true },
              });
            }
          }

          return graphData;
        };

        const graph = new Graph({
          container: 'container',
          autoFit: "view",
          padding: 30,
          data: customTreeToGraphData(data),
          node: {
            type: "rect",
            style: (d) => {
              // 检查是否为鱼尾节点
              const isTail = d.id === "Root Cause\nAnalysis" || d.data?.isTail;

              const style = {
                radius: 8,
                size: getNodeSize(d.id, d.depth, isTail),
                labelText: d.id,
                labelPlacement: "left",
                labelFontFamily: "Gill Sans",
              };

              // 鱼头节点或鱼尾节点使用相同样式
              if (d.depth === 0 || (d.depth === 1 && isTail)) {
                Object.assign(style, {
                  fill: "#EFF0F0",
                  labelFill: "#262626",
                  labelFontWeight: "bold",
                  labelFontSize: 24,
                  labelOffsetY: 3,
                  labelPlacement: "center",
                  labelLineHeight: 32,
                  stroke: "#262626",
                  lineWidth: 2,
                });
              } else if (d.depth === 1) {
                Object.assign(style, {
                  labelFontSize: 18,
                  labelFill: "#252525",
                  labelFillOpacity: 0.9,
                  labelOffsetY: 5,
                  labelPlacement: "center",
                  labelFontWeight: 600,
                  fill: d.style?.color,
                  fillOpacity: 0.6,
                  lineWidth: 2,
                  stroke: "#252525",
                });
              } else {
                Object.assign(style, {
                  fill: "transparent",
                  labelFontSize: 16,
                  labeFill: "#262626",
                });
              }
              return style;
            },
          },
          edge: {
            type: "polyline",
            style: (d) => {
              // 主线（包括到鱼尾的连接）使用更粗的线条
              const isMainLine = d.data?.isMainLine ||
                                (d.source && d.target &&
                                 (d.source.includes("Product Profitability") || d.target.includes("Root Cause")));

              return {
                lineWidth: isMainLine ? 5 : 3,
                stroke: "#252525",
              };
            },
          },
          layout: {
            type: "fishbone",
            direction: "RL",
            hGap: 40,
            vGap: 60,
            getRibSep: (node) => {
              console.log(node);
              return node.depth === 0 ? 0 : -50;
            },
          },
          behaviors: ["zoom-canvas", "drag-canvas"],
          transforms: ["assign-color-by-branch", "arrange-edge-z-index", "position-tail-node"],
        });

        graph.render();

        // 在渲染完成后手动调整鱼尾节点位置
        graph.on('afterlayout', () => {
          const nodes = graph.getAllNodesData();
          const headNode = nodes.find(node =>
            node.data.depth === 0 && !node.id.includes("Root Cause")
          );
          const tailNode = nodes.find(node =>
            node.id.includes("Root Cause")
          );

          if (headNode && tailNode) {
            // 获取鱼头节点的实际位置
            const headPosition = graph.getNodeData(headNode.id);

            // 更新鱼尾节点位置，确保在同一水平线上
            graph.updateNodeData(tailNode.id, {
              ...tailNode,
              data: {
                ...tailNode.data,
                x: headPosition.data.x - 500,
                y: headPosition.data.y
              }
            });

            console.log('Manually positioned tail node');
          }
        });
    </script>
</body>
</html>
