/* 获取通用 buttons 配置 */
function getDtButtons(options) {
  var defaults = {
    paging: true, /* 默认启用分页参数按钮 */
    colvis: false, /* 默认停用列显示/隐藏按钮 */
    export: true, /* 默认启用导出按钮 */
    filename: '导出文件' /* 导出文件前缀 */
  }
  var settings = $.extend({}, defaults, options);
  var buttons = [];
  if (settings.paging) {
    buttons.push({
      extend: 'pageLength',
      fade: 0
    });
  }
  if (settings.colvis) {
    buttons.push({
      extend: 'colvis',
      fade: 0
    });
  }
  if (settings.export) {
    buttons.push({
      extend: 'excelHtml5',
      filename: function() {
        return settings.filename + "_" + (new Date()).format("yyyyMMdd");
      },
      title: null,
      exportOptions: {
        columns: ':visible'
      }
    });
  }
  return buttons;
}

/* 设置默认参数 */
$.extend(true, $.fn.dataTable.defaults, {
  "dom": 'Bfrtip',
  "colReorder": false,
  "lengthMenu": [
    [10, 25, 50, 100, -1],
    [10, 25, 50, 100, "全部"]
  ],
  buttons: getDtButtons(),
  "language": {
    "processing": "处理中...",
    "lengthMenu": "_MENU_ 条记录 / 页",
    "zeroRecords": "未检索到满足条件的记录",
    "info": "第 _START_ 至 _END_ 条记录， 共 _TOTAL_ 条",
    "infoEmpty": "第 0 至 _END_ 条记录， 共 _TOTAL_ 条",
    "infoFiltered": "（从 _MAX_ 条记录中检索）",
    "infoPostFix": "",
    "search": "快速检索：",
    "url": "",
    "emptyTable": "无数据",
    "loadingRecords": "载入中……",
    "paginate": {
      "first": "首页",
      "previous": "上页",
      "next": "下页",
      "last": "末页"
    },
    "aria": {
      "sortAscending": ": 以升序排列此列",
      "sortDescending": ": 以降序排列此列"
    },
    "buttons": {
      "excel": '<i class="fe fe-download"></i> 导出 Excel',
      "colvis": '<i class="fe fe-check-square"></i> 显示 / 隐藏列',
      "pageLength": {
        "-1": "单页显示全部记录",
        "_": '<i class="fe fe-layers"></i> 每页 %d 条记录'
      }
    }
  }
});

/* 修改字符串排序方式，使其支持中文按拼音排序 */
$.extend($.fn.dataTable.ext.type.order, {
  "string-pre": null,

  "string-asc": function (x, y) {
    if (/[a-zA-Z0-9]/.test(x) || /[a-zA-Z0-9]/.test(y)) {
      return String(x).localeCompare(String(y), 'en');
    } else {
      return String(x).localeCompare(String(y), 'zh');
    }
  },

  "string-desc": function (x, y) {
    if (/[a-zA-Z0-9]/.test(x) || /[a-zA-Z0-9]/.test(y)) {
      return String(y).localeCompare(String(x), 'en');
    } else {
      return String(y).localeCompare(String(x), 'zh');
    }
  }
});
