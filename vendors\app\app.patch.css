/* 页面说明文字中的 ul 去掉底边距 */
#pageIntro ul {
  margin-bottom: 0;
}

/* 表格表头背景色调整 */
.table thead th {
  background: #f6f8fb;
}

/* 表单只读项图标为禁用 */
.form-control[readonly] {
  cursor: not-allowed
}

/* ----- 侧边导航菜单相关内容 ----- */

/* 右侧页面主体内容空出菜单位置 */
.page-wrapper {
  display: flex;
  flex-direction: column;
  margin-left: 8rem;
  flex: 1 1 auto !important;
}

/* 重新定义流式容器内边距 */
.page-wrapper .container-fluid {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
@media (min-width: 1200px) and (max-width: 1458px) {
  .page-wrapper .container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

/* 侧边导航顶层元素 */
.navbar-vertical {
  width: 8rem;
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  z-index: 1030;
  align-items: flex-start;
  transition: transform 0.3s;
  overflow-y: auto;
  padding: 0;
  -webkit-box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
  box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
}
.navbar-vertical.navbar-dark {
  background: #182433;
}
.navbar-vertical.navbar-light {
  background: #fff;
}

/* 侧边导航宽度充足时样式 */
@media (min-width: 1458px) {
  .navbar-vertical {
    width: 15rem;
  }
  .page-wrapper {
    margin-left: 15rem;
  }
  .navbar-vertical .short-copyright {
    display: none;
  }
}

/* 侧边导航宽度不足时样式 */
@media (max-width: 1458px) {
  .navbar-vertical .header-brand-img {
    margin-right: 0;
  }
  .navbar-vertical .app-title {
    display: none;
  }
  .navbar-vertical .header-brand {
    margin-right: 0 !important;
  }
  .navbar-vertical .full-copyright {
    display: none;
  }
}

/* 滚动条的宽度 */
.navbar-vertical::-webkit-scrollbar {
  width: 12px;
}
/* 滚动条轨道的颜色 */
.navbar-vertical.navbar-dark::-webkit-scrollbar-track {
  background: #182433;
}
/* 滚动条的颜色 */
.navbar-vertical.navbar-dark::-webkit-scrollbar-thumb {
  background: #384250;
}
.navbar-vertical.navbar-light::-webkit-scrollbar-thumb {
  background: #eee;
}

/* 侧边导航容器 */
.navbar-vertical > [class^="container"] {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  min-height: 100%;
  justify-content: flex-start;
  padding: 0;
}

/* 顶部系统标题 */
.navbar-vertical.navbar-dark .header-brand {
  color: #fff;
}
.navbar-vertical.navbar-light .header-brand {
  color: #066fd1;
}

/* 菜单容器 */
.navbar-vertical .navbar-collapse {
  display: flex !important;
  flex-direction: column;
  align-items: stretch;
}

/* 菜单列表 */
.navbar-vertical .navbar-nav .nav-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: stretch;
  padding: 0;
  position: relative;
}

/* 菜单内链接 */
.navbar-vertical .nav-item .nav-link {
  padding: 0.5rem;
  padding-left: 0.75rem;
}

.navbar-vertical.navbar-light .navbar-nav .active>.nav-link {
  color: #066fd1;
  background-color: #e6f1fa;
}

/* 下拉内容可换行 */
.navbar-vertical .navbar-collapse .dropdown-toggle {
  white-space: normal;
}

/* 下拉箭头靠右 */
.navbar-vertical .navbar-collapse .dropdown-toggle:after {
  margin-left: auto;
  margin-right: 0.3rem;
}

/* 一级导航图标 */
.navbar-vertical .nav-link-icon {
  font-size: 1.25rem;
  margin-right: 0.5rem;
}

/* 左侧激活状态条 */
.navbar-vertical .navbar-collapse .nav-item.active:after {
  border-left-width: 4px !important;
  border-bottom-width: 0;
  left: 0;
  right: auto;
  top: 0;
  bottom: 0;
  content: "";
  position: absolute;
  border: 0 solid #066fd1;
  height: 100%;
  z-index: 1001;
}

/* 子菜单容器 */
.navbar-vertical .dropdown-menu {
  background-color: transparent;
  padding: 0;
  margin: 0;
  min-width: 0;
  border: 0;
  box-shadow: none;
}

/* 子菜单功能项 */
.navbar-vertical .dropdown-item {
  padding: 0.5rem 0 0.5rem 2.5rem;
  white-space: normal;
  line-height: 1.2;
}
.navbar-vertical.navbar-dark .dropdown-item {
  color: rgba(255, 255, 255, 0.5);
}
.navbar-vertical.navbar-light .dropdown-item {
  color: rgba(0, 0, 0, .5);
}

/* 子菜单分隔线 */
.navbar-vertical .dropdown-divider {
  margin-left: 2.5rem;
}
.navbar-vertical.navbar-dark .dropdown-divider {
  border-top: 1px solid rgba(255, 255, 255, 0.25);
}
.navbar-vertical.navbar-light .dropdown-divider {
  border-top: 1px solid rgba(0, 40, 100, 0.25);
}

/* 子菜单小标题 */
.navbar-vertical .dropdown-header {
  padding-left: 2.5rem;
}
.navbar-vertical.navbar-light .dropdown-header {
  color: #6c7a91;
}
.navbar-vertical.navbar-dark .dropdown-header {
  color: rgba(255, 255, 255, 0.75);
}

/* 子菜单 hover 效果 */
.navbar-vertical.navbar-dark .dropdown-item:hover,
.navbar-vertical.navbar-dark .dropdown-item:focus {
  color: rgba(255, 255, 255, 0.75);
  text-decoration: none;
  background-color: #232b39;
}
.navbar-vertical.navbar-light .dropdown-item:hover,
.navbar-vertical.navbar-light .dropdown-item:focus {
  color: #16181b;
  text-decoration: none;
  background-color: #f3f3f3;
}

/* 子菜单激活状态下效果 */
.navbar-vertical.navbar-dark .dropdown-item.active,
.navbar-vertical.navbar-dark .dropdown-item.active:hover,
.navbar-vertical.navbar-dark .dropdown-item.active:focus {
  color: #fff;
  text-decoration: none;
  background-color: #066fd1;
}

.navbar-vertical.navbar-light .dropdown-item.active,
.navbar-vertical.navbar-light .dropdown-item.active:hover,
.navbar-vertical.navbar-light .dropdown-item.active:focus {
  color: #066fd1;
  text-decoration: none;
  background-color: #e6f1fa;
}

/* 隐藏子菜单顶部导航时的箭头 */
.navbar-vertical .dropdown-menu-arrow:before, .navbar-vertical .dropdown-menu-arrow:after {
  display: none;
}

/* 菜单内版权信息 */
.navbar-vertical .navbar-copyright {
  padding: 1.35rem 0;
}
.navbar-vertical.navbar-dark .navbar-copyright {
  border-top: 1px dashed rgba(255, 255, 255, 0.25);
  color: rgba(255, 255, 255, .5);
}
.navbar-vertical.navbar-light .navbar-copyright {
  border-top: 1px dashed rgba(0, 40, 100, 0.25);
  color: #6c7a91;
}

/* selectpicker 下拉框增加边框 */
.form-control.bootstrap-select .btn-light {
  border: 1px solid rgba(0, 40, 100, .12)
}

/* 排序项鼠标样式 */
.sortable-list .list-group-item, .sortable-list .list-group-item * {
  cursor: move
}

/* Card 右侧下拉菜单不隐藏箭头 */
.card-options .dropdown-toggle::after {
  display: inline-block;
}

/* 选项组颜色补丁 */
.selectgroup-input:checked+.selectgroup-button.checked-green {
  border-color: #2fb344;
  z-index: 1;
  color: #2fb344;
  background: #eaf7ec;
}
.selectgroup-input:checked+.selectgroup-button.checked-yellow {
  border-color: #f59f00;
  z-index: 1;
  color: #f59f00;
  background: #fef5e6;
}
.selectgroup-input:checked+.selectgroup-button.checked-red {
  border-color: #d63939;
  z-index: 1;
  color: #d63939;
  background: #fbebeb;
}

/* 多选时取消后的聚焦应该为灰色 */
.selectgroup-input:not(:checked):focus+.selectgroup-button {
  border-color: rgba(0, 40, 100, .12);
  z-index: 2;
  color: #6c7a91;
  box-shadow: 0 0 0 2px rgba(0, 40, 100, .12)
}

/* .btn-outline-yellow 补丁，比 orange 颜色偏黄一些 */
.btn-outline-yellow {
  color: #f59f00;
  border-color: #f59f00
}
.btn-outline-yellow:hover {
  color: #fff;
  background-color: #f59f00;
  border-color: #f59f00
}
.btn-outline-yellow.focus,
.btn-outline-yellow:focus {
  box-shadow: 0 0 0 2px rgba(247, 173, 38, .5)
}
.btn-outline-yellow.disabled,
.btn-outline-yellow:disabled {
  color: #f59f00;
  background-color: transparent
}
.btn-outline-yellow:not(:disabled):not(.disabled).active,
.btn-outline-yellow:not(:disabled):not(.disabled):active,
.show>.btn-outline-yellow.dropdown-toggle {
  color: #fff;
  background-color: #f59f00;
  border-color: #f59f00
}
.btn-outline-yellow:not(:disabled):not(.disabled).active:focus,
.btn-outline-yellow:not(:disabled):not(.disabled):active:focus,
.show>.btn-outline-yellow.dropdown-toggle:focus {
  box-shadow: 0 0 0 2px rgba(247, 173, 38, .5)
}

/* Tabler 样式补丁 */
.card-title a.btn-outline-primary {
  color: #066fd1;
}
.card-title a.btn-outline-primary:hover {
  color: #fff;
}

/* Button 增加 Flat 样式 */
.btn-flat {
  border: 0 !important;
}