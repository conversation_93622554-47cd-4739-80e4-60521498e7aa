!function(t,e){(function r(v,n,h,u){var i=!!(v.Worker&&v.Blob&&v.Promise&&v.OffscreenCanvas&&v.OffscreenCanvasRenderingContext2D&&v.HTMLCanvasElement&&v.HTMLCanvasElement.prototype.transferControlToOffscreen&&v.URL&&v.URL.createObjectURL);var w=typeof Path2D==="function"&&typeof DOMMatrix==="function";var t=function(){if(!v.OffscreenCanvas){return false}var t=new OffscreenCanvas(1,1);var e=t.getContext("2d");e.fillRect(0,0,1,1);var a=t.transferToImageBitmap();try{e.createPattern(a,"no-repeat")}catch(t){return false}return true}();function o(){}function m(t){var e=n.exports.Promise;var a=e!==void 0?e:v.Promise;if(typeof a==="function"){return new a(t)}t(o,o);return null}var x=function(r,n){return{transform:function(t){if(r){return t}if(n.has(t)){return n.get(t)}var e=new OffscreenCanvas(t.width,t.height);var a=e.getContext("2d");a.drawImage(t,0,0);n.set(t,e);return e},clear:function(){n.clear()}}}(t,new Map);var d=function(){var n=Math.floor(1e3/60);var t,e;var i={};var o=0;if(typeof requestAnimationFrame==="function"&&typeof cancelAnimationFrame==="function"){t=function(a){var r=Math.random();i[r]=requestAnimationFrame(function t(e){if(o===e||o+n-1<e){o=e;delete i[r];a()}else{i[r]=requestAnimationFrame(t)}});return r};e=function(t){if(i[t]){cancelAnimationFrame(i[t])}}}else{t=function(t){return setTimeout(t,n)};e=function(t){return clearTimeout(t)}}return{frame:t,cancel:e}}();var g=function(){var e;var s;var c={};function a(o){function l(t,e){o.postMessage({options:t||{},callback:e})}o.init=function t(e){var a=e.transferControlToOffscreen();o.postMessage({canvas:a},[a])};o.fire=function t(r,e,n){if(s){l(r,null);return s}var i=Math.random().toString(36).slice(2);s=m(function(e){function a(t){if(t.data.callback!==i){return}delete c[i];o.removeEventListener("message",a);s=null;x.clear();n();e()}o.addEventListener("message",a);l(r,i);c[i]=a.bind(null,{data:{callback:i}})});return s};o.reset=function t(){o.postMessage({reset:true});for(var e in c){c[e]();delete c[e]}}}return function(){if(e){return e}if(!h&&i){var t=["var CONFETTI, SIZE = {}, module = {};","("+r.toString()+")(this, module, true, SIZE);","onmessage = function(msg) {","  if (msg.data.options) {","    CONFETTI(msg.data.options).then(function () {","      if (msg.data.callback) {","        postMessage({ callback: msg.data.callback });","      }","    });","  } else if (msg.data.reset) {","    CONFETTI && CONFETTI.reset();","  } else if (msg.data.resize) {","    SIZE.width = msg.data.resize.width;","    SIZE.height = msg.data.resize.height;","  } else if (msg.data.canvas) {","    SIZE.width = msg.data.canvas.width;","    SIZE.height = msg.data.canvas.height;","    CONFETTI = module.exports.create(msg.data.canvas);","  }","}"].join("\n");try{e=new Worker(URL.createObjectURL(new Blob([t])))}catch(t){typeof console!==undefined&&typeof console.warn==="function"?console.warn("🎊 Could not load worker",t):null;return null}a(e)}return e}}();var l={particleCount:50,angle:90,spread:45,startVelocity:45,decay:.9,gravity:1,drift:0,ticks:200,x:.5,y:.5,shapes:["square","circle"],zIndex:100,colors:["#26ccff","#a25afd","#ff5e7e","#88ff5a","#fcff42","#ffa62d","#ff36ff"],disableForReducedMotion:false,scalar:1};function s(t,e){return e?e(t):t}function c(t){return!(t===null||t===undefined)}function C(t,e,a){return s(t&&c(t[e])?t[e]:l[e],a)}function I(t){return t<0?0:Math.floor(t)}function T(t,e){return Math.floor(Math.random()*(e-t))+t}function a(t){return parseInt(t,16)}function E(t){return t.map(e)}function e(t){var e=String(t).replace(/[^0-9a-f]/gi,"");if(e.length<6){e=e[0]+e[0]+e[1]+e[1]+e[2]+e[2]}return{r:a(e.substring(0,2)),g:a(e.substring(2,4)),b:a(e.substring(4,6))}}function P(t){var e=C(t,"origin",Object);e.x=C(e,"x",Number);e.y=C(e,"y",Number);return e}function p(t){t.width=document.documentElement.clientWidth;t.height=document.documentElement.clientHeight}function b(t){var e=t.getBoundingClientRect();t.width=e.width;t.height=e.height}function M(t){var e=document.createElement("canvas");e.style.position="fixed";e.style.top="0px";e.style.left="0px";e.style.pointerEvents="none";e.style.zIndex=t;return e}function S(t,e,a,r,n,i,o,l,s){t.save();t.translate(e,a);t.rotate(i);t.scale(r,n);t.arc(0,0,1,o,l,s);t.restore()}function O(t){var e=t.angle*(Math.PI/180);var a=t.spread*(Math.PI/180);return{x:t.x,y:t.y,wobble:Math.random()*10,wobbleSpeed:Math.min(.11,Math.random()*.1+.05),velocity:t.startVelocity*.5+Math.random()*t.startVelocity,angle2D:-e+(.5*a-Math.random()*a),tiltAngle:(Math.random()*(.75-.25)+.25)*Math.PI,color:t.color,shape:t.shape,tick:0,totalTicks:t.ticks,decay:t.decay,drift:t.drift,random:Math.random()+2,tiltSin:0,tiltCos:0,wobbleX:0,wobbleY:0,gravity:t.gravity*3,ovalScalar:.6,scalar:t.scalar,flat:t.flat}}function y(t,e){e.x+=Math.cos(e.angle2D)*e.velocity+e.drift;e.y+=Math.sin(e.angle2D)*e.velocity+e.gravity;e.velocity*=e.decay;if(e.flat){e.wobble=0;e.wobbleX=e.x+10*e.scalar;e.wobbleY=e.y+10*e.scalar;e.tiltSin=0;e.tiltCos=0;e.random=1}else{e.wobble+=e.wobbleSpeed;e.wobbleX=e.x+10*e.scalar*Math.cos(e.wobble);e.wobbleY=e.y+10*e.scalar*Math.sin(e.wobble);e.tiltAngle+=.1;e.tiltSin=Math.sin(e.tiltAngle);e.tiltCos=Math.cos(e.tiltAngle);e.random=Math.random()+2}var a=e.tick++/e.totalTicks;var r=e.x+e.random*e.tiltCos;var n=e.y+e.random*e.tiltSin;var i=e.wobbleX+e.random*e.tiltCos;var o=e.wobbleY+e.random*e.tiltSin;t.fillStyle="rgba("+e.color.r+", "+e.color.g+", "+e.color.b+", "+(1-a)+")";t.beginPath();if(w&&e.shape.type==="path"&&typeof e.shape.path==="string"&&Array.isArray(e.shape.matrix)){t.fill(A(e.shape.path,e.shape.matrix,e.x,e.y,Math.abs(i-r)*.1,Math.abs(o-n)*.1,Math.PI/10*e.wobble))}else if(e.shape.type==="bitmap"){var l=Math.PI/10*e.wobble;var s=Math.abs(i-r)*.1;var c=Math.abs(o-n)*.1;var f=e.shape.bitmap.width*e.scalar;var h=e.shape.bitmap.height*e.scalar;var u=new DOMMatrix([Math.cos(l)*s,Math.sin(l)*s,-Math.sin(l)*c,Math.cos(l)*c,e.x,e.y]);u.multiplySelf(new DOMMatrix(e.shape.matrix));var d=t.createPattern(x.transform(e.shape.bitmap),"no-repeat");d.setTransform(u);t.globalAlpha=1-a;t.fillStyle=d;t.fillRect(e.x-f/2,e.y-h/2,f,h);t.globalAlpha=1}else if(e.shape==="circle"){t.ellipse?t.ellipse(e.x,e.y,Math.abs(i-r)*e.ovalScalar,Math.abs(o-n)*e.ovalScalar,Math.PI/10*e.wobble,0,2*Math.PI):S(t,e.x,e.y,Math.abs(i-r)*e.ovalScalar,Math.abs(o-n)*e.ovalScalar,Math.PI/10*e.wobble,0,2*Math.PI)}else if(e.shape==="star"){var v=Math.PI/2*3;var m=4*e.scalar;var g=8*e.scalar;var p=e.x;var b=e.y;var M=5;var y=Math.PI/M;while(M--){p=e.x+Math.cos(v)*g;b=e.y+Math.sin(v)*g;t.lineTo(p,b);v+=y;p=e.x+Math.cos(v)*m;b=e.y+Math.sin(v)*m;t.lineTo(p,b);v+=y}}else{t.moveTo(Math.floor(e.x),Math.floor(e.y));t.lineTo(Math.floor(e.wobbleX),Math.floor(n));t.lineTo(Math.floor(i),Math.floor(o));t.lineTo(Math.floor(r),Math.floor(e.wobbleY))}t.closePath();t.fill();return e.tick<e.totalTicks}function k(r,t,n,i,o){var l=t.slice();var s=r.getContext("2d");var c;var f;var e=m(function(t){function e(){c=f=null;s.clearRect(0,0,i.width,i.height);x.clear();o();t()}function a(){if(h&&!(i.width===u.width&&i.height===u.height)){i.width=r.width=u.width;i.height=r.height=u.height}if(!i.width&&!i.height){n(r);i.width=r.width;i.height=r.height}s.clearRect(0,0,i.width,i.height);l=l.filter(function(t){return y(s,t)});if(l.length){c=d.frame(a)}else{e()}}c=d.frame(a);f=e});return{addFettis:function(t){l=l.concat(t);return e},canvas:r,promise:e,reset:function(){if(c){d.cancel(c)}if(f){f()}}}}function f(y,t){var o=!y;var l=!!C(t||{},"resize");var s=false;var c=C(t,"disableForReducedMotion",Boolean);var e=i&&!!C(t||{},"useWorker");var f=e?g():null;var w=o?p:b;var h=y&&f?!!y.__confetti_initialized:false;var u=typeof matchMedia==="function"&&matchMedia("(prefers-reduced-motion)").matches;var x;function d(t,e,a){var r=C(t,"particleCount",I);var n=C(t,"angle",Number);var i=C(t,"spread",Number);var o=C(t,"startVelocity",Number);var l=C(t,"decay",Number);var s=C(t,"gravity",Number);var c=C(t,"drift",Number);var f=C(t,"colors",E);var h=C(t,"ticks",Number);var u=C(t,"shapes");var d=C(t,"scalar");var v=!!C(t,"flat");var m=P(t);var g=r;var p=[];var b=y.width*m.x;var M=y.height*m.y;while(g--){p.push(O({x:b,y:M,angle:n,spread:i,startVelocity:o,color:f[g%f.length],shape:u[T(0,u.length)],ticks:h,decay:l,gravity:s,drift:c,scalar:d,flat:v}))}if(x){return x.addFettis(p)}x=k(y,p,w,e,a);return x.promise}function a(t){var e=c||C(t,"disableForReducedMotion",Boolean);var a=C(t,"zIndex",Number);if(e&&u){return m(function(t){t()})}if(o&&x){y=x.canvas}else if(o&&!y){y=M(a);document.body.appendChild(y)}if(l&&!h){w(y)}var r={width:y.width,height:y.height};if(f&&!h){f.init(y)}h=true;if(f){y.__confetti_initialized=true}function n(){if(f){var t={getBoundingClientRect:function(){if(!o){return y.getBoundingClientRect()}}};w(t);f.postMessage({resize:{width:t.width,height:t.height}});return}r.width=r.height=null}function i(){x=null;if(l){s=false;v.removeEventListener("resize",n)}if(o&&y){if(document.body.contains(y)){document.body.removeChild(y)}y=null;h=false}}if(l&&!s){s=true;v.addEventListener("resize",n,false)}if(f){return f.fire(t,r,i)}return d(t,r,i)}a.reset=function(){if(f){f.reset()}if(x){x.reset()}};return a}var B;function F(){if(!B){B=f(null,{useWorker:true,resize:true})}return B}function A(t,e,a,r,n,i,o){var l=new Path2D(t);var s=new Path2D;s.addPath(l,new DOMMatrix(e));var c=new Path2D;c.addPath(s,new DOMMatrix([Math.cos(o)*n,Math.sin(o)*n,-Math.sin(o)*i,Math.cos(o)*i,a,r]));return c}function R(t){if(!w){throw new Error("path confetti are not supported in this browser")}var e,a;if(typeof t==="string"){e=t}else{e=t.path;a=t.matrix}var r=new Path2D(e);var n=document.createElement("canvas");var i=n.getContext("2d");if(!a){var o=1e3;var l=o;var s=o;var c=0;var f=0;var h,u;for(var d=0;d<o;d+=2){for(var v=0;v<o;v+=2){if(i.isPointInPath(r,d,v,"nonzero")){l=Math.min(l,d);s=Math.min(s,v);c=Math.max(c,d);f=Math.max(f,v)}}}h=c-l;u=f-s;var m=10;var g=Math.min(m/h,m/u);a=[g,0,0,g,-Math.round(h/2+l)*g,-Math.round(u/2+s)*g]}return{type:"path",path:e,matrix:a}}function N(t){var e,a=1,r="#000000",n='"Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji", "EmojiOne Color", "Android Emoji", "Twemoji Mozilla", "system emoji", sans-serif';if(typeof t==="string"){e=t}else{e=t.text;a="scalar"in t?t.scalar:a;n="fontFamily"in t?t.fontFamily:n;r="color"in t?t.color:r}var i=10*a;var o=""+i+"px "+n;var l=new OffscreenCanvas(i,i);var s=l.getContext("2d");s.font=o;var c=s.measureText(e);var f=Math.ceil(c.actualBoundingBoxRight+c.actualBoundingBoxLeft);var h=Math.ceil(c.actualBoundingBoxAscent+c.actualBoundingBoxDescent);var u=2;var d=c.actualBoundingBoxLeft+u;var v=c.actualBoundingBoxAscent+u;f+=u+u;h+=u+u;l=new OffscreenCanvas(f,h);s=l.getContext("2d");s.font=o;s.fillStyle=r;s.fillText(e,d,v);var m=1/a;return{type:"bitmap",bitmap:l.transferToImageBitmap(),matrix:[m,0,0,m,-f*m/2,-h*m/2]}}n.exports=function(){return F().apply(this,arguments)};n.exports.reset=function(){F().reset()};n.exports.create=f;n.exports.shapeFromPath=R;n.exports.shapeFromText=N})(function(){if(typeof t!=="undefined"){return t}if(typeof self!=="undefined"){return self}return this||{}}(),e,false);t.confetti=e.exports}(window,{});