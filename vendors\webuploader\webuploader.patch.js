// 初始化上传按钮
function initUploader(base, selector, server, extensions, fileSizeMB, timestamp, sign, extFormData, afterUpload) {
  WebUploader.create({
    // swf文件路径
    swf: base + '/vendors/webuploader/Uploader.swf',
    // 文件接收服务端
    server: server,
    // 文件选择最大数量
    fileNumLimit: 1,
    // 自动上传
    auto: true,

    // 文件选择器
    pick: {
      id: selector,
      multiple: false
    },

    // 允许的文件类型
    accept: {
      extensions: extensions
    },

    // 单文件尺寸限制
    fileSingleSizeLimit: 1024 * 1024 * fileSizeMB,

    // 签名信息
    formData: $.extend({
      timestamp: timestamp,
      sign: sign
    }, extFormData)
  }).on('fileQueued', function (file) {
    // 文件进队列
    $(selector).find(".fileBox").append('<span id="' + file.id + '"></span>');
  }).on('uploadProgress', function (file, percentage) {
    // 显示上传进度
    var $li = $('#' + file.id);
    var $percent = $li.find('.upprogress');
    if (!$percent.length) {
      $percent = $('<span class="upprogress" style="color: #FF0"></span>').appendTo($li);
    }
    $percent.text(Math.round(percentage * 100) + '%');
  }).on("error", function (type) {
    // 错误提示
    var errInfo = "文件上传出现异常，请重试。";
    if (type.indexOf("EXCEED_SIZE") > 0) {
      errInfo = "文件大小超出 " + fileSizeMB + " MB 限制。";
    } else if (type === "Q_TYPE_DENIED") {
      errInfo = "文件类型不正确，请选择 " + extensions + " 格式文件。";
    } else if (type === "Q_EXCEED_NUM_LIMIT") {
      errInfo = "请等待当前文件完成上传。";
    }
    PNotify.error(errInfo);
  }).on('uploadSuccess', function (file, response) {
    // 上传成功
    if (response.error) {
      PNotify.error(response.error);
    } else {
      afterUpload(JSON.parse(response._raw), selector);
    }
  }).on('uploadError', function (file) {
    // 上传出错
    PNotify.error("文件上传出错，请重试。");
  }).on('uploadComplete', function (file) {
    // 上传完毕
    $('#' + file.id).remove();
    this.reset();
  });

}

// 默认上传完毕后操作
function addUploadFileToList(fileInfo, selector) {
  if ($(selector).prev("ul").length === 0) {
    $(selector).before('<ul class="list-group mb-2"></ul>');
    if ($(selector).prev("ul").sortable) {
      $(selector).prev("ul").sortable(); // 可排序时激活排序
    }
  }
  $(selector).prev("ul").append('<li class="list-group-item"><a href="' + fileInfo.link + '" target="_blank">' + fileInfo.srcname + '</a> <small>（大小：' + fileInfo.formatsize + '）</small><input type="hidden" name="filename[]" value="' + fileInfo.servername + '"><input type="hidden" name="filesrcname[]" value="' + fileInfo.srcname + '"><input type="hidden" name="filesize[]" value="' + fileInfo.size + '"><button class="close" onclick="$(this).parent().remove()"></button></li>');
}
