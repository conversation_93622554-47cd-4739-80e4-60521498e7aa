<#-- 表单容器 -->
<#macro form id tag="form" layout=true validate=true ajaxSubmit=true ajaxBeforeFunc="null" ajaxAfterFunc="null" attrs...>
  <#-- 表单主体 -->
  <${tag}
   id="${id}"
   <@p.renderTagAttrs attrs=attrs />
  >
  <#-- 是否使用 Grid 布局 -->
  <#if layout>
    <div class="row">
      <#nested>
    </div>
  <#else>
    <#nested>
  </#if>
  </${tag}>
  <#-- 表单验证 -->
  <#if validate>
    <@p.ready>
      $("#${id}").validate({
        <#if ajaxSubmit>
          submitHandler: function(form) {
            formAjaxSubmit(form, ${ajaxBeforeFunc?no_esc}, ${ajaxAfterFunc?no_esc});
          }
        </#if>
      });
    </@p.ready>
  <#-- 表单不验证但执行 ajaxSubmit -->
  <#elseif ajaxSubmit>
    <@p.ready>
      $("#${id}").submit(function() {
        formAjaxSubmit(this, ${ajaxBeforeFunc?no_esc}, ${ajaxAfterFunc?no_esc});
        return false;
      });
    </@p.ready>
  </#if>
</#macro>

<#-- 组件 -->
<#macro ctrl tag label="" class="form-control" groupClass="form-group" required=false intro="" size=12 validators={} data={} value="" valueType="string" inline=false attrs...>
  <#-- 定义标签体内容 -->
  <#local nestedContent><#nested></#local>
  <#-- 定义 validators 内容 -->
  <#local validateTags>
    <#if (tag == "select") || ("checkbox" == attrs.type!)>
      <#local cusValMsg = true>
      <#local valMsgs = {
        "maxlength": "最多可以选择 {0} 项",
        "minlength": "至少需要选择 {0} 项",
        "rangelength": "请选择 {0} 到 {1} 项"
      }>
    <#else>
      <#local cusValMsg = false>
    </#if>
    <#list validators as vkey, vvalue>
      data-rule-${vkey} = "${vvalue?string}"
      <#if cusValMsg && valMsgs?keys?seq_contains(vkey)>
        data-msg-${vkey} = "${(valMsgs[vkey])!}"
      </#if>
    </#list>
  </#local>
  <#-- 定义控件组 -->
  <div class="${groupClass} col-${size}">
  <#-- 表单项名称 -->
  <#if label?has_content>
    <label class="form-label">${label}<#if required><span class="form-required">*</span></#if></label>
  </#if>
  <#-- 表单项控件 -->
  <#if ["radio", "checkbox"]?seq_contains(attrs.type!)><#-- radio 和 checkbox -->
    <#if ["array", "keyvalue"]?seq_contains(valueType) && value?has_content>
      <#local tValue = value?eval>
    <#else>
      <#local tValue = value>
    </#if>
    <#local randomNum = _.getRandomStringUtils().randomNumeric(6)>
    <#local cusClassTag = randomNum + "-" + (attrs.name!)>
    <#local type = attrs.type>
    <#if data?is_sequence><#-- sequence -->
      <#local isListData = true>
      <#local tData = data>
    <#else><#-- hash -->
      <#local isListData = false>
      <#local tData = data?keys>
    </#if>
    <#if inline><div class="pt-2"></#if>
    <#list tData as item>
      <#if isListData>
        <#if item?is_string || item?is_number>
          <#local optKey = item?string>
          <#local optText = item?string>
          <#local optExtend = false>
        <#else>
          <#local optKey = (item[item?keys[0]])!>
          <#local optText = (item[item?keys[1]])!>
          <#local optExtend = (item[item?keys[2]])!false>
        </#if>
      <#else>
        <#local optKey = item!>
        <#local optText = data[item]!>
        <#local optExtend = false>
      </#if>
      <label class="custom-control custom-${type} <#if inline>custom-control-inline</#if>">
        <${tag}
          value="${optKey}"
          class="custom-control-input cus-ctl-${cusClassTag} cus-ctl-${cusClassTag}-${item?index}"
          data-cus-ctl-txt="cus-txt-${cusClassTag}-${item?index}"
          <#if required>required="required"</#if>
          <#if (tValue?is_hash_ex && tValue[optKey]??) || (tValue?is_sequence && tValue?seq_contains(optKey)) || (tValue?is_string && optKey == tValue) || (tValue?is_number && optKey == tValue?string)>checked="checked"</#if>
          <#list attrs?keys as key>
            <#if key != "name" || valueType == "string">
              ${key}="${attrs[key]}"
            </#if>
          </#list>
          <#if ["array", "keyvalue"]?seq_contains(valueType)>
            name="${attrs.name!}_${randomNum}"
          </#if>
          ${validateTags}>
        <span class='custom-control-label ${(!optExtend)?string("cus-txt-" + cusClassTag + "-" + item?index,"")}'>${optText!}</span>
        <#if optExtend?? && optExtend>
          <input type="text" class="form-control cus-txt-${cusClassTag} cus-txt-${cusClassTag}-${item?index}"
            <#if (tValue?is_hash_ex && tValue[optKey]??)>
              <#local extendValue = tValue[optKey]>
              <#if extendValue?index_of(optText + "：") == 0>
                <#local extendValue = extendValue?replace(optText + "：", "", "f")>
              <#elseif extendValue?index_of(optText) == 0>
                <#local extendValue = extendValue?replace(optText, "", "f")>
              </#if>
              value="${extendValue}"
            </#if>
            data-srctext="${optText!}"
          >
          <@p.ready>
            if (!$(".cus-ctl-${cusClassTag}-${item?index}").is(":checked") && $(".cus-txt-${cusClassTag}-${item?index}").is("input")) {
              $(".cus-txt-${cusClassTag}-${item?index}").hide();
            }
            $(".cus-ctl-${cusClassTag}").click(function () {
              $.each($(".cus-ctl-${cusClassTag}"), function() {
                if ($("." + $(this).data("cus-ctl-txt")).is("input")) {
                  if ($(this).is(":checked")) {
                    $("." + $(this).data("cus-ctl-txt")).show();
                  } else {
                    $("." + $(this).data("cus-ctl-txt")).hide();
                  }
                }
              });
            });
          </@p.ready>
        </#if>
      </label>
    </#list>
    <#if inline></div></#if>
    <#if ["array", "keyvalue"]?seq_contains(valueType)>
      <input type="hidden" name="${attrs.name!}" class="cus-jsontxt-${cusClassTag}" value="${value}">
        <@p.ready>
          <#if valueType == "keyvalue">
            $(".cus-ctl-${cusClassTag}").click(function() {
              var jsonObj = _.reduce($(".cus-ctl-${cusClassTag}:checked"), function(result, item) {
                var $textItem = $("." + $(item).data("cus-ctl-txt"));
                var srcText = $textItem.data("srctext") || $textItem.text();
                var inputText = $textItem.val();
                result[$(item).val()] =  inputText ? (srcText + "：" + inputText) : srcText;
                return result;
              }, {});
              var jsonText = JSON.stringify(jsonObj);
              $(".cus-jsontxt-${cusClassTag}").val((jsonText == "{}" ? "" : jsonText));
            });
            $(".cus-txt-${cusClassTag}").change(function() {
              var jsonObj = _.reduce($(".cus-ctl-${cusClassTag}:checked"), function(result, item) {
                var $textItem = $("." + $(item).data("cus-ctl-txt"));
                var srcText = $textItem.data("srctext") || $textItem.text();
                var inputText = $textItem.val();
                result[$(item).val()] =  inputText ? (srcText + "：" + inputText) : srcText;
                return result;
              }, {});
              var jsonText = JSON.stringify(jsonObj);
              $(".cus-jsontxt-${cusClassTag}").val((jsonText == "{}" ? "" : jsonText));
            });
          <#elseif valueType == "array">
            $(".cus-ctl-${cusClassTag}").click(function() {
              var jsonArray = _.reduce($(".cus-ctl-${cusClassTag}:checked"), function(result, item) {
                result.push($(item).val());
                return result;
              }, []);
              var jsonText = JSON.stringify(jsonArray);
              $(".cus-jsontxt-${cusClassTag}").val((jsonText == "[]" ? "" : jsonText));
            });
            $(".cus-txt-${cusClassTag}").change(function() {
              var jsonArray = _.reduce($(".cus-ctl-${cusClassTag}:checked"), function(result, item) {
                result.push($(item).val());
                return result;
              }, []);
              var jsonText = JSON.stringify(jsonArray);
              $(".cus-jsontxt-${cusClassTag}").val((jsonText == "[]" ? "" : jsonText));
            });
          </#if>
        </@p.ready>
    </#if>
  <#elseif tag == "select" && data?has_content><#-- select -->
    <#if ["array", "keyvalue"]?seq_contains(valueType) && value?has_content>
      <#local tValue = value?eval>
    <#else>
      <#local tValue = value>
    </#if>
    <#local randomNum = _.getRandomStringUtils().randomNumeric(6)>
    <#local cusClassTag = randomNum + "-" + (attrs.name!)>
    <#local jsonSelectDefault = "">
    <${tag}
      class="${class} cus-ctl-${cusClassTag}"
      <#if required>required="required"</#if>
      <#list attrs?keys as key>
        <#if key != "name" || valueType == "string">
          ${key}="${attrs[key]}"
        </#if>
      </#list>
      <#if ["array", "keyvalue"]?seq_contains(valueType)>
        name="${attrs.name!}_${randomNum}"
      </#if>
      ${validateTags}>
      <#if !attrs.multiple??>
        <option value=""></option>
      </#if>
      <#if data?is_sequence><#-- sequence -->
        <#local isListData = true>
        <#local tData = data>
      <#else><#-- hash -->
        <#local isListData = false>
        <#local tData = data?keys>
      </#if>
      <#list tData as item>
        <#if isListData>
          <#if item?is_string || item?is_number>
            <#local optKey = item?string>
            <#local optText = item?string>
          <#else>
            <#local optKey = (item[item?keys[0]])!>
            <#local optText = (item[item?keys[1]])!>
          </#if>
        <#else>
          <#local optKey = item!>
          <#local optText = data[item]!>
        </#if>
        <#if required && !value?has_content && !jsonSelectDefault?has_content && valueType=="keyvalue">
          <#local jsonSelectDefault>{"${optKey}":"${optText}"}</#local>
        <#elseif required && !value?has_content && !jsonSelectDefault?has_content && valueType=="array">
          <#local jsonSelectDefault>["${optKey}"]</#local>
        </#if>
        <option value="${optKey}" ${((tValue?is_hash_ex && tValue[optKey]??) || (tValue?is_sequence && tValue?seq_contains(optKey)) || (tValue?is_string && optKey == tValue) || (tValue?is_number && optKey == tValue?string))?string("selected", "")}>${optText!}</option>
      </#list>
    </${tag}>
    <#if ["array", "keyvalue"]?seq_contains(valueType)>
      <#if value?has_content>
        <input type="hidden" name="${attrs.name!}" class="cus-jsontxt-${cusClassTag}" value="${value}">
      <#else>
        <input type="hidden" name="${attrs.name!}" class="cus-jsontxt-${cusClassTag}" value='${jsonSelectDefault!}'>
      </#if>
        <@p.ready>
          <#if valueType == "keyvalue">
            $(".cus-ctl-${cusClassTag}").change(function() {
              var jsonObj = _.reduce($(".cus-ctl-${cusClassTag} option:selected"), function(result, item) {
                result[$(item).val()] =  $(item).text();
                return result;
              }, {});
              var jsonText = JSON.stringify(jsonObj);
              $(".cus-jsontxt-${cusClassTag}").val((jsonText == "{}" ? "" : jsonText));
            });
          <#elseif valueType == "array">
            $(".cus-ctl-${cusClassTag}").change(function() {
              var jsonArray = _.reduce($(".cus-ctl-${cusClassTag} option:selected"), function(result, item) {
                result.push($(item).val());
                return result;
              }, []);
              var jsonText = JSON.stringify(jsonArray);
              $(".cus-jsontxt-${cusClassTag}").val((jsonText == "[]" ? "" : jsonText));
            });
          </#if>
        </@p.ready>
    </#if>
  <#else><#-- 其他类型控件 -->
    <${tag}
      class="${class}"
      <#if required && tag!="div">required="required"</#if>
      <@p.renderTagAttrs attrs=attrs />
      ${validateTags}
      <#if nestedContent?has_content>
        >${nestedContent}</${tag}>
      <#elseif ["textarea", "div", "select"]?seq_contains(tag)>
        >${value}</${tag}>
      <#else>
        value="${value}">
      </#if>
  </#if>
  <#-- 表单项说明 -->
  <#if intro?has_content>
    <small class="form-text text-muted">${intro}</small>
  </#if>
  </div>
</#macro>
