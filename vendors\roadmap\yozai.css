/* Generated By cn-font-split@4.12.0 https://www.npmjs.com/package/@konghayao/cn-font-split
CreateTime: Mon, 25 Mar 2024 03:05:30 GMT;
Origin File Name Table:
copyright: Copyright (C) 2020 LXGW. Original Font Data Copyright (C) Y.OzVox.
fontFamily: Yozai
fontSubfamily: Bold
uniqueID: Yozai Bold:Version 0.85
fullName: Yozai Bold
version: Version 0.85;October 1, 2020;FontCreator 13.0.0.2613 64-bit
postScriptName: Yozai-Bold
trademark: Y.OzVox
description: This font was created using FontCreator  from High-Logic.com
license: This Font Software is licensed under the SIL Open Font License, Version 1.1. This Font Software is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the SIL Open Font License for the specific language, permissions and limitations governing your use of this Font Software.
licenseURL: http://scripts.sil.org/OFL
preferredFamily:
preferredSubfamily:
 */

@font-face {font-family: "<PERSON><PERSON>";src:local("<PERSON><PERSON>"),url("../fonts/yozai/f703ff042e18fcc40d3b5a41d265f600.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+29d4b,U+29ddb,U+29e15,U+29e3d,U+29e49,U+29e8a,U+29ec4,U+29edb,U+29ee9,U+29f7e,U+29f83,U+29f8c,U+29fce,U+29fd7,U+2a01a,U+2a02f,U+2a082,U+2a0f9,U+2a190,U+2a38c,U+2a437,U+2a5f1,U+2a602,U+2a61a,U+2a6b2,U+2a7dd,U+2a8fb,U+2a917,U+2aa30,U+2aa36,U+2aa58,U+2afa2,U+2b127-2b128,U+2b137-2b138,U+2b1ed,U+2b300,U+2b363,U+2b36f,U+2b372,U+2b37d,U+2b404,U+2b410,U+2b413,U+2b461,U+2b4e7,U+2b4ef,U+2b4f6,U+2b4f9,U+2b50d-2b50e,U+2b536,U+2b5ae-2b5af,U+2b5b3,U+2b5e7,U+2b5f4,U+2b61c-2b61d,U+2b626-2b628,U+2b62a,U+2b62c,U+2b695-2b696,U+2b6ad,U+2b6ed,U+2b7a9,U+2b7c5,U+2b7e6,U+2b7f7,U+2b7f9,U+2b7fc,U+2b806,U+2b80a,U+2b81c,U+2b8b8,U+2bac7,U+2bb5f,U+2bb62,U+2bb7c,U+2bb83,U+2bc1b,U+2bd77,U+2bd87,U+2bdf7,U+2be29,U+2c029-2c02a,U+2c0a9,U+2c0ca,U+2c1d5,U+2c1d9,U+2c1f9,U+2c27c,U+2c288,U+2c2a4,U+2c317,U+2c35b,U+2c361,U+2c364,U+2c488,U+2c494,U+2c497,U+2c542,U+2c613,U+2c618,U+2c621,U+2c629,U+2c62b-2c62d,U+2c62f,U+2c642,U+2c64a-2c64b,U+2c72c,U+2c72f,U+2c79f,U+2c7c1,U+2c7fd,U+2c8d9,U+2c8de,U+2c8e1,U+2c8f3,U+2c907,U+2c90a,U+2c91d,U+2ca02,U+2ca0e,U+2ca7d,U+2caa9,U+2cb29,U+2cb2d-2cb2e,U+2cb31,U+2cb38-2cb39,U+2cb3b,U+2cb3f,U+2cb41,U+2cb4a,U+2cb4e,U+2cb5a-2cb5b,U+2cb64,U+2cb69,U+2cb6c,U+2cb6f,U+2cb73,U+2cb76,U+2cb78,U+2cb7c,U+2cbb1,U+2cbbf-2cbc0,U+2cbce,U+2cc56,U+2cc5f,U+2ccf5-2ccf6,U+2ccfd,U+2ccff,U+2cd02-2cd03,U+2cd0a,U+2cd8b,U+2cd8d,U+2cd8f-2cd90,U+2cd9f;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/0793e0f1fa3cb56ef235f225092d10db.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+2504a,U+25055,U+25122,U+251a9,U+251cd,U+251e5,U+2521e,U+2524c,U+2542e,U+2548e,U+254d9,U+2550e,U+25532,U+25562,U+255a7-255a8,U+2567f,U+25771,U+257a9,U+257b4,U+25874,U+259c4,U+259d4,U+25ad7,U+25ae3-25ae4,U+25af1,U+25bb2,U+25c4b,U+25c64,U+25da1,U+25e2e,U+25e56,U+25e62,U+25e65,U+25ec2,U+25ed7-25ed8,U+25ee8,U+25f23,U+25f5c,U+25fd4,U+25fe0,U+25ffb,U+2600c,U+26017,U+26060,U+260ed,U+26221-26222,U+2626a,U+26270,U+26286,U+2634c,U+26402,U+2648d,U+26676,U+2667e,U+266b0,U+2671d,U+2677c,U+268dd,U+268ea,U+26951,U+2696f,U+26999,U+269dd,U+26a1e,U+26a58,U+26a8c,U+26ab7,U+26aff,U+26b5c,U+26c21,U+26c29,U+26c73,U+26cdd,U+26e40,U+26e65,U+26f94,U+26ff6-26ff8,U+270f4,U+2710d,U+27139,U+273da-273db,U+273fe,U+27410,U+27449,U+27614-27615,U+27631,U+27684,U+27693,U+2770e,U+27723,U+27752,U+27985,U+27a84,U+27bb3,U+27bbe,U+27bc7,U+27cb8,U+27da0,U+27e10,U+27fb7,U+27ff9,U+2808a,U+280bb,U+28277,U+28282,U+282f3,U+283cd,U+28408,U+2840c,U+28455,U+2856b,U+285c8-285c9,U+28678,U+28695,U+286d7,U+286fa,U+287e0,U+28946,U+28949,U+2896b,U+28987-28988,U+289ba-289bb,U+28a1e,U+28a29,U+28a43,U+28a71,U+28a99,U+28acd,U+28add,U+28ae4,U+28b49,U+28bc1,U+28bef,U+28c47,U+28c4f,U+28c51,U+28c54,U+28cdd,U+28d10,U+28d71,U+28dfb,U+28e17,U+28e1f,U+28e36,U+28e89,U+28e99,U+28eeb,U+28ef6,U+28f32,U+28ff8,U+292a0,U+292b1,U+29490,U+295cf,U+2967f,U+296f0,U+29719,U+29750,U+298c6,U+29a72;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/d7419bdecdeb72e87ac6f73be88f1849.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+ff88-ff9f,U+ffe0-ffe5,U+ffe8,U+2000b,U+20089-2008a,U+200a2,U+200a4,U+200b0,U+20158,U+20164,U+201a2,U+20213,U+2032b,U+20371,U+20381,U+203f9,U+2044a,U+20509,U+2053f,U+205d6,U+20611,U+20628,U+20676,U+2074f,U+20807,U+2083a,U+208b9,U+2090e,U+2097c,U+20984,U+2099d,U+20a64,U+20ad3,U+20b1d,U+20b9f,U+20bb7,U+20cd0,U+20d45,U+20de1,U+20e64,U+20e6d,U+20e95,U+20f5f,U+21201,U+2123d,U+21255,U+21274,U+2127b,U+212d7,U+212e4,U+212fd,U+2131b,U+21336,U+21344,U+2139a,U+213c4,U+21413,U+2146d-2146e,U+215d7,U+21647,U+216b4,U+21706,U+21742,U+218bd,U+219c3,U+21a1a,U+21c56,U+21d2d,U+21d45,U+21d62,U+21d78,U+21d92,U+21d9c,U+21da1,U+21db7,U+21de0,U+21e33-21e34,U+21f1e,U+21f76,U+21ffa,U+2217b,U+22218,U+2231e,U+223ad,U+22609,U+226f3,U+2285b,U+228ab,U+2298f,U+22ab8,U+22b46,U+22b4f-22b50,U+22ba6,U+22c1d,U+22c24,U+22de1,U+231b6,U+231c3-231c4,U+231f5,U+23372,U+233d0,U+233d2-233d3,U+233d5,U+233da,U+233df,U+233e4,U+233fe,U+2344a-2344b,U+23451,U+23465,U+234e4,U+2355a,U+23594,U+235c4,U+235cb,U+23638-2363a,U+23647,U+2370c,U+2371c,U+2373f,U+23763-23764,U+237e7,U+237ff,U+23824,U+2383d,U+23a98,U+23c7f,U+23c97-23c98,U+23cbe,U+23cfe,U+23d00,U+23d0e,U+23d40,U+23dd3,U+23df9-23dfa,U+23e23,U+23f7e,U+24096,U+24103,U+241c6,U+241fe,U+242ee,U+243bc,U+243d0,U+24629,U+246a5,U+247a4,U+247f1,U+24896,U+249db,U+24a4d,U+24a7d,U+24ac9,U+24b56,U+24b6f,U+24c16,U+24d14,U+24e0e,U+24e37,U+24e6a,U+24e8b;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/b08855401a8b9142ec18f7be67df3313.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+fa28-fa2d,U+fa30-fa6a,U+fb01-fb02,U+fe32,U+ff01-ff5e,U+ff61-ff87;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/1a5a35f3a30be20c4cf6dc63de26d74c.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+979d-979e,U+97a1-97a2,U+97a4-97a8,U+97aa,U+97ac,U+97ae,U+97b1,U+97b3,U+97b5-97b7,U+97b9-97bb,U+97bd-97bf,U+97c1-97c9,U+97cb-97d1,U+97d3-97d9,U+97db-97df,U+97e1,U+97e3,U+97e5,U+97e8,U+97ee,U+97f0-97f2,U+97f4,U+97f8-97fb,U+97fd-9808,U+980a,U+980c-9814,U+9816-9818,U+981a-981e,U+9820-9821,U+9823-9829,U+982b-982d,U+982f-9830,U+9832-9835,U+9837-9839,U+983b-983e,U+9841,U+9843-9858;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/a3a0556d7344e71e68984f3a2f8eb61f.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+9687,U+9689-968a,U+968e,U+9691-9693,U+9695,U+969a-969b,U+969d-969e,U+96a0-96a5,U+96a8-96aa,U+96ac,U+96ae-96af,U+96b1-96b2,U+96b4,U+96b7-96b8,U+96ba-96bb,U+96bf,U+96c2-96c3,U+96c8,U+96ca-96cb,U+96d1,U+96d3-96d4,U+96d6-96df,U+96e1-96e3,U+96e5,U+96eb,U+96f0-96f2,U+96f5,U+96f8,U+96fa-96fb,U+96fd,U+96ff,U+9702,U+9705,U+970a-970b,U+9710-9712,U+9714,U+9718-9719,U+971d,U+971f-9720,U+9722-9729,U+972b-972c,U+972e-972f,U+9733,U+9735-9736,U+973a-973b,U+973d,U+973f,U+9741-9744,U+9746-9749,U+974b,U+974d-974f,U+9751,U+9755,U+9757-9758,U+975a,U+975c,U+9763-9764,U+9766,U+9768,U+976a-976e,U+9770-9772,U+9777-977b,U+977d-9784,U+9786,U+9788,U+978a,U+978e-9790,U+9795-9797,U+9799-979a,U+979c;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/afcbcc9b3a492e50a0920b7677a56fef.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+9471-9479,U+947c-9483,U+9496,U+9498,U+94cf,U+94d3,U+94da,U+94fb,U+951c,U+9527,U+9533,U+953d,U+9543,U+9548,U+954b,U+9555,U+955a,U+9560,U+956e,U+9574-9575,U+9577-957d,U+9580,U+9582-9583,U+9586-9594,U+9596,U+9598-9599,U+959b-959c,U+959e-95a9,U+95ab-95ae,U+95b0-95b2,U+95b4-95b7,U+95b9-95c0,U+95c3,U+95c5-95cd,U+95d0-95d6,U+95d8,U+95da-95dc,U+95de-95e5,U+95ff,U+9607,U+9618,U+961e,U+9620,U+9623-9624,U+9628,U+962c-962d,U+962f-9630,U+9639-963a,U+9641,U+9643,U+964a,U+964e-964f,U+9651,U+9653,U+9658,U+965c-965e,U+9663,U+9665-9666,U+966b,U+966d,U+966f-9671,U+9673,U+9678,U+967a,U+967c-967e,U+9680,U+9682-9684;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/c8f01eb9655d99b3a2dd2841648ab835.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+9279-9280,U+9282-9283,U+9285-928d,U+9291,U+9293-929d,U+929f-92ad,U+92b1-92b7,U+92b9,U+92bb-92bc,U+92bf-92c7,U+92c9-92d3,U+92d5,U+92d7-92d9,U+92dd-92e1,U+92e3-92ea,U+92ed-92f3,U+92f6-92fc,U+92fe-9302,U+9304,U+9306-9309,U+930b-9316,U+9318-931b,U+931d-932d;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/762e95831b5773ce969565964be95853.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+91a0-91a1,U+91a4-91a5,U+91a7-91a8,U+91ab-91ac,U+91b0-91b3,U+91b6-91b9,U+91bc-91be,U+91c0-91c6,U+91c8,U+91cb,U+91d0,U+91d2-91db,U+91dd-91df,U+91e1-91ee,U+91f0-91f1,U+91f3-91fd,U+91ff-920a,U+920c-9212,U+9214-9217,U+9219-921a,U+921c,U+921e,U+9223-9229,U+922c-922e,U+9230-9234,U+9236-923a,U+923c-9240,U+9242-9257,U+9259-925b,U+925e,U+9260-9268,U+926c-926d,U+926f-9272,U+9276-9278;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/d43434b0361bd93a99064c1cebffc0c7.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+9049-904b,U+904e,U+9054-9056,U+9059,U+905c-905e,U+9060-9061,U+9067,U+9069,U+906b,U+906f-9070,U+9072-9073,U+9076-907c,U+907e,U+9081,U+9084-9087,U+9089-908a,U+908c-908d,U+908f-9090,U+9094,U+9098,U+909e-90a0,U+90a5,U+90a7-90a8,U+90b2,U+90bd,U+90bf,U+90c3,U+90c8,U+90cb,U+90d4-90d6,U+90d8-90da,U+90de-90e0,U+90e3-90e5,U+90e9-90ea,U+90ec,U+90f0-90f3,U+90f5-90f7,U+90f9-90fc,U+90ff-9101,U+9103,U+9105-9109,U+910b-9112,U+9114-9118,U+911a-911d,U+911f-9121,U+9124,U+9126-912e,U+9130,U+9132-9138,U+913a-913b,U+913d-9142,U+9144-9145,U+9147-9148,U+9153-9156,U+9158-9159,U+915b,U+915f-9160,U+9166,U+9168,U+9173,U+917a,U+9180-9184,U+9186,U+918a,U+918e-918f,U+9193,U+9196-9197,U+9199,U+919c-919f;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/025bd860d6ccc1927bd140792f3732f5.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+8efb-8f03,U+8f05-8f0e,U+8f10-8f20,U+8f23-8f26,U+8f29-8f2f,U+8f32-8f39,U+8f3b,U+8f3e-8f40,U+8f42-8f64,U+8f6a,U+8f80,U+8f8c,U+8f92,U+8fa4,U+8fa6-8fa7,U+8fad-8faf,U+8fb2,U+8fb4-8fb5,U+8fb7,U+8fba-8fbc,U+8fbf,U+8fc6,U+8fc9-8fcd,U+8fd2,U+8fd6-8fd7,U+8fda,U+8fe0-8fe1,U+8fe3,U+8fef,U+8ff1,U+8ff4-8ff6,U+8ffa-8ffc,U+8ffe-8fff,U+9008,U+900c,U+900e,U+9013,U+9015,U+9019,U+901c,U+9023-9024,U+9027,U+902c,U+9031-9032,U+9034,U+9037,U+9039,U+903d,U+903f,U+9043,U+9045-9046;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/140b764451a8879942a4d10f9e0d5bca.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+8e07-8e08,U+8e0d,U+8e10-8e13,U+8e15-8e1c,U+8e20-8e21,U+8e24-8e27,U+8e2b,U+8e30,U+8e33-8e34,U+8e36,U+8e38,U+8e3c,U+8e3e-8e3f,U+8e45,U+8e4c-8e4e,U+8e50,U+8e53-8e57,U+8e5a-8e65,U+8e67,U+8e6a,U+8e73,U+8e78,U+8e7a-8e7b,U+8e7d-8e7e,U+8e82,U+8e84,U+8e86,U+8e88-8e8e,U+8e91-8e93,U+8e95-8e9a,U+8e9d,U+8e9f-8ea1,U+8ea3-8ea6,U+8ea8-8eaa,U+8eae,U+8eb0-8eb1,U+8eb3,U+8eb5-8eb6,U+8ebb,U+8ebd-8ebe,U+8ec0,U+8ec2,U+8ec5-8ec6,U+8ec8-8ecd,U+8ecf,U+8ed1-8ed4,U+8ed7-8ed8,U+8edb-8ee9,U+8eeb-8eec,U+8eee-8eef,U+8ef1-8ef2,U+8ef4-8efa;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/94229d636acc3f048ac40040b5b80476.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+8cb6-8cc5,U+8cc7-8cc8,U+8cca,U+8ccc-8ccf,U+8cd1-8cd3,U+8cd5-8cd7,U+8cd9-8ce8,U+8cea-8cee,U+8cf0-8cf1,U+8cf3-8cf5,U+8cf8-8cfe,U+8d00,U+8d02,U+8d04-8d0b,U+8d0d-8d10,U+8d12-8d17,U+8d19,U+8d1b-8d1c,U+8d51-8d52,U+8d57,U+8d5f,U+8d68-8d6a,U+8d6c,U+8d6e-8d6f,U+8d71-8d72,U+8d78-8d79,U+8d7b,U+8d7d,U+8d80,U+8d89,U+8d8c-8d90,U+8d92-8d93,U+8d95-8d96,U+8d99,U+8d9b-8d9c,U+8da0-8da1,U+8da5-8da8,U+8daa-8daf,U+8db2,U+8db6-8db7,U+8db9,U+8dc1-8dc2,U+8dc5,U+8dc7-8dc8,U+8dcd,U+8dd0,U+8dd3,U+8dd5,U+8dd8-8dd9,U+8ddc,U+8de0-8de2,U+8de6-8de7,U+8de9,U+8dee,U+8df0-8df2,U+8df4,U+8df6,U+8dfc,U+8dfe-8e00,U+8e02-8e04,U+8e06;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/35e8be1c0cf7dd87e50753fc5238846f.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+8b57-8b60,U+8b63,U+8b65,U+8b67-8b6b,U+8b6d,U+8b6f-8b72,U+8b74,U+8b76-8b7b,U+8b7d-8b86,U+8b88,U+8b8a-8b8c,U+8b8e,U+8b90,U+8b92-8b96,U+8b98-8b9a,U+8b9c-8b9f,U+8bb1,U+8bbb,U+8bc7,U+8bd0,U+8c1e,U+8c39-8c3f,U+8c42-8c43,U+8c45,U+8c48,U+8c4a-8c4b,U+8c4d-8c50,U+8c54,U+8c56-8c57,U+8c5c-8c5d,U+8c5f,U+8c64-8c66,U+8c68-8c69,U+8c6c-8c72,U+8c75-8c77,U+8c7b-8c7d,U+8c80-8c81,U+8c84,U+8c86,U+8c8d-8c93,U+8c95,U+8c97,U+8c99-8ca5,U+8ca7-8cb0,U+8cb2-8cb5;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/8d27e7eaa4bbe4fb1b3f13716e6131fc.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+8aad-8aae,U+8ab0,U+8ab2,U+8ab6-8ac0,U+8ac2-8ac9,U+8acb-8acd,U+8acf-8ae2,U+8ae4,U+8ae6-8ae8,U+8aeb,U+8aed-8af8,U+8afa-8afc,U+8afe-8b02,U+8b04-8b06,U+8b08,U+8b0a-8b25,U+8b27-8b28,U+8b2a-8b31,U+8b33,U+8b35-8b37,U+8b39-8b3e,U+8b40-8b4c,U+8b4e-8b56;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/c3acf6c884b6162b92d80cb612402745.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+89a3-89a4,U+89a6-89a7,U+89a9-89aa,U+89ac-89b0,U+89b2-89b3,U+89b6-89b7,U+89b9-89ba,U+89bd-89c0,U+89c3,U+89d3-89d5,U+89d8-89d9,U+89db,U+89dd,U+89df-89e2,U+89e4,U+89e7-89e9,U+89ec-89ed,U+89f0-89f2,U+89f4,U+89f6-89f8,U+89fa-89ff,U+8a01-8a04,U+8a08,U+8a0a,U+8a0c,U+8a0e-8a18,U+8a1a-8a1b,U+8a1d-8a1f,U+8a21-8a23,U+8a25,U+8a27,U+8a2a,U+8a2c-8a2d,U+8a30-8a31,U+8a33-8a37,U+8a39-8a3c,U+8a3f-8a41,U+8a44-8a47,U+8a4a,U+8a4c-8a52,U+8a54-8a59,U+8a5b,U+8a5d-8a63,U+8a66,U+8a68-8a69,U+8a6b-8a6e,U+8a70-8a77,U+8a7a-8a7c,U+8a7f,U+8a81-8a87,U+8a8b-8a8d,U+8a8f-8a92,U+8a95-8a96,U+8a98-8a9a,U+8a9e,U+8aa0-8aa1,U+8aa3-8aa8,U+8aaa-8aac;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/afeb8a52a80648a229366ac72e968ff0.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+88b9-88ba,U+88bd-88c0,U+88c3-88c4,U+88c8,U+88ca-88cd,U+88cf-88d1,U+88d3,U+88d6-88d7,U+88da-88de,U+88e1,U+88e7,U+88eb-88ec,U+88ee-88ef,U+88f2,U+88f5-88f7,U+88fa-88fb,U+88fd,U+8901,U+8904-8907,U+8909,U+890b-890c,U+890e,U+8911,U+8914-8918,U+891c-891f,U+8922-8923,U+8926-8927,U+8929,U+892c-892f,U+8931-8933,U+8935,U+8937-8939,U+893b-893e,U+8940,U+8942-8943,U+8945-8947,U+8949,U+894b-894d,U+894f-8953,U+8955-895d,U+8960-8964,U+8969-896f,U+8971-8974,U+8976-8977,U+8979-897a,U+897c,U+897e,U+8980,U+8982,U+8985,U+8987-898b,U+898f-8990,U+8993-8998,U+899a-899f,U+89a1-89a2;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/0c51fd592112a38f710c9e83ff85e7ad.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+87c5,U+87c7-87c9,U+87cc,U+87ce-87d0,U+87d4,U+87d6-87d9,U+87dc-87df,U+87e1-87e4,U+87e6-87e8,U+87eb-87ed,U+87ef,U+87f2-87f7,U+87fa-87fc,U+87ff-8802,U+8805-8809,U+880b-8811,U+8814,U+8817,U+8819,U+881c-881d,U+881f-8820,U+8823-882c,U+882e-8831,U+8833,U+8835-8838,U+883b,U+883d-883f,U+8841-8843,U+8846,U+8848,U+884a-884b,U+884e,U+8852-8853,U+8855-8856,U+8858,U+885a-885b,U+885d-8860,U+8867,U+886a,U+886d,U+886f,U+8871,U+8874-8876,U+8879,U+887c,U+8880,U+8883,U+8886,U+8889,U+888c,U+888e,U+8891,U+8893,U+8895,U+8897-889b,U+889e-88a1,U+88a7-88a8,U+88aa,U+88ac,U+88ae-88b0,U+88b2,U+88b4-88b6,U+88b8;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/2bf4891b25b535e57f070dc74852a4f5.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+8678,U+8684-8687,U+8689,U+868e,U+8690-8691,U+8694,U+8696-869a,U+869e,U+86a1-86a2,U+86a5,U+86ab,U+86b2-86b3,U+86b7-86b9,U+86bb-86bf,U+86c1-86c3,U+86c5,U+86c8,U+86cc-86cd,U+86d3,U+86d5-86d7,U+86da,U+86dc-86dd,U+86e2-86e3,U+86e6,U+86e8,U+86ea-86ec,U+86ef,U+86f5-86f7,U+86fa-86fd,U+8701,U+8704-8706,U+870b-870c,U+870e,U+8710-8711,U+8719,U+871b,U+871f-8720,U+8724,U+8726-8728,U+872a,U+872c-872d,U+8730,U+8732-8733,U+8735,U+8738,U+873a,U+873c,U+8740-8743,U+8746,U+874b,U+874d,U+874f-8752,U+8754-8756,U+8758,U+875a-875f,U+8761-8762,U+8766-876d,U+876f,U+8771-8773,U+8775,U+8777-877a,U+877f,U+8781,U+8784,U+8787,U+8789,U+878f-8792,U+8794,U+8796,U+8798-879e,U+87a0,U+87a2-87a4,U+87a7,U+87aa,U+87ae,U+87b0-87b2,U+87b4,U+87b6-87b9,U+87bb-87bc,U+87be-87bf,U+87c1-87c4;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/7f29804edc2ebf1a1d1ae69103d21471.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+84e8-84eb,U+84ee-84ef,U+84f1-84f4,U+84f6-84f7,U+84f9-84fb,U+84fd-84fe,U+8500,U+8502-8503,U+8506-850b,U+850d-850f,U+8512,U+8514-8516,U+8518-8519,U+851b-851e,U+8520,U+8523-852a,U+852d-8532,U+8534-8535,U+853e,U+8540-8541,U+8544-8547,U+854b,U+854d-854f,U+8551-8555,U+8557-8558,U+855a-855b,U+855d,U+8560-8563,U+8565-8567,U+8569-8571,U+8575-8578,U+857c-857d,U+857f-8583,U+8586,U+8588-858e,U+8590-8591,U+8593-859a,U+859d-85a3,U+85a6-85a7,U+85a9,U+85ab-85ad,U+85b1,U+85b3-85b6,U+85b8,U+85ba,U+85bc-85bd;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/92ad385a6f528335290346f3bf79915b.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+8388,U+838a-838d,U+838f-8390,U+8394-8397,U+8399-839a,U+839d,U+839f,U+83a2-83a7,U+83ae-83af,U+83b5,U+83bf,U+83c2-83c4,U+83c6,U+83c8-83c9,U+83cb,U+83cd-83ce,U+83d0-83d1,U+83d3,U+83d5,U+83d7,U+83d9,U+83db,U+83de,U+83e2-83e4,U+83e7-83e8,U+83eb-83ec,U+83ee-83ef,U+83f3-83f7,U+83fa-83fc,U+83fe-83ff,U+8407,U+8409-840a,U+8410,U+8412-8413,U+8415,U+8417,U+841a-841b,U+8420,U+8422-8423,U+8429-842d,U+842f-8430,U+8432-8437,U+8439-843b,U+843f-8440,U+8442-8445,U+8447-8450,U+8452,U+8454,U+8456,U+845d-8460,U+8462,U+8464-8468,U+846e-8470,U+8474,U+8477,U+8479,U+847c-847e,U+8481,U+8484-8486,U+848d,U+848f-8491,U+8493-8494,U+8498,U+849a-849b,U+849d-84a0,U+84a2,U+84a4,U+84a6-84ae,U+84b0-84b1,U+84b6,U+84bb-84bc,U+84be,U+84c0,U+84c2,U+84c5-84c7,U+84cb-84cc,U+84ce-84cf,U+84d2,U+84d4,U+84d7,U+84d9-84dc,U+84e2,U+84e7;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/90ce6b4a58ea38c24c24d9c5d9501313.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+81d1-81d3,U+81d5-81e2,U+81e4-81e6,U+81e8-81e9,U+81ee,U+81f2,U+81f7-81fa,U+81fd,U+81ff,U+8203,U+8207-820b,U+820e,U+8211,U+8215-8219,U+821d,U+8220,U+8225,U+8229,U+822e,U+8232,U+823a,U+823c-823d,U+823f-8240,U+8242,U+8245-8246,U+824e,U+8250-8253,U+8255-8257,U+8259,U+825b-825e,U+8260-8264,U+8266,U+8269-826d,U+8271,U+8275-8278,U+827c,U+8280,U+8283,U+8285,U+8290,U+8293-8294,U+829a-829b,U+829e,U+82a0,U+82a2-82a3,U+82a7,U+82b2,U+82b5-82b6,U+82ba-82bc,U+82bf-82c0,U+82c2-82c3,U+82c5-82c6,U+82c9,U+82d6,U+82d9,U+82e2,U+82e7-82e8,U+82ea,U+82ec-82ed,U+82f0,U+82f2-82f3,U+82f5-82f6,U+82fa,U+82fd-82fe,U+8300,U+830b,U+830d,U+8313,U+8316,U+8318-8319,U+831d-831e,U+8320,U+8322-8326,U+8329-832a,U+8330,U+8332,U+8337,U+833b,U+833d,U+833f,U+8341-8342,U+8344-8345,U+8348,U+834a-834e,U+8353,U+8355-8359,U+8362,U+8373-8376,U+837a,U+837e-837f,U+8381,U+8383,U+8387;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/1d04bc27a9976845f976ae70a8ec09f5.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+803c,U+803e,U+8047-8048,U+804f-8051,U+8056,U+805c-8062,U+8064,U+8066-8068,U+806c,U+806f-8070,U+8072-8079,U+807b,U+807d-807e,U+8082,U+8085,U+808a,U+808f-8090,U+8092,U+8095,U+8099,U+809e,U+80a3,U+80a6-80a7,U+80ac,U+80b5,U+80b8,U+80c5,U+80c7-80ca,U+80cf-80d1,U+80d4-80d5,U+80d8,U+80e0,U+80e3,U+80e6,U+80f5,U+80f9,U+80fb,U+80fe,U+8100-8101,U+8105,U+8107-8108,U+8115,U+8119,U+811b,U+811d,U+811f,U+8121-8125,U+8127,U+8129,U+812b,U+812d,U+8130,U+8133,U+8135,U+8139-813a,U+813d,U+813f,U+8141,U+8143-8144,U+8147,U+814d-814f,U+8152,U+8156,U+8158,U+815b-815c,U+815e-815f,U+8161-8162,U+8164,U+8166,U+8168,U+816b,U+816f,U+8172-8173,U+8176-8178,U+8181,U+8183-8187,U+8189,U+818b-818d,U+8193,U+8195,U+8197,U+8199-819a,U+819e-81a0,U+81a2,U+81a4-81a5,U+81a7,U+81a9,U+81ab-81ac,U+81ae,U+81b0-81b2,U+81b4-81b5,U+81b7-81b9,U+81bc-81bf,U+81c4-81c5,U+81c7-81c9,U+81cd,U+81cf-81d0;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/9a66dd152066716391308e69760ccf43.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+7e90-7e9a,U+7e9c,U+7eae,U+7eb4,U+7ebb-7ebc,U+7ed6,U+7ee4,U+7ef9,U+7f0a,U+7f10,U+7f1e,U+7f39,U+7f3b-7f3f,U+7f43,U+7f47-7f48,U+7f4a-7f4f,U+7f52-7f53,U+7f5b-7f5e,U+7f60,U+7f63-7f67,U+7f6b-7f6d,U+7f70,U+7f73,U+7f75-7f78,U+7f7a-7f7d,U+7f7f,U+7f82-7f83,U+7f85-7f89,U+7f8b,U+7f8d,U+7f90-7f93,U+7f95-7f97,U+7f9b-7f9c,U+7fa0,U+7fa2-7fa3,U+7fa5-7fa6,U+7fa8-7fa9,U+7fac-7fae,U+7fb1,U+7fb3,U+7fb5-7fb7,U+7fba-7fbb,U+7fbe,U+7fc0,U+7fc2-7fc3,U+7fc6-7fc9,U+7fcb,U+7fcd,U+7fcf-7fd2,U+7fd7,U+7fd9-7fdc,U+7fde,U+7fe2-7fe3,U+7fe8,U+7fea-7fed,U+7fef,U+7ff2,U+7ff4-7ff5,U+7ff7-7ffa,U+7ffd-7fff,U+8002,U+8007-8008,U+800a,U+800e-800f,U+8011,U+801b,U+801e-801f,U+8021,U+8024,U+802c,U+802e,U+8030,U+8034,U+8039-803a;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/e6f95b473bbdd3f158716881c34cd402.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+7d59-7d5f,U+7d61-7d63,U+7d66-7d6b,U+7d6d,U+7d6f-7d73,U+7d75-7d76,U+7d79-7d7d,U+7d7f-7d81,U+7d83-7d86,U+7d88-7d89,U+7d8b-7d8f,U+7d91-7d94,U+7d96-7d97,U+7d99-7da3,U+7da7,U+7da9-7dad,U+7daf-7dc2,U+7dc4-7dc7,U+7dc9-7dcc,U+7dce-7dcf,U+7dd1-7dd2,U+7dd4-7de1,U+7de3-7de4,U+7de6-7dec,U+7dee-7df4,U+7df6-7df7,U+7df9-7dfb,U+7e01,U+7e03-7e05,U+7e08-7e0c;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/679026366941b24b659285feb941ac3d.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+7c60-7c61,U+7c63-7c67,U+7c69-7c70,U+7c72,U+7c75,U+7c78-7c7a,U+7c7e-7c85,U+7c88,U+7c8a-7c8d,U+7c8f-7c90,U+7c94,U+7c96,U+7c9b,U+7ca0-7ca1,U+7ca3,U+7ca6-7ca8,U+7cab,U+7cad,U+7caf,U+7cb4-7cb7,U+7cba-7cbb,U+7cbf-7cc0,U+7cc2,U+7cc4,U+7cc9,U+7ccb,U+7cce,U+7cd0-7cd2,U+7cd4,U+7cd8,U+7cdd-7cde,U+7ce2,U+7ce6-7ce7,U+7cea-7cec,U+7cee,U+7cf0-7cf2,U+7cf4-7cf7,U+7cf9-7cfa,U+7cfd-7cfe,U+7d00-7d09,U+7d0b-7d1f,U+7d21,U+7d23,U+7d28-7d29,U+7d2c,U+7d2e,U+7d30-7d33,U+7d35-7d36,U+7d38-7d48,U+7d4a-7d4c,U+7d4e-7d56,U+7d58;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/02802deec4ec036377bc5f819242878d.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+7a19,U+7a1b-7a1c,U+7a1f,U+7a22,U+7a26,U+7a28,U+7a2b,U+7a2d-7a32,U+7a38,U+7a3e,U+7a40,U+7a42-7a45,U+7a47-7a50,U+7a54,U+7a56,U+7a58-7a5d,U+7a5f-7a63,U+7a67-7a6e,U+7a71,U+7a75,U+7a7b,U+7a7d-7a7e,U+7a82,U+7a85,U+7a87,U+7a89-7a8c,U+7a8e-7a90,U+7a93-7a94,U+7a99,U+7a9e,U+7aa2-7aa3,U+7aa9-7aab,U+7aae-7ab2,U+7ab4-7abc,U+7abe,U+7ac0-7ac1,U+7ac3-7ac8,U+7aca,U+7acc-7acf,U+7ad1-7ad3,U+7ad5,U+7ad8,U+7ada,U+7adc-7add,U+7ae1-7ae2,U+7ae4,U+7ae7-7ae8,U+7aea-7aeb,U+7aee,U+7af0,U+7af4,U+7af6-7af8,U+7afb,U+7b00-7b02,U+7b05,U+7b07,U+7b09,U+7b0e,U+7b10,U+7b12-7b13,U+7b18,U+7b1a,U+7b1d,U+7b22-7b23,U+7b27,U+7b2d,U+7b2f-7b30,U+7b32,U+7b34-7b36,U+7b39,U+7b3b,U+7b3d,U+7b40-7b41,U+7b44,U+7b46,U+7b48,U+7b4a,U+7b4d-7b4e,U+7b53,U+7b55,U+7b5c,U+7b61,U+7b63-7b67,U+7b69,U+7b6c;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/9f6cbecb2616b995352c7444fcc8ca28.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+78db-78e7,U+78e9-78ea,U+78ed,U+78ef,U+78f3,U+78f9,U+78fb-7900,U+7902,U+7904,U+7907,U+7909,U+790c,U+790e,U+7910-7912,U+7914,U+7917,U+7919,U+791b-791d,U+7921,U+7923,U+7925-7931,U+7935,U+7938-7939,U+793d,U+793f,U+7942-7945,U+7947,U+794a-794c,U+794e-7952,U+7954-7955,U+7958,U+7961,U+7963-7964,U+7969-796b,U+7970,U+7972-7974,U+7979,U+797c-797f,U+7982,U+7988,U+798b,U+798d-798e,U+7990,U+7992-7998,U+799b-799d,U+79a0-79a2,U+79a4,U+79a6,U+79a8-79ae,U+79b0-79b2,U+79b4,U+79b6-79b8,U+79bf,U+79c2,U+79c5,U+79c7-79c8,U+79ca,U+79cc,U+79ce-79cf,U+79d4,U+79d6,U+79da,U+79dc-79de,U+79e0-79e1,U+79ea,U+79ec,U+79ee,U+79f6-79f7,U+79fa,U+79fe,U+7a04-7a05,U+7a08-7a0a,U+7a0c,U+7a10-7a13,U+7a15,U+7a18;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/66954b3518311a8616e09773d0f47680.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+774b-774f,U+7752,U+7754-7756,U+7758-7759,U+775c,U+775e-7760,U+7767,U+7769-776a,U+776d-776f,U+7772,U+777a,U+777c,U+7781-7783,U+7787-7789,U+778b,U+778f,U+7794-7795,U+7797-779e,U+77a1,U+77a3-77a4,U+77a8,U+77ab,U+77ad,U+77b1-77b2,U+77b4,U+77b6-77b7,U+77b9-77ba,U+77bc,U+77c2,U+77c4,U+77c7,U+77c9-77ca,U+77cc,U+77ce-77d0,U+77d3-77d5,U+77d8-77da,U+77de-77e0,U+77e4,U+77e6,U+77e8,U+77ea,U+77ef-77f2,U+77f4,U+77f7,U+77f9-77fc,U+7803-7806,U+780e-7810,U+7813,U+7815,U+7819,U+7820-7822,U+7828,U+782a-782b,U+782e-782f,U+7831-7833,U+7835,U+783f,U+7841,U+7843,U+7848-784a,U+784d,U+784f,U+7851,U+7854,U+7859-785a,U+785c,U+785e,U+7860,U+7862,U+7864-7865,U+7868-7869,U+786f-7872,U+7874,U+7879-787b,U+787e-7881,U+7883-7886,U+7888,U+788a,U+788f,U+7894-7896,U+7899,U+789d-789e,U+78a0,U+78a2,U+78a4,U+78a8-78ad,U+78af,U+78b5-78b6,U+78b8,U+78ba-78bc,U+78c3-78c4,U+78c6,U+78c8,U+78cc-78cf,U+78d1,U+78da;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/d5f0256231b5370d63704b9255220cc8.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+75ed-75ef,U+75f2-75f3,U+75f5-75f8,U+75fa-75fb,U+75fd-75fe,U+7602,U+7606-7609,U+760b,U+760d,U+760f,U+7611,U+7613-7614,U+7616,U+761a,U+761c-761e,U+7621,U+7623,U+7627-7628,U+762f,U+7631-7632,U+763a-763b,U+763d,U+7641-7642,U+7646-7649,U+764b,U+764e,U+7650,U+7652-7653,U+7655,U+7657-765a,U+765f-7662,U+7664-7665,U+7667-766a,U+766c-766e,U+7670-7672,U+7674-7676,U+7679-767a,U+767c,U+767f-7681,U+7683,U+7689-768a,U+768f-7690,U+7692,U+7695,U+769a-769e,U+76a0-76a1,U+76a5-76a8,U+76aa-76ab,U+76ad,U+76af-76b0,U+76b5-76bb,U+76bd-76be,U+76c3-76c4,U+76c9,U+76cc,U+76d3,U+76da,U+76dc-76de,U+76e1,U+76e3-76e4,U+76e6-76ea,U+76ec-76ed,U+76f0,U+76f3,U+76f5,U+76f7,U+76fa-76fb,U+7701,U+7703,U+7705,U+770a,U+770c,U+7710-7713,U+7715,U+7717,U+771b,U+771d-771e,U+7723-7725,U+7727,U+772c,U+7731-7734,U+7739,U+773b,U+773d-773e,U+7744-7746,U+774a;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/c7d4a78165590d1e7da21ef625b0c899.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+7495,U+7497-749a,U+749f-74a3,U+74a5-74a6,U+74aa-74b2,U+74b5-74b9,U+74bb,U+74bd-74c3,U+74c5,U+74c8-74cb,U+74cf,U+74d4-74db,U+74dd,U+74df,U+74e1,U+74e5,U+74e7-74e9,U+74eb-74ec,U+74f0-74f2,U+74f5,U+74f8,U+74fa-74fb,U+74fd-74fe,U+7500-7503,U+7505,U+7507-7508,U+750b-750c,U+750e,U+7510,U+7512,U+7514-7517,U+751d-751e,U+7520-7524,U+7526,U+752a,U+752e,U+7534,U+7539,U+753c-753d,U+753f,U+7544,U+7546-7547,U+7549-754a,U+754d,U+7550-7552,U+7556,U+755d,U+755f-7560,U+7562-7564,U+7567,U+7569,U+756b-756d,U+756f-7571,U+7573,U+7575-7577,U+757a,U+757d-757e,U+7580-7582,U+7584,U+7587,U+7589-758a,U+758c-758e,U+7590,U+7593,U+7595,U+7598,U+75a2,U+75a7,U+75aa,U+75ad,U+75b6-75b7,U+75ba-75bb,U+75bf-75c1,U+75c6,U+75cb-75cc,U+75ce-75d1,U+75d3,U+75d7,U+75d9-75da,U+75dd,U+75df-75e1,U+75e9,U+75ec;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/73fbbaa07257ec8e6da794c53d01d6b2.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+7358-735b,U+735d-735f,U+7361-7363,U+7365-736b,U+736e,U+7370-7378,U+737a-737d,U+737f-7383,U+7385-7386,U+7388,U+738a,U+7392-7395,U+7397-739a,U+739c-739e,U+73a0-73a1,U+73a4-73a6,U+73a8,U+73ac-73ad,U+73b1,U+73b4-73b6,U+73b8-73b9,U+73bc-73bf,U+73c3,U+73c5-73c7,U+73cb-73cc,U+73ce,U+73d2-73d8,U+73da-73dd,U+73e1-73e4,U+73e6,U+73e8,U+73ea-73eb,U+73ee,U+73f0-73f1,U+73f4-73fa,U+73fc-7401,U+7404,U+7407-7408,U+740b-740e,U+7413-7414,U+7416,U+741d,U+741f-7421,U+7423-7424,U+7429,U+742b,U+742d,U+742f,U+7431-7432,U+7439-743a,U+743f-7440,U+7442-7447,U+744a-744b,U+744d-7454,U+7456,U+745d,U+7460,U+7462-7464,U+7466-746c,U+746e-746f,U+7471-7473,U+7475,U+7479,U+747c-747d,U+747f,U+7485-7486,U+7488-748a,U+7492,U+7494;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/7a2a8a602d405d83f2f8078b38acfdb1.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+71f2,U+71f4-71f5,U+71f8,U+71fb-7203,U+7205,U+7207,U+720a,U+720c-720d,U+7210,U+7213-7215,U+7219-721b,U+721e-721f,U+7222-7223,U+7226-7227,U+7229,U+722b,U+722d,U+7232,U+7234,U+723a,U+723c,U+723e,U+7240-7242,U+7244,U+7246,U+7249-724b,U+724f,U+7253,U+7255,U+7257-7258,U+725a,U+725c,U+725e,U+7260,U+7263-7265,U+726a,U+726c,U+7270,U+7273-7274,U+7276-7278,U+727b-727d,U+7282,U+7285-7289,U+728c,U+728e,U+7290-7291,U+7293,U+7295-7298,U+729a-729b,U+729d-729e,U+72a0-72aa,U+72ae,U+72b1-72b2,U+72b5,U+72ba,U+72bd-72c0,U+72c5-72c7,U+72c9-72cc,U+72d1,U+72d4,U+72d6,U+72d8,U+72da-72dd,U+72df,U+72e2-72e4,U+72e6,U+72ea-72eb,U+72f6,U+72f9,U+72fd-7300,U+7304-7305,U+7307-7308,U+730b-730c,U+730f,U+7311-7312,U+7318-7319,U+731f,U+7323-7324,U+7326-7328,U+732d,U+732f-7330,U+7332-7333,U+7335-7336,U+733a-733d,U+7340-7345,U+7349-734a,U+734c,U+734e-734f,U+7351;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/1ca65de51ee3078d04026123224028cd.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+7082-7084,U+7086,U+708c,U+7091,U+7093,U+7097-7098,U+709a,U+709f,U+70a1,U+70a3-70a4,U+70a9,U+70b0,U+70b4-70b5,U+70ba,U+70be,U+70c5-70c7,U+70cb,U+70cd-70cf,U+70d1-70d4,U+70d7,U+70da,U+70dc-70de,U+70e0-70e2,U+70f0-70f1,U+70f3-70f4,U+70f6,U+70f8,U+70fa-70fc,U+70ff-7100,U+7102-7104,U+7106-7108,U+710b-710f,U+7114,U+7117,U+711b-711c,U+711e-7123,U+7125,U+7128,U+712b,U+712e,U+7132,U+713a,U+713c,U+713f,U+7141-7144,U+7146-7147,U+7149,U+714b,U+714d,U+7150-7156,U+7158-715a,U+715d,U+715f-7163,U+7165,U+7169-716a,U+716c,U+7170,U+7174,U+7179,U+717b,U+7180-7182,U+7185-7189,U+7190,U+7192,U+7195-7197,U+719a-719e,U+71a1-71a2,U+71a4-71a5,U+71a7,U+71a9-71aa,U+71ae-71b2,U+71b8,U+71ba-71c2,U+71c4-71cb,U+71cf-71d0,U+71d2-71d3,U+71d6-71dc,U+71df,U+71e1-71e2,U+71e4,U+71e6,U+71e8,U+71ec-71ed,U+71f0-71f1;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/2b6ed399c4ba414d8b2b59be0551902d.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+6ed8-6ed9,U+6edc-6edd,U+6ee7,U+6eea-6eef,U+6ef1-6ef2,U+6ef5-6ef8,U+6efb-6f01,U+6f03,U+6f05,U+6f07-6f08,U+6f0a-6f0e,U+6f10-6f12,U+6f16,U+6f18-6f1a,U+6f1c,U+6f1e-6f1f,U+6f21-6f23,U+6f25-6f27,U+6f2c,U+6f2e,U+6f30,U+6f32,U+6f34-6f35,U+6f37-6f3c,U+6f3f-6f41,U+6f43,U+6f45,U+6f4e-6f57,U+6f59-6f5b,U+6f5d,U+6f5f-6f61,U+6f63-6f64,U+6f67-6f6c,U+6f6f-6f70,U+6f73,U+6f75-6f77,U+6f7b,U+6f7d-6f82,U+6f85-6f87,U+6f8b,U+6f90-6f98,U+6f9a-6f9b,U+6f9e-6fa0,U+6fa2-6fa6,U+6fa8-6fb2,U+6fb4-6fb5,U+6fb8,U+6fba,U+6fbc-6fbf,U+6fc1,U+6fc3-6fc4,U+6fc6-6fc8,U+6fca-6fcd;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/d6c5114bdddae70aafcbc52b9c18f0b5.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+6d42,U+6d44,U+6d49,U+6d50,U+6d55,U+6d57-6d58,U+6d5b,U+6d5f,U+6d61-6d62,U+6d64-6d65,U+6d67-6d68,U+6d6c-6d6d,U+6d70,U+6d72,U+6d75-6d76,U+6d79-6d7b,U+6d7d-6d81,U+6d83-6d84,U+6d86-6d87,U+6d8a-6d8b,U+6d8d,U+6d90,U+6d92,U+6d96-6d99,U+6d9c,U+6da2,U+6dac,U+6db3-6db4,U+6db7,U+6dba-6dbe,U+6dc2,U+6dc8-6dca,U+6dcd-6dd0,U+6dd2-6dd5,U+6dd7,U+6dda-6ddc,U+6ddf,U+6de2-6de3,U+6de5,U+6de8-6dea,U+6ded,U+6def-6df0,U+6df2,U+6df4-6df6,U+6df8,U+6dfa,U+6dfd,U+6e00,U+6e03,U+6e07-6e09,U+6e0b,U+6e13,U+6e15,U+6e19,U+6e1b-6e1c,U+6e1e-6e1f,U+6e22,U+6e26-6e28,U+6e2c,U+6e2e,U+6e30-6e31,U+6e33,U+6e35-6e36,U+6e39,U+6e3b-6e42,U+6e45-6e4c,U+6e4f,U+6e51-6e52,U+6e55,U+6e57,U+6e5a,U+6e5c-6e5e,U+6e60-6e69,U+6e6f,U+6e71-6e74,U+6e76-6e79,U+6e80-6e82,U+6e87-6e88,U+6e8c-6e8e,U+6e92-6e94,U+6e96-6e97,U+6e99-6e9b,U+6e9d-6e9e,U+6ea0-6ea1,U+6ea3-6ea4,U+6ea6,U+6eab,U+6eae,U+6eb0,U+6eb3,U+6eb5,U+6eb9,U+6ebc,U+6ebe-6ec0,U+6ec3-6ec6,U+6ec8-6eca,U+6ecc-6ece,U+6ed0,U+6ed2,U+6ed6;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/4736b9c85e70cf6850be6770c92afbf5.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+6b6d,U+6b6f,U+6b72-6b78,U+6b7a,U+6b7e-6b80,U+6b88,U+6b8c,U+6b8e-6b8f,U+6b91,U+6b94-6b95,U+6b97-6b99,U+6b9e-6ba0,U+6ba2-6ba7,U+6ba9,U+6bab,U+6bad-6bb2,U+6bb6,U+6bba-6bbe,U+6bc0,U+6bc3-6bc4,U+6bc6-6bca,U+6bcc,U+6bce,U+6bd0,U+6bd8,U+6bda,U+6bde-6be0,U+6be2-6be4,U+6be6-6be8,U+6bec,U+6bee,U+6bf0-6bf2,U+6bf7-6bf8,U+6bfb-6bfc,U+6bfe-6c04,U+6c08-6c0c,U+6c0e,U+6c17,U+6c1d,U+6c20,U+6c23,U+6c25,U+6c2b-6c2c,U+6c33,U+6c36-6c37,U+6c3a-6c3b,U+6c3e-6c3f,U+6c43,U+6c48,U+6c4b-6c4f,U+6c52,U+6c59-6c5a,U+6c62,U+6c65-6c67,U+6c6b,U+6c6d,U+6c6f,U+6c71,U+6c73,U+6c78,U+6c7a-6c7b,U+6c80,U+6c84,U+6c87,U+6c8a-6c8b,U+6c8d-6c8e,U+6c92,U+6c95-6c98,U+6c9a,U+6c9c-6c9d,U+6ca2,U+6ca8,U+6cac,U+6cb0,U+6cb4,U+6cb6-6cb7,U+6cba,U+6cc0-6cc3,U+6cc6-6cc7,U+6ccd,U+6ccf,U+6cd1-6cd2,U+6cd9-6cda,U+6cdc-6cdd,U+6ce7,U+6ce9,U+6cec-6ced,U+6cf2,U+6cf9,U+6d00,U+6d03,U+6d08-6d0a,U+6d0d,U+6d0f-6d11,U+6d13,U+6d16,U+6d18,U+6d1d,U+6d1f-6d20,U+6d22-6d24,U+6d26,U+6d28-6d29,U+6d2c-6d2d,U+6d2f-6d30,U+6d34,U+6d36-6d38,U+6d3a,U+6d3f-6d40;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/a596ac2bb718b33c99e58e8da77232a4.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+69ae-69b0,U+69b2-69b3,U+69b5-69b6,U+69b9-69ba,U+69bc-69c0,U+69c2-69c4,U+69c6-69c7,U+69c9,U+69cb,U+69cd,U+69cf,U+69d1,U+69d3,U+69d6-69da,U+69dc-69de,U+69e2-69ec,U+69ee-69ef,U+69f1,U+69f3-69f9,U+69fb-69fc,U+69fe,U+6a00-6a02,U+6a04-6a09,U+6a0b-6a0d,U+6a0f,U+6a11-6a16,U+6a19-6a1b,U+6a1d-6a1e,U+6a20,U+6a22-6a23,U+6a25-6a27,U+6a29,U+6a2b,U+6a2e,U+6a30,U+6a32-6a34,U+6a36,U+6a38-6a3c,U+6a3f-6a41,U+6a45-6a46,U+6a48-6a49,U+6a4b,U+6a4d-6a4f,U+6a51-6a52,U+6a54-6a56,U+6a5a,U+6a5d-6a60,U+6a62,U+6a64,U+6a66-6a6b,U+6a6d,U+6a6f,U+6a72-6a73,U+6a76,U+6a78,U+6a7a,U+6a7e-6a7f,U+6a81,U+6a83,U+6a85,U+6a87,U+6a89,U+6a8b-6a8d,U+6a92-6a96,U+6a9a;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/7a94f65bb67544f437fed0d3ffab4641.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+6880,U+6882,U+6884,U+6887,U+6889-688e,U+6890-6892,U+6894,U+6896,U+6898-689d,U+689f-68a1,U+68a3-68a5,U+68a9-68ac,U+68ae,U+68b1-68b2,U+68b4,U+68b6,U+68b9-68bf,U+68c1,U+68c3-68c8,U+68ca,U+68cc,U+68ce-68d1,U+68d3-68d4,U+68d6-68d7,U+68d9,U+68dc-68df,U+68e1,U+68e4-68ed,U+68ef,U+68f2-68f4,U+68f6-68f8,U+68fb,U+68fd,U+6900,U+6903-6904,U+6906-6908,U+690a,U+690c,U+690f,U+6911,U+6913-6915,U+6917,U+6919-691d,U+6921-6923,U+6925-6926,U+6928,U+692a,U+692f,U+6932-6933,U+6935-6938,U+693b-693c,U+6940-6941,U+6944-6946,U+6948-694c,U+694e-694f,U+6951-6953,U+6955-6956,U+6958-6959,U+695b-695c,U+695f,U+6961-6962,U+6964-6965,U+6968-696a,U+696c-696d,U+696f-6970,U+6972-6976,U+697a-697b,U+697d-697f,U+6981,U+6983,U+6985,U+698a,U+698e,U+6990-6993,U+6996-6997,U+6999-699a,U+699e,U+69a0-69a1,U+69a3-69a6,U+69a9-69aa,U+69ac;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/e06197b7fb9a899d3f059a43b61f6d07.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+66b6-66b8,U+66ba-66bd,U+66bf-66c1,U+66c4,U+66c6-66cc,U+66cf,U+66d2,U+66d6,U+66d8,U+66da,U+66de,U+66e0,U+66e3-66e4,U+66e8,U+66eb-66ee,U+66f1,U+66f5-66f6,U+66f8,U+66fa-66fb,U+66fd,U+6701,U+6703-6705,U+670e-670f,U+6712-6713,U+6716,U+6718-6719,U+671e,U+6720-6723,U+6727,U+672e,U+6733,U+6736-6739,U+673b-673c,U+673e-673f,U+6741,U+6744-6745,U+6747,U+674b,U+674d,U+6754-6755,U+6757,U+6759-675a,U+675d,U+6762-6764,U+6766-6767,U+676c,U+676e,U+6771,U+6774,U+6776,U+6778-677b,U+677d,U+6783,U+6785-6786,U+678c-678e,U+6791-6794,U+6799,U+679b,U+679f-67a1,U+67a6,U+67a9,U+67ae,U+67b2,U+67b4,U+67b9-67bb,U+67c0,U+67c2,U+67c5-67c6,U+67c8-67ce,U+67d6-67d7,U+67db,U+67df,U+67e3-67e4,U+67e6-67e7,U+67ea-67eb,U+67ed-67ee,U+67f2,U+67f5-67fc,U+67fe,U+6801-6804,U+6810,U+6812,U+6814,U+6818,U+681a,U+681c,U+681e-6820,U+6822,U+6825-6826,U+6828,U+682b-6831,U+6834-6835,U+683a-683b,U+684b,U+684d,U+684f,U+6852,U+6857,U+6859,U+685b-685d,U+685f,U+686d-686f,U+6871-6872,U+6875,U+6878-687f;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/ae242cb9be0343979a5bd4abb047b3fb.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+6520-6524,U+6526,U+6529-652a,U+652c-652d,U+6532-6533,U+6537,U+653d,U+6541,U+6543-6544,U+6546-6547,U+654a,U+654d-654e,U+6553-6554,U+6557-6558,U+655c,U+6564-6565,U+6567-656a,U+656f,U+6573,U+6575-6576,U+6578-657c,U+657f-6585,U+6588-658a,U+658e,U+6592,U+6594-6596,U+659d-659e,U+65a0,U+65a2,U+65a8,U+65aa,U+65ac,U+65ae,U+65b2-65b3,U+65b5-65b8,U+65bb,U+65bf,U+65c2,U+65c9,U+65cd,U+65d0,U+65d3-65d4,U+65d9-65db,U+65dd-65df,U+65e1,U+65e3,U+65f2-65f5,U+65f8-65f9,U+65fb-65fd,U+65ff,U+6604-6605,U+6607-6609,U+660b,U+660d,U+6610-6612,U+661c,U+661e,U+6621-6624,U+6626,U+662a-662c,U+662e,U+6630,U+6632-6633,U+6639-663b,U+663d,U+663f,U+6642,U+6644-6645,U+6647-664a,U+664e,U+6650-6651,U+6659,U+665b-665e,U+6662-6663,U+6665,U+6667,U+6669-666d,U+6671-6673,U+6678-6679,U+667b-667c,U+6680-6681,U+6683,U+6685-6686,U+6688-668b,U+668d-668e,U+6690,U+6692,U+6694-6695,U+6698-6699,U+669f-66a2,U+66a4,U+66a6,U+66a9-66ab,U+66ad,U+66af-66b2,U+66b5;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/d5383eba63c5479635f7350dad41ed0e.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+6416-6418,U+641a,U+641f,U+6422-6425,U+6427-6429,U+642b,U+642e-6430,U+6433,U+6435-6437,U+6439,U+643e,U+6440,U+6442-6443,U+644b,U+644d-6451,U+6453,U+6459-645d,U+645f-6461,U+6465-6466,U+6468,U+646b-646c,U+646e-6470,U+6472-6477,U+647b,U+647d,U+647f,U+6483,U+6488-648c,U+648f-6490,U+6493,U+6497-6498,U+649a,U+649c-649d,U+649f-64a0,U+64a2-64a3,U+64a5-64a6,U+64ab,U+64b1-64b3,U+64b9,U+64bb,U+64bd-64bf,U+64c1,U+64c3-64c4,U+64c7,U+64c9-64cc,U+64cf,U+64d4-64d6,U+64d9-64db,U+64e0-64e1,U+64e3,U+64e5,U+64e7-64e9,U+64eb-64ed,U+64ef-64f4,U+64f6-64f8,U+64fa-64ff,U+6501,U+6503-6507,U+650c-6510,U+6513-6517,U+6519,U+651b-651e;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/d5f2c4dd4f131b36c91ebedbd8545ad4.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+621e,U+6220,U+6223,U+6226-6227,U+6229,U+622b,U+622d,U+622f-6230,U+6232,U+6236,U+6238,U+623a-623b,U+6242,U+6245-6246,U+624a,U+6250,U+6255-6256,U+6259-625a,U+625c,U+625e,U+6260-6262,U+6264-6265,U+6268,U+6271-6272,U+6274,U+6277,U+627a-627b,U+627d,U+6281-6283,U+6285-6288,U+628c,U+628e-628f,U+6294,U+6299,U+629c,U+629e,U+62a6,U+62a9-62aa,U+62ad-62b0,U+62b3-62b4,U+62b6,U+62b8,U+62be,U+62c3,U+62cb,U+62cf,U+62d1,U+62d5,U+62dd,U+62e0-62e1,U+62e4,U+62eb,U+62f0,U+62f2,U+62f5,U+62f8-62fb,U+6300,U+6303,U+630a-630d,U+630f-6310,U+6313-6315,U+6318-6319,U+6326-6327,U+6329,U+632c-632d,U+6333-6336,U+6338,U+633b-633c,U+633e-6341,U+6344,U+6347-6348,U+634a,U+6351,U+6354,U+6356-635a,U+635c,U+6365,U+6368,U+636b-636c,U+636f-6370,U+6372,U+6374-6375,U+6378,U+637c-637d,U+6381,U+6383-6385,U+638d,U+6391,U+6394,U+6397,U+6399,U+639b-639f,U+63a1,U+63a4,U+63ab,U+63af,U+63b1-63b2,U+63b5,U+63bb,U+63bd,U+63c0,U+63c2-63c3,U+63c5,U+63c7-63c8,U+63ca-63cc,U+63d1,U+63d3-63d5,U+63d7-63dd,U+63df,U+63e4-63e5,U+63e7-63e8,U+63eb-63ec,U+63ee-63f1,U+63f3,U+63f5,U+63f7,U+63f9-63fa,U+6406,U+6409-640a,U+640d-640e,U+6412,U+6415;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/9ec494718991ad58043360c6519917d9.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+611d-611e,U+6121,U+6128-6129,U+612c-6130,U+6132,U+6134,U+6136-6137,U+613a-613e,U+6140-6142,U+6144-6147,U+6149,U+614b,U+614d,U+614f,U+6152-6154,U+6156,U+6158-615b,U+615e-6161,U+6163-6166,U+616a-616f,U+6171-6174,U+6176,U+6179-617a,U+617c,U+617e,U+6180,U+6182-6183,U+6187,U+6189-618a,U+618c-618d,U+6190-6193,U+6195-619b,U+619f,U+61a1-61a2,U+61a4-61a5,U+61aa-61ab,U+61ad-61b6,U+61b8-61ba,U+61bc,U+61bf,U+61c1,U+61c3,U+61c5-61c7,U+61c9,U+61cc-61cd,U+61d0,U+61d5-61d6,U+61d8,U+61dd-61e0,U+61e3-61e5,U+61e7-61eb,U+61ed-61ee,U+61f0-61f2,U+61f4,U+61f6-61fe,U+6200-6201,U+6203-6204,U+6207,U+6209,U+6213-6214,U+6219,U+621d;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/86e3a0885fb26630baf4a6b5a2d0da02.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+5f37-5f38,U+5f3b,U+5f3d-5f3e,U+5f41,U+5f43-5f4c,U+5f4e-5f4f,U+5f51,U+5f54,U+5f59,U+5f5c,U+5f5f,U+5f63,U+5f65,U+5f67,U+5f6b,U+5f6f,U+5f72,U+5f74,U+5f76,U+5f78,U+5f7d-5f7f,U+5f83,U+5f86,U+5f8f,U+5f91,U+5f93,U+5f96,U+5f9b,U+5f9e-5fa0,U+5fa2,U+5fa4-5fa7,U+5fa9,U+5fab-5fac,U+5faf,U+5fb2-5fb4,U+5fb6,U+5fb8-5fb9,U+5fbb,U+5fbe-5fc1,U+5fc7,U+5fcb,U+5fd3-5fd5,U+5fdc,U+5fde,U+5fe2-5fe3,U+5fe5,U+5fe8-5fe9,U+5fef-5ff0,U+5ff3-5ff4,U+5ff7,U+5ffa,U+5ffc,U+6009,U+600b-600c,U+6010-6011,U+6013,U+6017-6018,U+601a,U+601e,U+6022,U+6024,U+602c-602e,U+6031-6034,U+6037,U+6039-603a,U+6040,U+6044-6047,U+6049-604a,U+604c,U+6053-6054,U+6058,U+605b,U+605e-6061,U+6065-6066,U+606e,U+6072,U+6075,U+6077,U+6080-6081,U+6085-6088,U+608a-608b,U+608e,U+6090,U+6095,U+6097,U+609c,U+609e,U+60a2,U+60a4,U+60a7,U+60a9-60aa,U+60b0,U+60b3,U+60b5-60b7,U+60b9-60ba,U+60bd-60c1,U+60c3-60c4,U+60c7-60c9,U+60cc-60cf,U+60d3-60d4,U+60d9,U+60db,U+60de,U+60e1-60e4,U+60f1-60f2,U+60f5,U+60f7-60f8,U+60fb-60fc,U+60fe-60ff,U+6103-6105,U+610a-610b,U+6110-6114,U+6116,U+6118-6119,U+611b-611c;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/a5134007e4baa355386620b2d0bec94e.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+5db5,U+5db8-5dba,U+5dbc-5dbd,U+5dc0,U+5dc2-5dc3,U+5dc6-5dc7,U+5dc9,U+5dcb-5dcc,U+5dcf-5dd8,U+5ddf-5de0,U+5de3-5de4,U+5df0,U+5df5,U+5df8-5df9,U+5dfb,U+5dff-5e00,U+5e04,U+5e0a-5e0b,U+5e0e,U+5e12,U+5e17,U+5e1f-5e25,U+5e28-5e29,U+5e2b,U+5e2f-5e30,U+5e33-5e34,U+5e36,U+5e3e,U+5e40-5e41,U+5e43,U+5e47,U+5e49-5e4b,U+5e4d-5e4f,U+5e53,U+5e56-5e59,U+5e5c-5e5d,U+5e5f-5e60,U+5e63-5e64,U+5e66-5e70,U+5e75,U+5e77,U+5e79,U+5e7e,U+5e81-5e83,U+5e88-5e89,U+5e8c-5e8d,U+5e9b,U+5ea2-5ea4,U+5ea8,U+5eaa-5eac,U+5eae,U+5eb0-5eb2,U+5eb4,U+5ebc,U+5ebf,U+5ec1-5ec8,U+5ecb-5ecc,U+5ece-5ed0,U+5ed4-5ed5,U+5ed7-5eda,U+5edc-5ee3,U+5ee5-5ee7,U+5ee9,U+5eec,U+5eee-5ef3,U+5ef8-5ef9,U+5efb-5efd,U+5f05-5f07,U+5f09,U+5f0c-5f0e,U+5f10,U+5f12,U+5f14,U+5f16,U+5f1a,U+5f1c-5f1e,U+5f21-5f24,U+5f28,U+5f2e,U+5f30,U+5f33-5f36;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/1cf06d87594db707e9babd95853a94ff.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+5c5f,U+5c62,U+5c64,U+5c67-5c6a,U+5c6c-5c6d,U+5c70,U+5c73-5c74,U+5c76,U+5c7b-5c7c,U+5c7e,U+5c86,U+5c89-5c8b,U+5c8f,U+5c92-5c93,U+5c95,U+5c9d-5ca1,U+5ca4-5ca8,U+5caa,U+5cae-5cb0,U+5cb6,U+5cba-5cbc,U+5cbe,U+5cc2-5cc3,U+5cc5-5cca,U+5ccc,U+5cce-5cd0,U+5cd3-5cd4,U+5cd6-5cd8,U+5cda-5cdb,U+5cde-5ce0,U+5ce3,U+5ce7,U+5ce9,U+5cec,U+5cee-5cef,U+5cf1,U+5cf4-5cfa,U+5cfd,U+5cff-5d01,U+5d04,U+5d0b-5d0d,U+5d0f-5d12,U+5d15,U+5d17-5d1a,U+5d1d,U+5d1f-5d23,U+5d25,U+5d28,U+5d2b-5d2c,U+5d30-5d33,U+5d35-5d3a,U+5d3c,U+5d3f-5d43,U+5d45-5d46,U+5d49,U+5d4e,U+5d50-5d53,U+5d55-5d56,U+5d59-5d5a,U+5d5c,U+5d5e,U+5d61-5d63,U+5d65,U+5d67-5d68,U+5d6a,U+5d6d,U+5d70-5d73,U+5d76-5d77,U+5d79-5d7a,U+5d7c-5d81,U+5d84-5d8d,U+5d90,U+5d92-5d95,U+5d97,U+5d9a,U+5d9c,U+5d9e-5da2,U+5da4,U+5da6-5daa,U+5dac-5db2,U+5db4;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/cd5f0463771f4504e2b06cf3f0148535.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+5ad0,U+5ad5,U+5ad7,U+5ad9-5adb,U+5add-5adf,U+5ae2,U+5ae5,U+5ae8,U+5aea,U+5aec-5aee,U+5af0,U+5af3-5afb,U+5afd,U+5aff-5b03,U+5b05,U+5b07-5b08,U+5b0b-5b0c,U+5b0f-5b10,U+5b13-5b14,U+5b19-5b1b,U+5b1d-5b1e,U+5b20-5b28,U+5b2a,U+5b2c-5b30,U+5b36,U+5b38,U+5b3c-5b3f,U+5b41,U+5b43,U+5b45,U+5b47-5b48,U+5b4b-5b4e,U+5b52,U+5b56,U+5b68,U+5b6b,U+5b6e-5b6f,U+5b72,U+5b76-5b78,U+5b7b-5b7c,U+5b7e-5b7f,U+5b82,U+5b8a,U+5b8d-5b8e,U+5b92,U+5b96,U+5b9f,U+5ba7-5ba8,U+5bac-5bae,U+5bc0-5bc1,U+5bc3,U+5bc9-5bcb,U+5bcd-5bce,U+5bd1,U+5bd4,U+5bd6,U+5bd8-5bd9,U+5bdb,U+5be0,U+5be2-5be3,U+5be6-5be7,U+5be9-5bec,U+5bef,U+5bf1-5bf3,U+5bf5-5bf6,U+5bfd-5bfe,U+5c02-5c03,U+5c05,U+5c07-5c08,U+5c0b-5c0e,U+5c10,U+5c12-5c13,U+5c19,U+5c1e-5c20,U+5c23,U+5c28-5c2b,U+5c2d,U+5c30,U+5c33,U+5c37,U+5c43-5c44,U+5c46-5c47,U+5c4c-5c4d,U+5c53-5c54,U+5c56,U+5c58,U+5c5b-5c5d;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/898ec7aabb5691350f4da5aa1d051c60.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+5922,U+592c,U+5932,U+593d-593e,U+5940,U+5945-5946,U+594a,U+5950,U+5953,U+595b-595d,U+595f,U+5961,U+5963,U+5967-596e,U+5970-5972,U+5975,U+5977,U+597b-597c,U+597e-5980,U+5985,U+598b,U+598e-5990,U+5998,U+599b,U+599d,U+599f-59a2,U+59a6-59a7,U+59ac-59ad,U+59b1,U+59b3-59b6,U+59ba,U+59bc-59bd,U+59c0-59c1,U+59c3,U+59c5,U+59c7-59c9,U+59cc-59cf,U+59d6,U+59d9,U+59db,U+59de,U+59e0-59e1,U+59e4,U+59e6-59e7,U+59e9-59eb,U+59ed-59ee,U+59f1-59f8,U+59fa,U+59fc-59fe,U+5a00,U+5a0a,U+5a0d,U+5a0f,U+5a15-5a17,U+5a19-5a1b,U+5a1e,U+5a27,U+5a2d-5a2f,U+5a33,U+5a35,U+5a37-5a39,U+5a3e,U+5a41-5a44,U+5a47-5a48,U+5a4c-5a4d,U+5a50-5a53,U+5a56-5a58,U+5a5b-5a60,U+5a63-5a66,U+5a69,U+5a6b-5a6d,U+5a70,U+5a73,U+5a78,U+5a7b-5a7e,U+5a82-5a84,U+5a86,U+5a8a-5a8c,U+5a8e-5a90,U+5a93-5a97,U+5a9c-5aa0,U+5aa2,U+5aa5-5aa7,U+5aa9,U+5aac-5ab1,U+5ab4,U+5ab6-5ab7,U+5ab9-5abd,U+5abf-5ac0,U+5ac4,U+5ac6-5ac8,U+5aca-5acb,U+5acd;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/7ea3adfa4898fa8d5a24b5215a7df86c.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+5803-5805,U+5808-580a,U+580c,U+580e,U+5810,U+5814,U+5816,U+581b-581d,U+5823,U+5825,U+5827-5829,U+582c-582f,U+5831-5834,U+5836-5841,U+5845-5846,U+5848-584b,U+584e-584f,U+5852-5853,U+5855,U+5857,U+5859-585b,U+585d,U+5861-5864,U+5867-5869,U+586d,U+586f-5872,U+5874-5876,U+5879-587d,U+587f,U+5882,U+5886-5888,U+588a-588b,U+588d-5891,U+5894-5895,U+5897-5898,U+589c-589d,U+58a0-58a1,U+58a3,U+58a5-58a6,U+58aa-58ac,U+58ae-58af,U+58b1-58b3,U+58b8-58bb,U+58bd-58bf,U+58c2-58c3,U+58c6-58ca,U+58cc-58d0,U+58d2-58d4,U+58d6-58e0,U+58e2-58e3,U+58e5,U+58e7-58e9,U+58ef,U+58f1-58f2,U+58f4,U+58f7-58f8,U+58fa-58ff,U+5903,U+5905-5906,U+5909-590c,U+590e,U+5910,U+5912,U+5917-5918,U+591b,U+5920;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/f18ae185bf018ef06cf325ee8c3b7240.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+566e-5670,U+5672-5674,U+5677-567a,U+567e-5684,U+5687-5688,U+568a,U+568c-568d,U+5690,U+5694-5695,U+5697-569a,U+569c-569e,U+56a0,U+56a2,U+56a5-56ae,U+56b1-56b6,U+56bd-56be,U+56c0-56c3,U+56c5-56c6,U+56c8-56c9,U+56cb-56ce,U+56d1,U+56d3,U+56d8,U+56e3,U+56e5,U+56e7-56e8,U+56ea,U+56ee-56ef,U+56f2-56f3,U+56f6-56f7,U+5700-5702,U+5707,U+570b-570d,U+570f-5710,U+5712-5716,U+5718-571b,U+571e,U+5720-5722,U+5726-5727,U+572b,U+5732,U+5734,U+5737-5738,U+5741,U+5745-5746,U+5749,U+574b,U+5752,U+5759,U+5762,U+5765,U+576c,U+5770-5772,U+5774-5775,U+577d,U+577f-5780,U+5788-5789,U+578d-578f,U+5794-5795,U+5797-579a,U+579c-579f,U+57a5,U+57a8,U+57aa,U+57ac,U+57af-57b1,U+57b3,U+57b5-57b6,U+57b9-57ba,U+57bc-57c1,U+57c6-57c8,U+57cc,U+57d0,U+57d3,U+57d6-57d7,U+57dc,U+57de,U+57e1-57e3,U+57e5-57e7,U+57e9-57ec,U+57ee,U+57f0-57f3,U+57f5-57f7,U+57fb-57fc,U+57ff,U+5801;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/49b891537f8953fc088687b7ed4341c6.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+5460-5461,U+5463,U+5465,U+5467,U+546a-546c,U+546f-5470,U+5474,U+547a,U+547e-547f,U+5481,U+5487-548a,U+548d,U+5491,U+5498,U+549c,U+54a0-54a2,U+54a5,U+54ae,U+54b0,U+54b2,U+54b6-54b7,U+54b9-54ba,U+54bc,U+54be,U+54c3,U+54c5,U+54d6,U+54d8,U+54e0-54e2,U+54e4,U+54eb-54ec,U+54ef,U+54f1,U+54f7-54f8,U+54fb,U+5500,U+5503-5505,U+5508,U+550a-550c,U+550e,U+5512,U+5516-5517,U+551a,U+551d,U+5526,U+552b,U+552d,U+5532,U+5534-5536,U+5538-5539,U+553b,U+5540,U+5545,U+5547-5548,U+554b-554f,U+5551-5553,U+5557,U+555d-5560,U+5562-5563,U+556b,U+5570,U+5574,U+557d,U+557f,U+5586,U+558c-558e,U+5592-5593,U+5595,U+559a,U+559e,U+55a1-55a6,U+55a8-55ae,U+55b0,U+55b2,U+55b6,U+55bf-55c0,U+55c2-55c3,U+55c6-55c8,U+55ca-55cb,U+55ce-55d0,U+55d5,U+55d9-55db,U+55de,U+55e2,U+55e7,U+55e9,U+55f6,U+55f9-55fa,U+55fc,U+55ff,U+5602,U+5604,U+5606-5607,U+560d,U+5610,U+5612-5617,U+561a,U+561c-561d,U+5620-5621,U+5628-562a,U+562e-5630,U+5633,U+5635,U+5637-5638,U+563a,U+563d-563e,U+5640-5642,U+5645-564b,U+564f-5650,U+5653,U+565a-565b,U+565d-565e,U+5660,U+5663,U+5665-5666,U+566d;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/74dc5f6968685dc2868a52ab9c4e2865.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+5221,U+5225,U+5227,U+522a,U+522c,U+5231-5232,U+5235,U+5244-5247,U+5249,U+524b,U+524e-524f,U+5252,U+5255,U+5257,U+525a-525b,U+525d,U+525f,U+5262-5264,U+5266,U+526b-526e,U+5270-5271,U+5273-5275,U+5277-5278,U+527a-527c,U+5280,U+5283-5284,U+5287,U+5289-528d,U+5291-5292,U+5294,U+5296-5299,U+529c,U+52a4,U+52a6,U+52ae-52af,U+52b4-52b5,U+52b9-52bc,U+52c0-52c2,U+52c5,U+52c8,U+52ca,U+52cc-52cd,U+52d1,U+52d3-52d5,U+52d7,U+52d9-52db,U+52dd-52de,U+52e0,U+52e2-52e3,U+52e6-52e7,U+52e9,U+52eb,U+52ef,U+52f1-52f5,U+52f7-52f8,U+52fb-52fc,U+5301-5302,U+5307,U+5309-530b,U+530e,U+5311-5312,U+531c,U+531f,U+5322,U+5324,U+532d,U+532f-5331,U+5333-5335,U+5337-5338,U+533c-533d,U+5340,U+5342,U+5346,U+534b-534d,U+5354,U+5358,U+536c,U+5372,U+5379,U+537b-537d,U+538a,U+538e-538f,U+5392-5394,U+5396-5397,U+5399,U+539c,U+539e,U+53a0,U+53a4,U+53a7,U+53ab-53ad,U+53b0,U+53b2-53b4,U+53b7,U+53b9,U+53be,U+53c0,U+53c3,U+53c6-53c7,U+53ce,U+53d5,U+53da,U+53dd,U+53e1-53e2,U+53f4,U+53fa,U+5407,U+540b,U+5418-5419,U+541c,U+5424-5425,U+542a,U+5430,U+5433,U+5436-5437,U+543d,U+543f,U+5441-5442,U+5445,U+5447,U+5449,U+544d-544f,U+5451,U+545f;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/c4b0630b79f100fc102dddf717079638.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+50ad-50b1,U+50b3-50b8,U+50bc-50bf,U+50c1-50c2,U+50c4-50ce,U+50d0-50d1,U+50d3-50d5,U+50d7-50d9,U+50db,U+50dd-50e1,U+50e3-50e5,U+50e8-50ea,U+50ef-50f2,U+50f4,U+50f6,U+50f8-50f9,U+50fc-5105,U+5108-510a,U+510c-510e,U+5110-5111,U+5113-5118,U+511a-511c,U+511e-5120,U+5122,U+5124-5126,U+5129-512b,U+512d-512e,U+5130-5135,U+5137-513d,U+5147,U+514a,U+514c,U+514e,U+5150,U+5152,U+5157,U+5159,U+515b,U+515d-5161,U+5163-5164,U+5167,U+5169-516a,U+5183,U+5186-5187,U+518a-518b,U+518f-5191,U+5193-5194,U+5198,U+519d-519e,U+51a1,U+51a3,U+51a6,U+51a8-51aa,U+51ad-51ae,U+51b4,U+51b8-51b9,U+51be,U+51c3,U+51c5,U+51c8,U+51ca,U+51cd-51ce,U+51d0,U+51d2-51d4,U+51d6-51d8,U+51dc,U+51de-51df,U+51e2,U+51e6-51e7,U+51e9-51ea,U+51ec,U+51ee,U+51f1,U+51f4,U+51fe,U+5204,U+5209,U+520b-520c,U+5210,U+5213-5215,U+521c,U+521e;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/a534d38dd367540fd2e026ad5860bdb6.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+4f12-4f16,U+4f1c-4f1d,U+4f23,U+4f28-4f29,U+4f2c-4f2d,U+4f33,U+4f37,U+4f39,U+4f3b,U+4f3e-4f3f,U+4f41-4f42,U+4f45,U+4f47-4f49,U+4f4b-4f4c,U+4f52,U+4f54,U+4f56,U+4f61-4f62,U+4f6a-4f6b,U+4f6e,U+4f72,U+4f75,U+4f77-4f7a,U+4f7d,U+4f80-4f82,U+4f85-4f87,U+4f8a,U+4f90,U+4f92,U+4f95-4f96,U+4f98,U+4f9a,U+4f9c,U+4f9e,U+4fa1-4fa2,U+4fab,U+4fad,U+4fb0,U+4fb2-4fb4,U+4fb6-4fb7,U+4fb9-4fbb,U+4fbd-4fbe,U+4fc0-4fc2,U+4fc7-4fc9,U+4fcb-4fcd,U+4fd2-4fd4,U+4fd6,U+4fd9,U+4fdb,U+4fe0,U+4fe4-4fe5,U+4feb-4fec,U+4ff0,U+4ff2,U+4ff4-4ff7,U+4ff9,U+4ffd,U+4fff-5003,U+5005-5009,U+500b,U+500e,U+5010-5011,U+5013,U+5015-5017,U+501b,U+501e,U+5020,U+5022-5024,U+5027,U+502b,U+502f-5031,U+5033-5037,U+5039,U+503b,U+5040-5042,U+5045-5046,U+5049-504b,U+504d,U+5050-5051,U+5053,U+5056-5058,U+505b,U+505d-5064,U+5066,U+5068-506b,U+506d-5070,U+5072-5075,U+5078,U+507a,U+507d,U+5081-5083,U+5087,U+5089,U+508b-508c,U+508e-5096,U+5098-509e,U+50a2,U+50a6,U+50aa;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/77089e1e56c37d44c7c8431325aac443.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+44eb-44ec,U+4508,U+450d,U+4525,U+4543,U+457a,U+459d,U+45b8,U+45d6,U+45db,U+45e5,U+45ea,U+45f4,U+460f,U+4641,U+4665,U+46a1,U+46ae-46af,U+470c,U+4723,U+4729,U+4759,U+4764,U+47e6,U+47fd,U+4816,U+4844,U+484e,U+48b5,U+48ba,U+48bc,U+48d8,U+497d,U+4983,U+4986,U+49b0,U+49e7,U+49fa,U+4a04,U+4a29,U+4abc,U+4b38,U+4b3b,U+4bc2,U+4bca,U+4bd2,U+4be8,U+4c17,U+4c20,U+4c9f-4ca2,U+4ca4,U+4cc4,U+4cd1,U+4d07,U+4d13-4d19,U+4d77,U+4dae,U+4dc0-4dff,U+4e02,U+4e04-4e05,U+4e0f,U+4e12,U+4e17,U+4e1f,U+4e21,U+4e26,U+4e29,U+4e2e-4e2f,U+4e31,U+4e33,U+4e37,U+4e3c,U+4e40-4e42,U+4e44,U+4e51,U+4e55,U+4e57,U+4e5a-4e5b,U+4e62,U+4e6a,U+4e78,U+4e7f-4e85,U+4e87,U+4e8a,U+4e96,U+4e99,U+4e9c-4e9e,U+4ea3,U+4eb0,U+4eb6-4eb9,U+4ebc,U+4ec8,U+4ecf-4ed0,U+4eda,U+4edc,U+4ee0,U+4ee9,U+4eed-4eee,U+4ef1,U+4ef4,U+4ef8,U+4efc,U+4f00,U+4f02-4f05,U+4f08,U+4f0b;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/ab9b55cde38a09871002bbdcc4cca496.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+33c2,U+33c4,U+33c8,U+33cb-33cd,U+33d4,U+33d7-33d8,U+33da,U+3402,U+3405-3406,U+3427,U+342c,U+342e,U+3447,U+344a,U+3468,U+346a,U+3488,U+3492,U+34b5,U+34bc,U+34c1,U+34c7,U+34db,U+351f,U+353e,U+355d-355e,U+3563,U+356e,U+359e,U+35a6,U+35a8,U+35c5,U+35da,U+35f4,U+3605,U+360e,U+364a,U+364d,U+3658,U+3666,U+3691,U+3696,U+3699,U+36c3,U+36cf,U+36da,U+36f9,U+3761-3762,U+376b-376c,U+3775,U+378d,U+37c1,U+37c3,U+37e2,U+37e8,U+37f4,U+37fd,U+3800,U+3807,U+3813,U+382f,U+3836,U+3840,U+385c,U+3861,U+38fa,U+3917-3918,U+391a,U+3944,U+396f,U+39d0-39d1,U+39df,U+3a6e,U+3a73,U+3ad6-3ad7,U+3aea,U+3af0,U+3af3,U+3b0a,U+3b0e,U+3b1a,U+3b1c,U+3b22,U+3b4e,U+3b55,U+3b6d,U+3b77,U+3b87-3b88,U+3b8d,U+3ba4,U+3bb6,U+3bbe,U+3bc3,U+3bcd,U+3bf0,U+3c00,U+3c0f,U+3c26,U+3cc3,U+3cc7,U+3cd2,U+3cd8,U+3cda,U+3d11,U+3d14,U+3d1e,U+3d4e,U+3d50,U+3d64,U+3d9a,U+3db2,U+3dc0,U+3dd4,U+3e05-3e06,U+3e0c,U+3e3f,U+3e60,U+3e66,U+3e68,U+3e83-3e84,U+3e8a,U+3e94,U+3eda,U+3eec,U+3f4f,U+3f57,U+3f72,U+3f75,U+3f77,U+3fae,U+3fb1,U+3fc9,U+3fd7,U+3fe0,U+4039,U+4056,U+4058,U+4093,U+40ae,U+40c5,U+40ce,U+4103,U+4105,U+4148,U+414f,U+415f,U+4163,U+41b4,U+41bf,U+41e6,U+41ee,U+41f3,U+4207,U+420e,U+4264,U+4293,U+42c6,U+42d6,U+42dd,U+4302,U+432b,U+4337,U+4339,U+4343,U+4383,U+4396,U+43dd,U+43e1,U+43ee,U+43f0,U+43f2,U+4403,U+4408,U+440c,U+4417,U+441c,U+4422,U+4453,U+445b,U+4476,U+447a,U+4491,U+44b3,U+44be,U+44d4,U+44d6,U+44db,U+44e8;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/ed944b5c65814b70992de0aa17e3c11e.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+32a0-32bf,U+32d0-332b,U+332d-3357,U+3371,U+337b-337f,U+3385-3389,U+338d-3390,U+3396-3398,U+339b-33a6,U+33b0-33b3;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/7b27633967bbf671d3d9275c6530880e.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+30df-30fe,U+3105-312d,U+31c0-31e3,U+3220-3243,U+3248-324f,U+3251-325f,U+327f-329f;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/1d0cdc463a99c704ad792f399c56d4b7.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+2fc1-2fd5,U+3000,U+3003-3007,U+3012-3013,U+301c-301d,U+301f-3029,U+3030,U+3033-3036,U+3038-303b,U+3041-3094,U+309b-309e,U+30a1-30de;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/61bf6426e3b3ea3ffc277b8da76a57ab.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+2eec-2ef3,U+2f00-2fc0;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/46ddfcb0c32ceedd813bbeeab8fe6cc4.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+25c7,U+25c9-25cc,U+25ce-25d3,U+25e2-25e6,U+25ef,U+2600-2603,U+2605-2606,U+260e,U+2612,U+2616-2617,U+261c-261f,U+2630-2637,U+2640,U+2642,U+2660-266f,U+2686-268f,U+26e4,U+26e7,U+2702,U+2713,U+271a,U+273f-2740,U+2756,U+2776-277f,U+27a1,U+2934-2935,U+29bf,U+29c9,U+29fa-29fb,U+2b07,U+2bba,U+2e80-2e99,U+2e9b-2eeb;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/e4a713306eaee5083ea04f1c58fa988e.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+24ae-254b,U+2550,U+255e,U+2561,U+256a,U+256d-2573,U+2581-258f,U+2594-2595,U+25a0-25a2,U+25aa-25ab,U+25b1-25b3,U+25b6-25b7,U+25bc-25bd,U+25c0-25c1,U+25c6;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/84441e6724d06d354fdfd5f56ab26486.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+4e0c,U+4e28,U+4e36,U+4ec2,U+4f91,U+502e,U+50ba,U+5181,U+522d,U+5290,U+52f9,U+5369,U+53a3,U+54d5,U+54dc,U+54ff,U+552a,U+5588,U+55b5,U+5686,U+5776,U+5786,U+57a4,U+57f4,U+5820,U+5865,U+58bc,U+5902,U+5ab5,U+5b32,U+5b65,U+5c1c,U+5c22,U+5c66,U+5c6e,U+5d3e,U+5ddb,U+5f2a,U+5f50,U+5fed,U+619d,U+61d4,U+620b,U+6224-6225,U+6343,U+63ce,U+63f2,U+640b-640c,U+6496,U+64d0,U+6509,U+652e,U+6603,U+67a8,U+6833,U+6844,U+684a,U+6957,U+6971,U+6a65,U+6a91,U+6bea,U+6cd0,U+6d48,U+6f24,U+6f2d,U+706c,U+70c0,U+721d,U+728b,U+72c3,U+72f3,U+730a,U+7338-7339,U+734d,U+736f,U+752f,U+75c4,U+762d,U+770d,U+7735,U+77a2,U+7839,U+784e,U+786d,U+791e,U+7924,U+7953,U+7a80,U+7b58,U+7bda,U+7cc8,U+7f0f,U+7f12,U+7f33,U+7f68,U+8022,U+8028-8029,U+8035,U+804d,U+8080,U+80e9,U+80ec,U+80f2,U+8221,U+8223,U+822d,U+825a,U+8274,U+82b0,U+82e0,U+8307,U+8347,U+83b0,U+83f9,U+8487-8488,U+8539,U+857a,U+85a8,U+867c,U+86b0,U+86d8,U+8788,U+8803,U+89cc,U+8ba0,U+8c85,U+8c8a,U+8c98,U+8dbc,U+8e23,U+8e40,U+8e94,U+8f77,U+8f79,U+9058,U+90b6,U+914e,U+91a2,U+91b5,U+943e,U+9494,U+94b6,U+94b8,U+94de,U+94f9,U+950a,U+950e,U+951e,U+9538,U+953e-953f,U+9544,U+955f,U+9561,U+9569,U+95f6,U+9603,U+960d,U+963d,U+9794,U+97ab,U+9a96,U+9ab1,U+9ad1,U+9b0f,U+9b2f,U+9c92,U+9c95,U+9cbc,U+9cc6,U+9cd0,U+9cd8,U+9e38,U+9e3a,U+9e5b,U+9e7e,U+9eea,U+9ef9,U+9efb-9efc,U+9fa0;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/ff7de8d1050c9658ed78cf83af60c5a9.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+2177-217b,U+217f,U+2190,U+2192,U+2194-2199,U+21c4-21c6,U+21cb-21cc,U+21d0,U+21d2,U+21d4,U+21e6-21e9,U+2200,U+2202-2203,U+2205-220b,U+220f,U+2211,U+2213,U+2219-221a,U+221d-2220,U+2225-222e,U+2234-2235,U+223c-223d,U+2243,U+2245,U+2248,U+2252,U+2260-2262,U+2264-2267,U+226a-226b,U+2272-2273,U+2276-2277,U+2282-2287,U+228a-228b,U+2295-2298,U+229d-229e,U+22a0,U+22a5,U+22bf,U+22da-22db,U+22ee-22ef,U+2300,U+2305-2307,U+2312,U+2318,U+23be-23cc,U+23ce,U+23fb-23fc,U+2423,U+244a,U+2460-24ad;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/e39a3df0da7ba1d7235e0bae93d7cba2.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+4ea0,U+4f74,U+4f94,U+4fc5,U+507e,U+50ed,U+5182,U+51f5,U+525e,U+5282,U+5326,U+537a,U+5423,U+5459,U+54b4,U+54d9,U+55c9,U+572f,U+574c,U+580b,U+5925,U+5a08,U+5b16,U+5b84,U+5be4,U+5cb5,U+5cbd,U+5e31,U+5e5e,U+5e80,U+5ee8,U+5f82,U+5fc9,U+600a,U+605d,U+609b,U+609d,U+60dd,U+6243,U+6322,U+63de,U+63f8,U+643f,U+6445,U+64d7,U+6534,U+6549,U+656b,U+674c,U+680a,U+6864,U+69d4,U+6c2a,U+6c46,U+6c5c,U+6d0e,U+6e2b,U+6eb2,U+6eb7,U+6f89,U+7039,U+70b1,U+7113,U+71d4,U+727f,U+72f4,U+7303,U+7321,U+736c,U+74a9,U+74de,U+750d,U+7513,U+7592,U+7605,U+760a,U+761b,U+7625,U+7643,U+764d,U+7707,U+7747,U+77b5,U+78a5,U+793b,U+798a,U+7a03,U+7a06,U+7a78,U+7aac-7aad,U+7ab3,U+7b0a,U+7ba8,U+7be5,U+7c40,U+7ca2,U+7d77,U+7ea1,U+7ec1,U+7f0b,U+7f2f,U+7f9d,U+8025,U+809c,U+80ad,U+80b7,U+80e8,U+811e,U+8204,U+822f,U+8233,U+823b,U+824b,U+827d,U+827f,U+828f,U+82c8,U+831b,U+837d,U+839b,U+83a9,U+83dd,U+8406,U+84b9,U+84e3,U+8579,U+864d,U+867f,U+86d1,U+86f2,U+8764,U+8770,U+8797,U+87ac-87ad,U+87b5,U+87bd,U+881b,U+8844,U+88bc,U+88fc,U+8930,U+895e,U+89cf,U+89d6,U+8bd4,U+8c02,U+8c2b,U+8db1,U+8dd6,U+8f81-8f82,U+8fd5,U+90db,U+9164,U+91ad,U+94b7,U+94d8,U+94eb,U+950d,U+9514,U+9516,U+9518,U+9529,U+954e,U+95fc,U+963c,U+9667,U+966c,U+96b3,U+9792,U+97b2,U+98a1,U+9969,U+996b,U+9987,U+9998,U+9a75,U+9a7a,U+9a80,U+9a92,U+9adf,U+9b43,U+9cb4,U+9cbd,U+9cd4,U+9e31,U+9e4e,U+9e71,U+9ee5;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/57cdefb1ae1ce6eeaabd1bdb9b30ca26.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+2d8-2d9,U+2db,U+2dd,U+2e5-2e9,U+300-303,U+305-306,U+30a-30c,U+327,U+332,U+336,U+361,U+384-385,U+391-3a1,U+3a3-3a9,U+3b1-3c1,U+3c3-3c9,U+401,U+410-44f,U+451,U+45b,U+2070,U+2075-2079,U+2080-2089,U+20dd-20de,U+2100,U+2103,U+2105,U+2109-210a,U+210f,U+2116,U+2121,U+2126-2127,U+212b,U+212e,U+2135,U+2153-215e,U+2160-216b,U+2170-2176;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/5a2a9663692b196d8cae8489b88ab053.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+4ef3,U+4f1b,U+4f7e,U+50d6,U+50ec,U+51ab,U+51b1,U+52d6,U+536e,U+54a9,U+54da,U+55be,U+55cd,U+564d,U+576b,U+57d8,U+57f8,U+57fd,U+5844,U+59d2,U+5ae0,U+5b37,U+5b5b,U+5b80,U+5d1e,U+5d6b,U+5efe,U+5f11,U+5f56,U+5f58,U+5f73,U+5f8c,U+5fc4,U+5fe4,U+602b,U+6106,U+610d,U+641b,U+64e4,U+6634,U+66db,U+676a,U+67b5,U+681d,U+6883,U+69b1,U+69e0,U+6b37,U+6b39,U+6b9b,U+6d7c,U+6ed7,U+6f36,U+6f72,U+6f8c,U+7035,U+7173,U+7178,U+7228,U+728f,U+72b4,U+72ef,U+7331,U+7481,U+74e0,U+7540,U+755b,U+758b,U+75c3,U+75e6,U+7635,U+763c,U+76cd,U+7704,U+7743,U+7780,U+7847,U+786a,U+78b9,U+7962,U+7a02,U+7b4c,U+7b7b,U+7bfc,U+7c0f,U+7c16,U+7cc7,U+7cf8,U+7e3b,U+7ea9,U+7ef2,U+7f02,U+7f07,U+7f0c,U+7f23,U+7fbc,U+8016,U+8020,U+812c,U+8136,U+8182,U+825f,U+8268,U+8284,U+8288,U+8291,U+8308,U+8311,U+831a,U+835b,U+836d,U+840f,U+845c,U+84b4,U+850c,U+855e,U+85a4,U+863c,U+86ba,U+86c4,U+86de,U+86f1,U+873e,U+87db,U+880a,U+883c,U+8864,U+887f,U+88f0,U+890a,U+892b,U+89ef,U+8a48,U+8bdc,U+8c18,U+8c33,U+8c94,U+8dcf,U+8de3,U+8e6f,U+8e90,U+8f7a,U+8f7e,U+8fb6,U+8fd3,U+902d,U+90be,U+91af,U+936a,U+948b,U+9513,U+953a,U+956c,U+9654,U+9688,U+972a,U+97b4,U+9aa3,U+9aba,U+9aed,U+9af9,U+9b08,U+9c8e,U+9c94,U+9c9a,U+9e2b,U+9e36,U+9e4b,U+9e55,U+9e63,U+9e68-9e69,U+9ebd,U+9ec9,U+9f0d,U+9f2c,U+9f37,U+9f51;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/ee6f96839ba28a4354c0ce4eec144bb8.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+4e69,U+4f67,U+4fdc,U+50e6,U+5196,U+5202,U+5233,U+523f,U+52a2,U+5476,U+54ad,U+54cf,U+5537,U+561e,U+56dd,U+56df,U+5709,U+572c,U+57cf,U+580d,U+5881,U+589a,U+5941,U+59b2,U+5c25,U+5d24,U+5d74,U+5e42,U+5e8b,U+5eb3,U+5ed2,U+5fad,U+6003,U+603c,U+6083,U+6100,U+6126,U+6206,U+62ca,U+638e,U+63b4,U+6426,U+646d,U+6535,U+65c4,U+6715,U+6769,U+6798,U+67c3,U+6861,U+698d,U+69ca,U+69ed,U+69f2,U+69ff,U+6a18,U+6bb3,U+6c0d,U+6cb2,U+6cd6,U+6cf7,U+6cfa,U+6d33,U+6e16,U+6e53-6e54,U+6ebb,U+6fb6,U+709d,U+72ad,U+72f7,U+72fb,U+7313,U+739f,U+74ba,U+754b,U+75ac,U+75d6,U+7617,U+7640,U+76a4,U+76b2,U+775a,U+77bd,U+781f,U+79b3,U+7b2b,U+7b31,U+7b3e,U+7b6e,U+7b9c,U+7bcc,U+7c0b,U+7c9e,U+7cc1,U+7ce8,U+7ea5,U+7f21,U+7f27,U+7f74,U+7fb0,U+8031,U+8071,U+80ea,U+8114,U+8160,U+81a6,U+81c1,U+829f,U+82a4,U+82fb,U+8333,U+836c,U+83b6,U+83f8,U+8411,U+841c,U+8489,U+848c,U+8627,U+8629,U+866e,U+86b5,U+872e,U+8731,U+877b,U+877d,U+87ea,U+8813,U+8816,U+88ce,U+88e5,U+897b,U+89cb,U+89f3,U+8bfc,U+8c35,U+8d46,U+8d4d,U+8dba,U+8e3a,U+8f75,U+9161,U+9179,U+917e,U+91a3,U+94ac,U+94d7,U+94e5,U+952a,U+952c,U+9545,U+9565,U+9568,U+956a,U+961d,U+96e0,U+9730,U+989f,U+98e7,U+990d,U+9967,U+9993,U+9ac0,U+9ae1,U+9aeb,U+9c86,U+9c8b,U+9ca0-9ca1,U+9ca3,U+9ce2,U+9e48,U+9e6a,U+9e87,U+9ee2,U+9ee9,U+9f17,U+9f19,U+9f80;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/71dec76f53414ec961b4900c206a8b49.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+50a7,U+5240,U+5261,U+52ac,U+531a,U+5363,U+5432,U+5452,U+5456,U+5472,U+5478,U+553f,U+5575,U+5581,U+55cc,U+55fe,U+5601,U+572e,U+57d2,U+5924,U+5981,U+5997,U+5aaa,U+5ab8,U+5b34,U+5d5d,U+5def,U+5e11,U+5e91,U+5ed1,U+5ef4,U+5f40,U+600d,U+6019,U+601b,U+605a,U+6092,U+60ab,U+6217,U+623d,U+6369,U+65d2,U+6661,U+670a,U+6753,U+67a7,U+6855,U+68f9,U+6939,U+6980,U+6a7c,U+6aab,U+6b82,U+6bf9,U+6c05,U+6c19-6c1a,U+6ca9,U+6cf6,U+6d1a,U+6dab,U+6f74,U+7085,U+7198,U+71b5,U+7256,U+725d,U+727e,U+72fa,U+7322,U+738e,U+73e5,U+750f,U+75b3,U+760c,U+7615,U+7630,U+763f,U+77ec,U+78a1,U+78d9,U+7905,U+7b2a,U+7b2e,U+7b62,U+7b85,U+7bea,U+7c26,U+7c74,U+7c9c-7c9d,U+7e47,U+7e9b,U+7e9f,U+7ee0,U+7ee8,U+7ef1,U+7f01,U+7f11,U+7f17,U+7f7e,U+7fee,U+802a,U+80cd,U+8112,U+8169,U+8234,U+8279,U+8298,U+82ca,U+82d8,U+82e1,U+83c0,U+83d4,U+83df,U+8401,U+845a,U+8476,U+8478,U+84ba,U+84bd,U+84e0,U+8548,U+8556,U+8585,U+868d,U+86e9,U+86f4,U+86f8,U+8765,U+8785,U+87ab,U+87ee,U+8832,U+8872,U+88b7,U+88e2-88e3,U+89da,U+8bce,U+8bd3,U+8bd6,U+8bf9,U+8c16,U+8d5c,U+8dde,U+8f6d,U+8f94,U+8fe8,U+9011,U+915e,U+9185,U+918c,U+94ab,U+94d1,U+9515,U+9558,U+9567,U+96ce,U+96e9,U+9785,U+9878,U+987c,U+9883,U+9954,U+9963,U+9a93,U+9ac1,U+9b1f,U+9b49,U+9b4d,U+9b51,U+9ca7,U+9cae,U+9cce,U+9cd3,U+9e37,U+9e39,U+9e41,U+9e46,U+9f22,U+9f2f,U+9f39,U+9f85;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/72b87555d8636b6feba6ad8d4b46ecd5.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+4e47,U+4f65,U+4f89,U+5416,U+5454,U+54bb,U+54c2,U+54d3,U+54de,U+55e5,U+560c,U+566b,U+5769,U+5793,U+57e4,U+57ef,U+581e,U+593c,U+59a3,U+59ab,U+5ad4,U+5ad8,U+5af1,U+5b53,U+5ba5,U+5c59,U+5c63,U+5d5b,U+5e0f,U+5e14,U+5fbc,U+6004,U+60ad,U+610e,U+61b7,U+624c,U+634c,U+64ba,U+65f0,U+66f7,U+67e2,U+67f0,U+680c,U+686b,U+6874,U+696e,U+6989,U+6a17,U+6b81,U+6b84,U+6bf3,U+6c06-6c07,U+6d07,U+6d27,U+6d2b,U+6d91,U+6e6b,U+6e8f,U+6fde,U+70bb,U+723b,U+726e,U+72b0,U+72ce,U+72f2,U+7301,U+731e,U+737e,U+7477,U+748e,U+755a,U+7594,U+7654,U+771a,U+7726,U+7765,U+7768,U+7817,U+781c,U+7829,U+78d4,U+7913,U+7957,U+79d5,U+79eb,U+7a70,U+7a86,U+7b25,U+7b38,U+7b47,U+7b72,U+7ba7,U+7dae,U+7ee1,U+7efe,U+7f26,U+7f31,U+7f36,U+801c,U+8043,U+809f,U+80ab,U+80d7,U+8118,U+8188,U+81cc,U+823e,U+82c1,U+82e4,U+82f4,U+8306,U+833a,U+835c,U+839c,U+83bc,U+8451,U+846d,U+851f,U+867a,U+868b,U+8734,U+87ca,U+886e,U+887e,U+88a2,U+88c9,U+8bb5,U+8bf3,U+8c04,U+8c17,U+8c1d,U+8c25,U+8c36,U+8c55,U+8c73,U+8c78,U+8d40,U+8d59,U+8d67,U+8d91,U+8deb-8dec,U+8dfd,U+8e14,U+8e41,U+8f8e,U+900b,U+90cf,U+9146,U+9162,U+9172,U+9190,U+92c8,U+948c,U+94aa,U+94c8,U+94ca,U+94d5,U+94df,U+94e9-94ea,U+94f3,U+94f7,U+94fc-94fd,U+951d,U+954f,U+9554,U+9559,U+9566,U+9571-9572,U+95f1,U+9608,U+960f,U+97af,U+988f,U+98d1,U+98d5,U+992e,U+9955,U+9ab0,U+9acc,U+9b32,U+9c9e,U+9ca5,U+9ca9,U+9cad,U+9cb1,U+9e47,U+9ee7,U+9f87;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/7df723549181cea988a1e0213e6d9336.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+4e5c,U+4e8d,U+4edf,U+4f32,U+4f5e,U+4f76,U+4faa,U+4fe6,U+5028,U+5048,U+50ee,U+520e,U+538d,U+53c1,U+5412,U+5443,U+54dd,U+5577,U+5591,U+55f3,U+560f,U+562c,U+5657,U+5664,U+56af,U+575c,U+577c,U+578c,U+57b2,U+57da,U+5800,U+5889,U+5aeb,U+5ca3,U+5d26,U+5edb,U+5f01,U+5fdd,U+6029,U+6041,U+6079,U+60b1,U+629f,U+6332,U+63bc,U+647a,U+65ab,U+65c3,U+65c6,U+6600,U+668c,U+669d,U+66be,U+6800,U+68fc,U+690b,U+691f,U+6924,U+6978,U+69a7,U+6a3e,U+6a50,U+6a5b,U+6b24,U+6b8d,U+6c10,U+6c3d,U+6c54,U+6d04,U+6d4d,U+6eb1,U+6ebd,U+71b3,U+7230,U+728d,U+7292,U+72b8,U+72d2,U+7360,U+73a2,U+74ff,U+75a0,U+7633,U+779f,U+7826,U+7877,U+7aa8,U+7b04,U+7ba6,U+7baa,U+7bac,U+7c1f,U+7ccd,U+7ecb,U+7ed4,U+7ed7,U+7efb,U+7f0d,U+7f35,U+7f5f,U+7faf,U+7fd5,U+8027,U+80bc,U+80dd,U+8132,U+815a,U+8167,U+81ca,U+8244,U+824f,U+82a1,U+82a9,U+82ab,U+82b4,U+8351,U+8368,U+83b3,U+83b8,U+83d8,U+83f0,U+8497,U+84c1,U+858f,U+85ff,U+867b,U+86a8-86a9,U+870a,U+876e,U+877c,U+8888,U+88df,U+8919,U+8921,U+8bdf,U+8be8,U+8bee,U+8c20,U+8c2f,U+8d3d,U+8dbf,U+8df8,U+8e05,U+9021,U+9044,U+9062,U+908b,U+90b4,U+90d0,U+90eb,U+9123,U+918d,U+91aa,U+933e,U+93ca,U+9486,U+948d,U+9490,U+94ad,U+94b2,U+94bd,U+94d6,U+94d9,U+9507,U+951b,U+9546,U+955e,U+956b,U+95e9,U+960b,U+9612,U+9617,U+989a-989b,U+9a78,U+9a7d,U+9aa0,U+9aa2,U+9ac2,U+9b23,U+9b3b,U+9c82,U+9c90,U+9cc3,U+9cca,U+9e28,U+9e6c,U+9efe,U+9f0b;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/ea8042fd77c8b2420800743cc89e731f.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+4e93,U+4ec4,U+4ef5,U+4f25,U+4f27,U+4f7b,U+4fe3,U+5080,U+5121,U+5208,U+5250,U+52f0,U+535f,U+5453,U+54a6,U+54d0,U+54d4,U+5533,U+5541,U+5549,U+5550,U+5556,U+55dd,U+55f5,U+5627,U+5658,U+567b,U+56d4,U+571c,U+57b4,U+5807,U+58c5,U+59a4,U+59af,U+5a09,U+5a62,U+5c3b,U+5cac,U+5d03,U+5d9d,U+5db7,U+5ebe,U+5f2d,U+5fb5,U+5fea,U+5ff8,U+6120,U+6221-6222,U+623e,U+638a,U+63e0,U+6485,U+64b8,U+64e2,U+66e9,U+67c1,U+67d2,U+67fd,U+6877,U+68f0,U+6934,U+6998,U+69c1,U+6a84,U+6a97,U+6aa9,U+6b87,U+6baa,U+6bd6,U+6c18,U+6cd4,U+6ceb,U+6cee,U+6de0,U+6ecf,U+70b7,U+7110,U+7168,U+71f9,U+72d9,U+7352,U+73b3,U+73d0,U+7511,U+75a5,U+75c8,U+75e7-75e8,U+7610,U+7619,U+772d,U+782c,U+784c,U+7850,U+7856,U+789b,U+78f4,U+7a39,U+7ae6,U+7b0f,U+7b15,U+7b1e,U+7b24,U+7b5a,U+7bd9,U+7ed0,U+7f1f,U+7f45,U+7f71,U+7fbf,U+7fe5,U+8052,U+80b1,U+80db,U+80fc,U+816d,U+81ec,U+8210,U+8228,U+8249,U+828a,U+828e,U+82cc,U+8315,U+8369,U+8378,U+83a8,U+83aa,U+83b4,U+83ea,U+8538,U+859c,U+85ae,U+86b4,U+86cf,U+8722,U+87e5,U+8ba7,U+8bcc,U+8c07,U+8c30,U+8c47,U+8d36,U+8db5,U+8e1f,U+8e2f,U+8e70,U+8e85,U+8f78,U+8f87,U+8f8b,U+8f8f,U+8f9a,U+90ba,U+90c4,U+90fe,U+917d,U+9487,U+948f,U+94cd,U+94ef,U+954a,U+9604,U+9609-960a,U+9615,U+96b9,U+96d2,U+9708,U+97ea,U+989e,U+98a7,U+996c,U+9980,U+9991,U+9a88,U+9ab6,U+9b47,U+9c87,U+9c9b,U+9cb5,U+9cc7,U+9cd9,U+9e2c,U+9e42,U+9e58,U+9e5a,U+9e5e,U+9ecd,U+9ecf,U+9f8a,U+9f8c;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/04cfe2a91171e009c38968dc4a8d3371.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+4edd-4ede,U+4ee1,U+4ffe,U+5025,U+506c,U+50a5,U+51fc,U+5241,U+525c,U+539d,U+53fc,U+54e7,U+5514,U+556e,U+557e,U+5599,U+55b9,U+55d4,U+5623,U+5654,U+5671,U+568f,U+56d7,U+57a1,U+57a7,U+57d9,U+584d,U+58ec,U+5942,U+59dd,U+5a32,U+5a55,U+5a75,U+5b71,U+5b73,U+5c98,U+5d06,U+5d6f,U+5dfd,U+5e5b,U+5fd2,U+600f,U+606b,U+62bb,U+62bf,U+634b,U+636d,U+6387,U+63b3,U+63b8,U+63c4,U+63c6,U+63f6,U+6441,U+6555,U+661d,U+6657,U+66a7,U+6849,U+6867,U+6901,U+699b,U+6a28,U+6a79,U+6b46,U+6b92,U+6c21,U+6c24,U+6c29,U+6c32,U+6c35,U+6c4a,U+6c86,U+6ca3,U+6cc5,U+6d39,U+6d63,U+6d6f,U+6ddd,U+6ed3,U+6edf,U+6fb9,U+6fd1,U+6fef,U+712f,U+71e7,U+72cd,U+72f0,U+7325,U+7350,U+7391,U+73c8,U+73d9,U+741a,U+757f,U+7583,U+759d,U+75b4,U+75f1,U+75ff,U+7699,U+7751,U+789c,U+78b2,U+7a17,U+7b08,U+7b33,U+7be6,U+7c2a,U+7ea3,U+7eb0,U+7f2c,U+8012,U+8171,U+821b,U+826e,U+8297,U+82be,U+82eb,U+830c,U+8314,U+8331,U+83c5,U+83d6,U+83fd,U+843c,U+8473,U+84a1,U+8564,U+8572,U+85c1,U+86a3,U+86aa,U+86c6,U+8708-8709,U+870d,U+8869,U+89c7,U+89d0,U+8a3e,U+8b07,U+8bc2,U+8bf0,U+8c2a,U+8c49,U+8c62,U+8c89,U+8d49,U+8d6d,U+8d94,U+8df9,U+8e2e,U+8e3d,U+8e47,U+8e51,U+8e7f,U+9005,U+9016,U+907d,U+9082,U+9088,U+90b0,U+914a,U+9495,U+94a3-94a4,U+94b9-94ba,U+94cb-94cc,U+94e1,U+9509,U+9512,U+9552-9553,U+955d,U+9697,U+96b0,U+96bc,U+975b,U+998a,U+9990,U+9995,U+9cb3,U+9cb7,U+9e57,U+9e82,U+9edc,U+9edf-9ee0,U+9f83,U+9f89;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/bbf355fd8f691159834b1bd2d4934748.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+4ebb,U+4fa9,U+502c,U+51c7,U+51eb,U+523d,U+530f,U+53f5,U+5464,U+5466,U+549d,U+54a3,U+54bf,U+5527,U+555c,U+556d,U+5576,U+558f,U+55b1,U+55eb,U+55f2,U+5624,U+564c,U+5685,U+56e1,U+5739,U+59d8,U+5a0c,U+5a4a,U+5a67,U+5ad2,U+5b6c,U+5c50,U+5c7a,U+5ca2,U+5d27,U+5d6c,U+5df3,U+5e19,U+5ea0,U+5eb9,U+5eea,U+5ffe,U+6042,U+6175,U+6215,U+622c,U+6266,U+6308,U+6339,U+643d,U+659b,U+6677,U+6775,U+678b,U+679e,U+67b3,U+6832,U+6840,U+6860,U+68c2,U+6910,U+6966,U+6987,U+69ce,U+6a2f,U+6a3d,U+6b93,U+6bc2,U+6bfd,U+6c16,U+6d0c,U+6d60,U+6da0,U+6e0c,U+6e86,U+6f4b,U+7023,U+7080,U+70ca,U+7145,U+7284,U+732c,U+740a,U+7441,U+7457,U+74d2,U+7596,U+75a3,U+75d8,U+75e3-75e4,U+7622,U+765e,U+7688,U+76b4,U+76e5,U+7812,U+7818,U+7887,U+789a,U+7a51,U+7bb8,U+7bc1,U+7ccc,U+7ea8,U+7ec0,U+7ee6,U+7efa,U+7f1b,U+7f22,U+7fa7,U+7fe6,U+7ff3,U+8084,U+8093,U+80e4,U+80ef,U+80f4,U+81bb,U+8202,U+82a8,U+82e3,U+8366,U+83e1,U+84ca,U+84d1,U+84fc,U+853b,U+857b,U+85d3,U+8605,U+8662,U+86b1,U+86c9,U+86d4,U+86ed,U+86f3,U+8725,U+8748,U+874c,U+8763,U+879f,U+87b3,U+887d,U+88fe,U+89de,U+89e5,U+8a8a,U+8ba6,U+8c00,U+8c14,U+8c21,U+8c7a,U+8d30,U+8dd7,U+8e59,U+8e69,U+8f6b,U+8f73,U+8ff3,U+9026,U+902f,U+9099,U+909b,U+90c7,U+9143,U+91ae,U+91ba,U+94af,U+94bf,U+94d2,U+94f0,U+9531,U+95f3,U+9765,U+977c,U+9880,U+98a2,U+9994,U+9a9b,U+9ab7,U+9ac5,U+9afb,U+9c91,U+9ccf,U+9cd5,U+9e29,U+9f88;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/dd793757576d356f49b1c0822ff9b909.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+4eb5,U+4f09,U+4f22,U+4f43,U+4f57,U+4f6f,U+4fce,U+4fea,U+4ff8,U+500c,U+504e,U+507b,U+5088,U+5345,U+53fb,U+54a7,U+54cc,U+550f,U+5544,U+558b,U+5594,U+559f,U+55e8,U+564e,U+5785,U+57ad,U+57ed,U+5914,U+59aa,U+5a06,U+5a40,U+5ae1,U+5ae6,U+5b40,U+5b93,U+5c99,U+5c9c,U+5ccb,U+5d2e,U+5d47,U+5d4b,U+5d99,U+5e54,U+5e61,U+6002,U+603f,U+6078,U+607d,U+607f,U+608c,U+609a,U+60fa,U+61ff,U+621b,U+6410,U+6414,U+6448,U+64d8,U+6710,U+6748,U+6772,U+67ad,U+67b0,U+680e,U+69ab,U+6c68,U+6c8f,U+6d2e,U+6d9e,U+6e11,U+6e98,U+6ec2,U+6ee2,U+6f66,U+6fe0,U+7094,U+70e9,U+7119,U+723f,U+729f,U+73c9,U+73de,U+7430,U+74e4,U+753e,U+7548,U+75bd,U+75cd,U+7634,U+76c5,U+76f1,U+7708,U+778c,U+77b3,U+781d,U+7830,U+783b,U+7893,U+78a3,U+7949,U+79e3,U+7a14,U+7a23,U+7afd,U+7c0c,U+7c41,U+7c91,U+7ec2,U+7ecc,U+7f03-7f04,U+7fca,U+8006,U+8069,U+814b,U+8198,U+8200,U+828d,U+82c4,U+82cb,U+82d2,U+82f7,U+832d,U+834f,U+840b,U+8438,U+847a,U+84d6,U+84e5,U+852b,U+85d0,U+86f0,U+8703,U+8707,U+8782,U+87a8,U+87d2,U+87e0,U+8839,U+8913,U+891b,U+8941,U+8a07,U+8bc3,U+8bcb,U+8bd2,U+8c11,U+8c29,U+8c32,U+8c4c,U+8d45,U+8dfa,U+8e31,U+8e49,U+8e6d,U+9004,U+9036,U+90dc,U+9169,U+91b4,U+938f,U+9506,U+950f,U+9528,U+9542,U+9549,U+9563,U+9573,U+961a,U+9649,U+9701,U+972d,U+973e,U+9798,U+97a3,U+97eb,U+988c,U+98a6,U+98d3,U+98d9,U+996f,U+9977,U+997d,U+9997,U+9a9d,U+9acb,U+9e20,U+9e49,U+9e4c,U+9e51,U+9f3e;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/cde5500be8115fb0de1069f6aab10bef.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+4ee8,U+4eeb,U+4f5d,U+4fda,U+502d,U+50f3,U+5106,U+520d,U+52ad,U+530d,U+5310,U+53a9,U+53df,U+5421,U+544b,U+5477,U+5482,U+551b,U+5530,U+5555,U+55c4,U+55d1,U+55df,U+55e4,U+55ea,U+55ec,U+55ef,U+562d,U+56eb,U+56f5,U+56f9,U+5704,U+576d,U+57d5,U+57dd,U+5880,U+5958,U+599e,U+59be,U+5abe,U+5b51,U+5bee,U+5cbf,U+5cc4,U+5cd2,U+5ce4,U+5e96,U+607b,U+61d1,U+622e,U+626a,U+6371,U+63ae,U+63cd,U+63d6,U+63ff,U+6421,U+64c0,U+66dc,U+67a5,U+6841,U+6954,U+6a47,U+6c94,U+6ca4,U+6d94,U+6e4e,U+705e,U+7116,U+726f,U+72de,U+75b1,U+75b8,U+75c2,U+7618,U+766f,U+7719,U+777e,U+7791,U+7823,U+7827,U+7889,U+795c,U+7a88,U+7a95,U+7aa0,U+7b90,U+7bd1,U+7bfe,U+7cb2,U+7da6,U+7ebe,U+7eeb,U+7eef,U+7f08,U+7f25,U+7f58,U+7f61,U+7f9f,U+7fb8,U+8026,U+8037,U+8153,U+8174,U+8191,U+8214,U+8222,U+82d5,U+82dc,U+8334,U+835a,U+8418,U+84ff,U+8616,U+866c,U+8693,U+869d,U+86a7,U+86ac,U+86af,U+86b6,U+86ca,U+86d0,U+8759,U+8760,U+87af,U+87c6,U+87d1,U+88c6,U+8934,U+89ca,U+89ce,U+8ba3,U+8baa-8bab,U+8bc5,U+8bdb,U+8be4,U+8c15,U+8d84,U+8db8,U+8dc4,U+8dc6,U+8dce,U+8ddb,U+8e09,U+8e1d,U+8e39,U+8e42,U+8e4b,U+8e8f,U+8f71-8f72,U+9051,U+9097,U+90d3,U+90e2,U+90e6,U+90ef,U+9104,U+9150,U+919a,U+9497,U+949c,U+94e0,U+951f,U+9534,U+9557,U+9562,U+9606,U+965f,U+9791,U+9889,U+9894,U+9974,U+9a90,U+9a9c,U+9aa7,U+9aef,U+9c88,U+9ca2,U+9cb2,U+9cb6,U+9cc5,U+9ccd,U+9cdc,U+9cdf,U+9e22,U+9e2a,U+9e67,U+9e6b,U+9e73,U+9eb8,U+9f9b;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/e99e850c633aae4e57c488047f2ad2fa.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+4ec9,U+4f5a,U+4f8f,U+4fdf,U+4ff3,U+500f,U+501c,U+52be,U+5420,U+542e,U+5457,U+5486,U+5499,U+549b,U+54c6,U+54d2,U+552c,U+55bd,U+55d6,U+565c,U+567c,U+568e,U+5768,U+577b,U+57a9,U+59f9,U+5a11,U+5b17,U+5b6a,U+5bb8,U+5c15,U+5cc1,U+5d3d,U+5d4a,U+5e1b,U+5ea5,U+5fcf-5fd1,U+5fd6,U+5fff,U+6006,U+6014,U+60af,U+60b4,U+60c6,U+60da,U+60f4,U+612b,U+6194,U+61ca,U+621f,U+62c8,U+631b,U+631e,U+63e9,U+64b5,U+655d,U+65cc,U+6619,U+6635,U+6641,U+664c,U+665f,U+67b7,U+67e9,U+6845,U+684e,U+688f,U+695d,U+696b,U+69b7,U+6a58,U+6c26,U+6d35,U+6d43,U+6dd9,U+6dec,U+6e6e,U+6e9f,U+6ef9,U+6f09,U+6f78,U+6f8d,U+6fc2,U+6fc9,U+714a,U+72c8,U+7337,U+7566,U+7572,U+7579,U+75c9,U+75e2,U+75fc,U+762a,U+7638,U+7678,U+76c2,U+76f9,U+7762,U+77cd,U+77dc,U+77fd,U+7800,U+782d,U+783c,U+78ec,U+795f,U+7980,U+7b95,U+7ba9,U+7bdd,U+7cb3,U+7cbd,U+7cc5,U+7ec9,U+7ef6,U+7f19,U+7f44,U+7f54,U+807f,U+809b,U+80bd,U+80ed,U+816e,U+817c,U+81c0,U+8360,U+8392,U+84af,U+8537,U+8671,U+869c,U+86a4,U+86ce,U+86f9,U+8713,U+8737,U+873b,U+87c0,U+87cb,U+8815,U+8821,U+8936,U+89d1,U+8bb9,U+8bcf,U+8bd8,U+8be9,U+8c0c,U+8c0f,U+8d5d,U+8d73,U+8e76,U+8f7c,U+8fe4,U+9041,U+9052,U+905b,U+9095,U+90d7,U+916a,U+92ae,U+9485,U+94a1,U+94c4,U+94c9,U+94db,U+94e7,U+9503,U+9517,U+9537,U+95fe,U+9602,U+9616,U+9621,U+96c9,U+96f3,U+970e,U+9739,U+9753,U+98d2,U+98da,U+9968,U+9984,U+9a81,U+9b03,U+9c85,U+9ca8,U+9cab,U+9e66,U+9ebe,U+9f10,U+9f8b;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/494dbe5b41a5e8da197aa8fe93dcfa98.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+4e15,U+4e1e,U+4e2b,U+4eb3,U+4f0e,U+4f64,U+50a9,U+510b,U+51a2,U+51bc,U+527d,U+52d0,U+536f,U+53a5,U+53fd,U+5429,U+5494,U+54af,U+5506,U+5511,U+5520,U+5522,U+556c,U+55b3,U+55d2,U+55e1,U+55e6,U+55f7,U+55fd,U+561f,U+5639,U+5659,U+5662,U+5693,U+572a,U+5892,U+598a,U+5992,U+59a9,U+5a20,U+5a76,U+5ae3,U+5b7d,U+5cab,U+5cb7,U+5d34,U+5d58,U+5d82,U+5e1a,U+5e7a,U+5eff,U+5f0b,U+5f77,U+5fa8,U+6026,U+6035,U+6063,U+60bb,U+60d8,U+60ee,U+6115,U+61e6,U+61f5,U+620a,U+6248,U+62a1,U+62d7,U+6376,U+637b,U+64de,U+652b,U+6538,U+65bc,U+65ce,U+65d6,U+6666,U+6684,U+66b9,U+6773,U+6777,U+6787,U+67d8,U+67de,U+692d,U+693d,U+6994,U+6a35,U+6b59,U+6ce0,U+6d54,U+6d5c,U+6d8e,U+6dd6,U+6eb4,U+6f15,U+6f2a,U+704f,U+70ec,U+7118,U+7172,U+71b9,U+724d,U+7266,U+728a,U+733e,U+7396,U+73b7,U+73cf,U+7428,U+742c,U+742e,U+7455,U+74ee,U+74f4,U+7525,U+753a,U+75b5,U+75b9,U+75d4,U+7656,U+765c,U+768e,U+777d,U+7825,U+7837,U+78b4,U+795a,U+79ed,U+7a1e,U+7b06,U+7b20,U+7bab,U+7c7c,U+7cdc,U+7f30,U+7f42,U+7f94,U+8004,U+800b,U+8019,U+80ae,U+80c4,U+80f1,U+8146,U+81c6,U+81fc,U+81fe,U+822b,U+82aa,U+830f,U+832f,U+8340,U+835e,U+8365,U+8385,U+83a0,U+8424,U+8568,U+86e4,U+8717-8718,U+87fe,U+88f1,U+8902,U+8c12,U+8d32,U+8d53,U+8df7,U+8e7c,U+8f95,U+8fab,U+9035,U+909d,U+90c5,U+911e,U+9122,U+9149,U+919b,U+948e,U+9492,U+949a,U+94b5,U+94bc,U+94c6,U+94f1,U+9502,U+9511,U+9536,U+956f-9570,U+95f0,U+9631,U+9642,U+968d,U+97ed,U+988a,U+98e8,U+998b,U+99a5,U+9a9e,U+9edd;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/aa10c8404b10c1586241baf5e5004014.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+4e10,U+4e56,U+4e98,U+4f3a,U+4f5f,U+4f88,U+4f97,U+4fa5,U+504c,U+5197,U+52fa,U+5364,U+53e8,U+5406,U+545c,U+5471,U+5480,U+5495,U+54b3,U+54df,U+54e6,U+54ee,U+557c,U+5583,U+55dc,U+55e3,U+566c,U+592f,U+5944,U+5983,U+59ca,U+59e5,U+5a13,U+5a7f,U+5b09,U+5bd0,U+5eb5,U+5f1b,U+5f3c,U+608d,U+60cb,U+61a7,U+61ac,U+61cb,U+6233,U+62a0,U+62e7,U+62ee,U+62f4,U+62f7,U+6382,U+63c9,U+63ea,U+6400,U+645e,U+6482,U+6593,U+6615,U+664f,U+66e6,U+675e,U+67da,U+6805,U+6808,U+6868,U+68a2,U+695e,U+69ad,U+6a80,U+6a90,U+6b83,U+6be1,U+6c30,U+6cad,U+6cb1,U+6cf1,U+6d31,U+6d93,U+6dbf,U+6dc6-6dc7,U+6e0d,U+6e3a,U+6e85,U+6eba,U+6f5e,U+6f7c,U+71ee,U+722a,U+72b7,U+72e9,U+73ba,U+73d1,U+7409,U+7435-7436,U+7459-745a,U+747e,U+7487,U+74e2,U+7504,U+752c,U+7599,U+759f,U+75a1,U+75ca,U+75f0,U+761f,U+7629,U+777f,U+7785,U+77a5,U+77bf,U+78d5,U+7934,U+7940,U+79a7,U+7b19,U+7c95,U+7cb1,U+7ce0,U+7eca,U+7ef7,U+7f2b,U+7f81,U+7fcc,U+8046,U+8148,U+8165,U+819b,U+81ba,U+82ae,U+82b7,U+82d3,U+8301,U+830e,U+831c,U+8338,U+837c,U+8393,U+8398,U+83ba,U+83e0,U+853c,U+8654,U+86df,U+8712,U+873f,U+874e,U+8783,U+8859,U+88a4,U+8925,U+8bb7,U+8bff,U+8c1b,U+8c24,U+8c2c,U+8d61,U+8e6c,U+8f8a,U+8fe5,U+8ff8,U+901e,U+90f4,U+912f,U+9163,U+9170,U+91dc,U+949b,U+94a8,U+94b3,U+94c0,U+94e8,U+9525,U+9539,U+954c-954d,U+9550,U+955b,U+962a,U+9685,U+96cc,U+9776,U+988d,U+9975,U+9985,U+9a6f,U+9aa5,U+9c7f,U+9ca4,U+9cb8,U+9e25,U+9e35,U+9e4a;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/409c25575800b8f3502492c8ba9b73bd.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+4f58,U+4f6c,U+4f70,U+4fd0,U+4fe8,U+5014,U+51bd,U+524c,U+5315,U+5323,U+535e,U+540f,U+542d,U+545b,U+548e,U+549a,U+54ab,U+54fc,U+5567,U+556a,U+5600,U+5618,U+5669,U+56f1,U+56ff,U+573b,U+574d,U+579b,U+586c,U+58f9,U+595a,U+598d,U+5993,U+5996,U+59d7,U+5b7a,U+5ba6,U+5c4e,U+5c96,U+5e4c,U+5eb6,U+5f08,U+602f,U+6059,U+606c,U+607a,U+60ed,U+61a9,U+620c,U+6249,U+62a8,U+62c4,U+62ed,U+62fd,U+6342,U+6345,U+6396,U+6402,U+6413,U+642a,U+6487,U+64a9,U+64ac,U+6556,U+659f,U+65a1,U+667e,U+67e0,U+69db,U+69df,U+6aac,U+6b86,U+6c50,U+6c5e,U+6c76,U+6c85,U+6cde,U+6d19,U+6d52,U+6da7,U+6dae,U+6e25,U+6e4d,U+6e5f,U+6ec1,U+6f31,U+6f7a,U+6fa7,U+6fe1,U+701b,U+70ab,U+70f7,U+717d,U+71a8,U+7252,U+72c4,U+72e1,U+7315,U+736d,U+73ae,U+73c0,U+73c2,U+752d,U+75a4,U+7600-7601,U+768b,U+76bf,U+76d4,U+772f,U+776c,U+77a0,U+77b0,U+783a,U+78fa,U+7977,U+7a37,U+7a92,U+7afa,U+7b71,U+7b94,U+7cef,U+7f28,U+7fe1,U+808b,U+80e5,U+80eb,U+8110,U+812f,U+814c,U+81c3,U+8235,U+828b,U+82d4,U+8309,U+83c1,U+8431,U+8469,U+84bf,U+84d3,U+84df,U+84e6,U+8511,U+8638,U+86db,U+86fe,U+8757,U+8822,U+8882,U+8892,U+88f3,U+892a,U+8ba5,U+8bd9,U+8be0,U+8be7,U+8bfd,U+8c1a,U+8d4a,U+8d4e,U+8d66,U+8dda,U+8e0c,U+8e52,U+8e74,U+8e87,U+8f76,U+8fc2,U+8fe6,U+900d,U+9068,U+90ac,U+90b3,U+90b8,U+90e7,U+9119,U+9131,U+915a,U+94d0,U+94e2,U+94ec,U+94ff,U+9522,U+9535,U+965b,U+96f9,U+9774,U+9981,U+998d,U+998f,U+9a6e,U+9a7f,U+9ab8,U+9b13,U+9c9f,U+9e3e,U+9e43,U+9e6d,U+9e8b,U+9e92,U+9eef;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/aea9d65e4e102d43541761114a95dd80.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+4f2b,U+4f3d,U+4fac,U+5043,U+5055,U+5140,U+5156,U+51cb,U+5243,U+531d,U+53ae,U+53f1,U+541d,U+5431,U+547b,U+5492,U+54a4,U+54aa,U+54ce,U+54fd,U+5509,U+553e,U+557b,U+55c5,U+5608,U+5636,U+563b,U+563f,U+5773,U+57a0,U+57b8,U+57c2,U+5811,U+587e,U+58d5,U+59e3,U+5a29,U+5a6a,U+5a7a,U+5ac9,U+5b62,U+5b95,U+5c49,U+5c8c,U+5ce5,U+5d02,U+5e44,U+5f29,U+5f89,U+5f99,U+5f9c,U+6005,U+6043,U+60b8,U+60ec,U+60f0,U+618e,U+630e,U+637a,U+6390,U+63a3,U+63ac,U+63b0,U+64ae,U+64b7,U+6525,U+65ee-65ef,U+6631,U+6636,U+6654,U+66f3,U+677c,U+67b8,U+683e,U+6886,U+68b5,U+6963,U+6979,U+6988,U+6b9a,U+6c69,U+6c74,U+6c8c,U+6cae,U+6cef,U+6d95,U+6db8,U+6dc5,U+6dde,U+6de6,U+6dfc,U+6e1a,U+6ea7,U+6f29,U+7096,U+70c3,U+7131,U+715c,U+7166,U+7317,U+731d,U+7329,U+73e9,U+740f,U+7425,U+7490,U+74ef,U+7519,U+75de,U+7663,U+7691,U+7728-7729,U+77f8,U+77fe,U+783e,U+787c,U+78d0,U+7a79,U+7abf,U+7b3a,U+7b4f,U+7b60,U+7b75,U+7b8d,U+7bb4,U+7bd3,U+7be1,U+7cbc,U+7edb,U+7f1c,U+7f8c,U+7fb2,U+7fb9,U+7fce,U+7ff1,U+810d,U+8113,U+82a5,U+82de,U+8317,U+8343,U+8364,U+836a,U+853a,U+8543,U+854a,U+8559,U+85b0,U+85b9,U+864f,U+86c0,U+8715,U+8845,U+8884-8885,U+88e8,U+8983,U+8be1,U+8c1f,U+8c27,U+8c5a,U+8c82,U+8d58,U+8dbe,U+8f98,U+9074,U+90a1,U+9157,U+916e,U+93d6,U+949d,U+94b4,U+94c2,U+94e3-94e4,U+9556,U+95eb,U+9611,U+9619,U+9706,U+970f,U+9893,U+9a77,U+9a87,U+9a8a,U+9aa1,U+9abc,U+9cdd,U+9e2f,U+9e33,U+9e44,U+9e5c,U+9e9d,U+9edb;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/1498bb238ce7c1f7b38d3e583708ce73.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+4fa3,U+4fae,U+4fd8,U+50a3,U+5189,U+5195,U+51db,U+5220,U+5228,U+527f,U+5288,U+532e,U+533f,U+53db,U+5450,U+5484,U+5490,U+54c9,U+54e9,U+5501,U+56a3,U+5729,U+575e,U+589f,U+5984,U+5a04,U+5a36,U+5a77,U+5a9b,U+5ab2,U+5bc7,U+5c51,U+5cd9,U+5d0e,U+5deb,U+5e3c,U+5e87,U+5ed3,U+5f13,U+5f27,U+5f64,U+5ffb,U+606a,U+6096,U+60eb,U+60f6,U+60f9,U+6151,U+61a8,U+620d-620e,U+6241,U+6273,U+627c,U+6289,U+62c7,U+62cc,U+6361,U+6363,U+63b7,U+6518,U+66ae,U+6756,U+6789,U+6813,U+6829,U+6862,U+6866,U+6893,U+6897,U+68e3,U+6984,U+69a8,U+69cc,U+6a1f,U+6a44,U+6a59,U+6bd3,U+6c13,U+6cbd,U+6ccc,U+6cd3,U+6cd7,U+6cfe,U+6d4a,U+6d4f,U+6d5a,U+6d9f,U+6da1,U+6e44,U+6ea5,U+6ee4,U+6ee6,U+6f2f,U+6f8e,U+701a,U+7078,U+7095,U+709c,U+70af,U+70bd,U+70db,U+70e8,U+714e,U+715e,U+71a0,U+71ce,U+7235,U+7239,U+72d0,U+72f8,U+745c,U+7480,U+74a7-74a8,U+74e3,U+75ae,U+75f9,U+7693,U+76cf,U+776b,U+77e3,U+781a,U+7852,U+789f,U+797a,U+79ba,U+79be,U+79c3,U+79c6,U+79f8,U+7a8d,U+7a98,U+7aa6,U+7aff,U+7b1b,U+7bc6,U+7cd9,U+7d6e,U+7eee,U+7f24,U+7f2d,U+7fd8,U+800d,U+80fa,U+8151,U+818a,U+81b3,U+8205,U+82b8,U+82c7,U+82db,U+8339,U+8386,U+83cf,U+8611,U+868c,U+8747,U+8774,U+88f4,U+8912,U+8b6c,U+8bbd,U+8c0e,U+8d26,U+8d31,U+8d3b-8d3c,U+8d48,U+8e35,U+8e4a,U+8f99,U+8fe9,U+9017,U+914c,U+916f,U+9175-9176,U+918b,U+94a0,U+94ce,U+94f2,U+951a,U+952f,U+9541,U+9640,U+9672,U+968b,U+96cd,U+96ef,U+9713,U+97ec,U+9885,U+996a,U+9992,U+9a79,U+9cbb,U+9cd7,U+9cde,U+9e93;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/dd6855d212df7ae70c1d12219ad2323f.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+4ea2,U+4ea5,U+4ec3,U+4f36,U+4f84,U+4f8d,U+501a,U+5029,U+516e,U+51a5,U+51c4,U+51f8,U+5201,U+5321,U+5352,U+5366,U+53e9,U+543c,U+54c7,U+5632,U+5676,U+56b7,U+56bc,U+56da,U+56e4,U+5703,U+5742,U+57a2-57a3,U+5815,U+58d1,U+5919,U+592d,U+5955,U+5a05,U+5a25,U+5a34,U+5b70,U+5b75,U+5bdd,U+5bf0,U+5c41,U+5c79,U+5c91,U+5c94,U+5ce6,U+5ced,U+5d69,U+5dc5,U+5e16,U+5e27,U+5f95,U+6020,U+604d,U+6055,U+60e6,U+6123,U+618b,U+62ce,U+62d9,U+631f,U+634d-634e,U+6452,U+6479,U+64ce,U+64d2,U+655b,U+660a,U+6726,U+672d,U+67c4,U+6809,U+6853,U+68f1,U+68fa,U+693f,U+6942,U+6995,U+69b4,U+6a71,U+6b89,U+6bcb,U+6bd9,U+6c40,U+6cf8,U+6d85,U+6da3,U+6daa,U+6e0e,U+6e32,U+6e43,U+6f3e,U+6f88,U+6fee,U+7099,U+70d9,U+70fd,U+7109,U+7184,U+733f,U+73f2,U+748b,U+749c,U+749e,U+759a,U+75d2,U+75eb,U+7620,U+766b,U+7738,U+773a,U+778e,U+77aa,U+78be,U+7948,U+795b,U+7960,U+796f,U+7a20,U+7a96,U+7aa5,U+7b03,U+7b28,U+7b50,U+7b77,U+7bf1,U+7c27,U+7c38,U+7d0a,U+7ead,U+7ec5,U+7ee2,U+7ef0,U+7efd,U+7f0e,U+7f2e,U+7f79,U+7f9a,U+8098,U+80da,U+80e7,U+80f0,U+80f3,U+81e7,U+8237-8238,U+8299,U+82ce,U+837b,U+83bd,U+83e9,U+8426,U+8475,U+85c9,U+85d5,U+85dc,U+85e9,U+871a,U+8749,U+888d,U+8910,U+891a,U+8bb4,U+8be3,U+8bec,U+8bf2,U+8c06,U+8c0d,U+8c19,U+8db4,U+8de4,U+8e1e,U+8e66,U+8f84,U+8f97,U+9083,U+90e1,U+9165,U+91c9,U+94b0,U+94f5,U+9504,U+9530,U+9532,U+956d,U+95f5,U+95fa,U+9668,U+9698,U+96bd,U+9704,U+9773,U+9890,U+997a,U+9a74,U+9a8b,U+9cc4,U+9ccc;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/994cc295607ee1636f8fe3fdbefb0308.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+4e4d,U+4e5e,U+4ed5,U+4fef,U+50da,U+51f3,U+51f9,U+5203,U+52ff-5300,U+533e,U+5351,U+53ed-53ee,U+5435,U+543b,U+5455,U+548b,U+54d7,U+5507,U+5543,U+5578,U+55d3,U+560e,U+5760,U+5830,U+58a9,U+5962,U+59e8,U+59ec,U+5a07,U+5a23,U+5a3c,U+5a9a,U+5ac2,U+5ad6,U+5b5a,U+5bde,U+5c2c,U+5c34,U+5c7f,U+5cb1,U+5ce8,U+5cea,U+5d4c,U+5f5d,U+5f87,U+5fe1,U+5ff1,U+601c,U+6064,U+60df,U+6177,U+6252,U+62c2,U+62e3,U+62ef,U+62f1,U+634f,U+63a0,U+63e3,U+6401,U+6405,U+6495,U+6512,U+6577,U+65a9,U+65f7,U+6627,U+6655,U+6714,U+6795,U+67ff,U+68b3,U+68d5,U+690e,U+6960,U+6977,U+69bb,U+69d0,U+6a31,U+6b7c,U+6ba1,U+6bb4,U+6c72,U+6c79,U+6c81,U+6c90,U+6c93,U+6ca6,U+6cbc,U+6cfb,U+6da9,U+6dcc,U+6e2d,U+6eaf,U+6ec7,U+6f13,U+6f62,U+6fa1,U+7011,U+707c,U+708a,U+70d8,U+7194,U+7280-7281,U+7357,U+7384,U+73ab,U+7410,U+745b,U+7578,U+75ea,U+7682,U+76b1,U+76ce,U+7736,U+77d7,U+77e2,U+77eb,U+77f6,U+780c,U+7941,U+7a1a,U+7a9c,U+7b5d,U+7bf7,U+7c07,U+7ca5,U+7e82,U+7eab,U+7eda,U+7ede,U+7f00,U+7f9e,U+7fdf,U+7fe9,U+803d,U+80aa,U+80b4,U+8116,U+813e,U+8155,U+817b,U+819d,U+821c,U+82b9,U+82df,U+82ef,U+8304,U+832c,U+8335,U+83b9,U+8446,U+846b,U+8587,U+85af,U+85fb,U+8650,U+865e,U+8682,U+868a,U+86d9,U+86ee,U+8862,U+889c,U+88d4,U+88d8,U+88f8,U+895f,U+8a79,U+8bb6,U+8bc0,U+8bf5,U+8c41,U+8c79,U+8d50,U+8dcb,U+8dea,U+8e29,U+8e44,U+8eac,U+8eaf,U+8f8d,U+8fe2,U+9050,U+90f8,U+914b,U+948a,U+94ae,U+9661,U+9699,U+9761,U+97a0,U+9a6d,U+9a85,U+9a8f,U+9ae6,U+9e26,U+9f9f;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/4355e0b63079ecc78ee97b72ce0705e2.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+4ea8,U+4ec7,U+4f51,U+4f63,U+4fa0,U+5018,U+5026,U+508d,U+50e7,U+515c,U+51a4,U+51ff,U+5254,U+5375,U+538c,U+541f,U+5475,U+548f,U+54d1,U+54fa,U+5587,U+5589,U+5598,U+55a7,U+575f,U+576f,U+5777,U+57ae,U+5937,U+5978,U+5a1f,U+5a49,U+5ab3,U+5b99,U+5bb5,U+5bc5,U+5be1,U+5be5,U+5c09,U+5d29,U+5e18,U+5f26,U+5f57,U+6016,U+6021,U+606d,U+60d5,U+60e7,U+614c,U+61d2,U+625b,U+6292,U+6296,U+62e2,U+631d,U+6467,U+64bc,U+64c2,U+6590,U+65a7,U+6643,U+6652,U+6656,U+6687,U+66dd,U+67d1,U+6816,U+68a7,U+68ad,U+68cd,U+68d8,U+68e0,U+6930,U+6b47,U+6bd7,U+6c22,U+6c2e-6c2f,U+6c7e,U+6ca5,U+6ce3,U+6d3c,U+6de4,U+6df3,U+6e1d,U+6e83,U+6ed5,U+6f33,U+6f4d,U+70c1,U+70eb,U+70f9,U+711a,U+7130,U+716e,U+718f,U+71ac,U+71e5,U+72fc,U+7316,U+731c,U+73ca,U+7405,U+7422,U+7426,U+742a,U+745f,U+7470,U+7574,U+75af,U+75f4,U+763e,U+7737,U+7741,U+7792,U+77a7,U+77bb,U+77e9,U+77ee,U+785d,U+788c,U+78c5,U+796d,U+7985,U+79e4,U+79fd,U+7a3c,U+7a57,U+7ad6,U+7c3f,U+7c9f,U+7cb9,U+7cdf,U+7ece,U+7ed1,U+7ee5,U+7f09,U+7f15,U+7f2a,U+7f38,U+8018,U+8038,U+803b,U+804b,U+80d6,U+817a,U+81fb,U+82ad,U+82af,U+8327,U+8354,U+835f,U+8367,U+841d,U+84b2,U+853d,U+8549,U+8681,U+8700,U+8721,U+88d9,U+88f9,U+89c5,U+8bb3,U+8beb,U+8c23,U+8c34,U+8d1e,U+8d81,U+8e72,U+8e81,U+901b,U+906e,U+9091,U+90af,U+946b,U+9499,U+94a5,U+94be,U+94ee,U+9508,U+950c,U+9524,U+952d,U+9540,U+9576,U+9600,U+962e,U+9647,U+96cf,U+9716,U+97e7,U+97f6,U+98a0,U+98a4,U+9a7c,U+9a86,U+9cd6,U+9e3d;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/f36b9bb41591a7f4d4919a404f7dc774.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+4e19,U+4e38,U+4ec6,U+4ed1,U+4f1e,U+4f7c,U+4f83,U+4fd1,U+4fde,U+4ffa,U+50bb,U+50f5,U+50fb,U+5154,U+5162,U+51d1,U+51f0,U+5256,U+5308,U+5319,U+5320,U+532a,U+535c,U+5384,U+53e0,U+5413,U+54bd,U+54e8,U+5582,U+561b,U+5631,U+57e0,U+58f6,U+5974,U+59ae,U+5b9b,U+5ba0,U+5c27,U+5c38,U+5c82,U+5c90,U+5d16,U+5dcd,U+5de2,U+5e37,U+5e90,U+5e9a,U+5f6a,U+5f8a,U+5f98,U+5fcc,U+607c,U+6094,U+60bc,U+611a,U+6254,U+626f,U+62b9,U+62d0,U+62fe,U+631a,U+6320,U+6346,U+63ba,U+6454,U+658b,U+663c,U+6674,U+66d9,U+66f0,U+673d,U+6749,U+67ab,U+67af,U+6817,U+6854,U+6869,U+6912,U+6986,U+69fd,U+6b49,U+6b6a,U+6c1f,U+6c5d,U+6cab,U+6cbe,U+6cf5,U+6dc4,U+6e0a,U+6e23,U+6e9c,U+6ed4,U+6f84,U+70ac,U+7334,U+7529,U+752b,U+75d5,U+762b,U+7696,U+7720,U+7766,U+77ac,U+7838,U+7845,U+78ca,U+7901,U+79b9,U+79e7,U+7a3d,U+7a74,U+7a84,U+7a9f,U+7b0b,U+7b52,U+7b5b,U+7c7d,U+7caa,U+7cd5,U+7eac,U+7eb6,U+7f20,U+7f69,U+7fa1,U+7fc5,U+803f,U+8086,U+808c,U+809a,U+80a2,U+80be,U+8180,U+81ed,U+820c,U+8231,U+829c,U+82a6,U+82bd,U+82d1,U+8346,U+836b,U+839e,U+83c7,U+83f1,U+8403,U+840c,U+840e,U+8471,U+849c,U+84c9,U+8517,U+851a,U+85e4,U+871c,U+8776,U+87ba,U+87f9,U+8a93,U+8c05,U+8c1c,U+8d2c,U+8d2e,U+8d43,U+8d9f,U+8dfb,U+8e0a,U+8e22,U+8eb2,U+8eba,U+8f69,U+8fa8,U+8fed,U+900a,U+902e,U+9038,U+90aa,U+90dd,U+915d,U+9171,U+9187,U+9489,U+94a7,U+9523,U+9551,U+95f7,U+964c,U+96b6,U+96c0-96c1,U+9709,U+971c,U+9756,U+9965,U+997f,U+9988,U+99a8,U+9a73,U+9a82,U+9ad3,U+9e9f,U+9ed4;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/dd0497d9be890febaf5f083a11fdc3b2.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+4e11,U+4e53,U+4e7e,U+4e9f,U+4f50,U+502a,U+50b2,U+517d,U+5188,U+51f6,U+522e,U+5239,U+52c9,U+52df,U+52fe,U+5349,U+53a2,U+53a8,U+53c9,U+5496,U+54ac,U+54b8,U+54c0,U+54c4,U+5561,U+5566,U+55bb,U+566a,U+574e,U+5764,U+576a,U+5792,U+57ab,U+584c,U+5885,U+5938,U+594e,U+59a8,U+5acc,U+5b5c,U+5bc2,U+5c39,U+5c60,U+5c6f,U+5eb8,U+5f2f,U+5fa1,U+6012,U+6068,U+6073,U+6109,U+6124,U+6127,U+621a,U+626e,U+6284,U+62d8,U+62e6,U+6321,U+6328,U+632b,U+6349,U+6367,U+638f,U+63a9,U+641c,U+655e,U+6591,U+65a5,U+65ed,U+660f,U+66a8,U+6735,U+674f,U+6760,U+67ef,U+6850,U+68a8,U+68d2,U+68f5,U+6bef,U+6c28,U+6c41,U+6c82,U+6c9b,U+6cb8,U+6cc4,U+6cfc,U+6d47,U+6d51,U+6d74,U+6d78,U+6d9d,U+6da4,U+6daf,U+6dcb,U+6deb,U+6df9,U+6e5b,U+6e89,U+6eb6,U+6ef4,U+6f06,U+6f47,U+6f9c,U+6fd2,U+7076,U+70b3,U+70ef,U+7199,U+723d,U+72ac,U+72ed-72ee,U+7433,U+7476,U+754f,U+7554,U+75bc,U+7624,U+7626,U+76ef,U+7750,U+7779,U+7784,U+780d,U+786b,U+78b3,U+78f7,U+7978,U+7984,U+79c9,U+7aed,U+7c98,U+7eb9,U+7ee3,U+7f1a,U+7fd4,U+7fe0,U+7ff0,U+8042,U+804a,U+8087,U+80ba,U+80c3,U+810a,U+814a,U+8154,U+818f,U+81c2,U+8292,U+8328,U+832b,U+8389,U+838e,U+8513,U+8574,U+857e,U+859b,U+867e,U+8680,U+8695,U+884d,U+88e4,U+8944,U+8c10,U+8d42,U+8f9c,U+8fa3,U+8fb1,U+903b,U+9042,U+904f,U+90b5,U+9102,U+94c5,U+94fe,U+95f8,U+95fd,U+960e,U+964b,U+96a7,U+96fe,U+978d,U+97ad,U+97f5,U+9882,U+9888,U+9976,U+9a9a,U+9b3c,U+9b41,U+9b44,U+9b54,U+9c8d,U+9e45,U+9e64,U+9e70,U+9f3b,U+9f7f,U+9f9a;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/2384badd5dfa32c6c885b53784a0f8db.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+4e22,U+4e52,U+4ed7,U+4ef0,U+4fcf,U+4fe9,U+4fed,U+50ac,U+5112,U+5179,U+5180,U+5265,U+52cb,U+5367,U+5378,U+537f,U+5395,U+5398,U+53d4,U+541e,U+543e,U+5440,U+5446,U+54b1,U+5565,U+5580,U+56ca,U+572d,U+573e,U+5782-5784,U+58f3,U+5939,U+5948,U+5951,U+5986,U+59fb,U+5a1c,U+5a74,U+5ae9,U+5b55,U+5b5d,U+5bb0,U+5bd3,U+5bf8,U+5c3f,U+5c48,U+5d14,U+5d2d,U+5df7,U+5dfe,U+5e05-5e06,U+5e15,U+5e1c,U+5e62,U+5e7b,U+5e7d,U+5e99,U+5ed6,U+5f17,U+5f66,U+5f6c,U+6028,U+604b,U+609f,U+60a6,U+60e8,U+6101,U+6148,U+61be,U+6247,U+629b,U+62ab,U+62da,U+633d,U+635e,U+6380,U+63fd,U+6491,U+64b0,U+64e6,U+6572,U+662d,U+6691,U+67a2-67a3,U+67d4,U+680b,U+6876,U+68da,U+6905,U+6a0a,U+6a61,U+6b3a,U+6b79,U+6bb7,U+6bbf,U+6c55,U+6c5b,U+6c70,U+6c83,U+6ca7,U+6d46,U+6dc0,U+6dd1,U+6dd8,U+6e17,U+6e24,U+6e34,U+6ea2,U+6f6d,U+707f,U+70e4,U+70e6,U+710a,U+722c,U+725f,U+7261,U+727a,U+72f1,U+730e,U+732b,U+743c,U+7538,U+75b2,U+76d2,U+7709,U+7802,U+7898,U+78a7,U+78b1,U+78cb,U+7a83,U+7a91,U+7b3c,U+7b4b,U+7c92,U+7ca4,U+7cca,U+7eb1,U+7ef3,U+7ef5,U+7f05,U+7f55,U+7f62,U+7fc1,U+7ffc,U+8036,U+806a,U+80a0,U+80a4,U+8102,U+8106,U+8247,U+8258,U+82cd,U+82f9,U+83ca,U+8427,U+845b,U+846c,U+84b8,U+86c7,U+8702,U+886c,U+8896,U+88b1,U+8bc8,U+8c26,U+8c28,U+8c2d,U+8d4c,U+8d63,U+8f67,U+8f74,U+8fb0,U+8fc4,U+9006,U+9063,U+90a2,U+90b1,U+90c1,U+9177,U+917f,U+9189,U+9493,U+949e,U+94ed,U+9610,U+961c,U+96c7,U+97e6,U+987d,U+997c,U+9a84,U+9a91,U+9b45,U+9f0e,U+9f20;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/0cd2a5623fd703e699955076a20ae377.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+4e08,U+4e18,U+4e1b,U+4e27,U+4e32,U+4e73,U+4ead,U+4ed4,U+4ed9,U+4fa6,U+5076,U+51bb,U+51c9,U+51ef,U+51fd,U+524a,U+526a,U+529d,U+52ab,U+5306,U+5339,U+53d9,U+540a,U+5410,U+5439,U+54ed,U+5524,U+5564,U+558a,U+55b7,U+5634,U+574a,U+5751,U+57a6,U+57cb,U+57d4,U+5824,U+582a,U+5835,U+5858,U+5893,U+58e4,U+59da,U+59ff,U+5a03,U+5a46,U+5ac1,U+5bb4,U+5bfa,U+5c18,U+5c3a,U+5c4f,U+5c61,U+5cb3,U+5cfb,U+5d1b,U+5e3d,U+5e9e,U+5eca,U+5f0a,U+5f18,U+5f25,U+5f7c,U+5fcd,U+60a0,U+60ac,U+60d1,U+614e,U+6155,U+6168,U+61c8,U+6208,U+6212,U+6251,U+629a,U+62ac,U+62fc,U+6323,U+632a,U+633a,U+63d2,U+642d,U+643a,U+6492,U+649e,U+64c5,U+659c,U+6602,U+6614,U+6664,U+6670,U+6676,U+6746,U+67cf,U+682a,U+6843,U+6846,U+6b67,U+6c27,U+6c64,U+6caa,U+6cca,U+6ce1,U+6d12,U+6d45,U+6e7f,U+6ee5,U+6f02,U+7092,U+70c2,U+7115,U+7237,U+7272,U+72c2,U+72d7,U+739b,U+73b2,U+751c,U+758f,U+7686,U+76c6,U+76fc,U+775b,U+77a9,U+7816,U+788e,U+7897,U+78b0,U+79bd,U+7a0d,U+7a9d,U+7ae3,U+7bad,U+7d2b,U+7f06,U+7f14,U+7f1d,U+7f50,U+809d,U+80bf,U+80c1,U+80ce,U+80f8,U+8109,U+810f,U+8170,U+8179,U+819c,U+821f,U+8230,U+8236,U+8273,U+829d,U+8305,U+8350,U+83b2,U+83cc,U+8404,U+840d,U+8461,U+8482,U+84ec,U+8521,U+85aa,U+8679,U+8854,U+886b,U+8877,U+8bbc,U+8be6,U+8c31,U+8c6b,U+8d4b,U+8dcc,U+8e2a,U+8e48,U+8f90,U+901d,U+9022,U+903c,U+903e,U+9065,U+916c,U+94a9,U+94c3,U+94dd,U+9510,U+953b,U+970d,U+9738,U+9875,U+9877,U+989c,U+98d8,U+9a70,U+9aa4,U+9b42,U+9b4f,U+9e2d,U+9e3f,U+9e7f;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/fcc81a026d206c6f61ad563e8fe30698.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+4e59,U+4ed3,U+4f0f,U+4f38,U+4fa7,U+4faf,U+4ff1,U+5077,U+5085,U+5144,U+5146,U+5151,U+51af,U+51b6,U+51cc,U+523a,U+5251,U+5272,U+52d8,U+5353,U+5389,U+53f9,U+5415,U+541b,U+54f2,U+554a,U+559d,U+575d,U+5806,U+5821,U+586b,U+5915,U+594f,U+5960,U+5999,U+59a5,U+59b9,U+59c6,U+59d1,U+59dc,U+5b5f,U+5b64,U+5b87,U+5bbf,U+5c16,U+5c1d,U+5c3e,U+5c9a,U+5ca9,U+5cad,U+5de1,U+5de7,U+5de9,U+5ef7,U+5f04,U+5f70,U+5f79,U+5fc6,U+6050,U+6052,U+6070,U+6084,U+60b2,U+60dc,U+60e9,U+6167,U+6170,U+61c2,U+6291,U+62df,U+62f3,U+6324,U+6377,U+6398,U+63cf,U+640f,U+642c,U+6458,U+6478,U+6500,U+654c,U+6566,U+658c,U+65c1,U+65cb,U+65ec,U+6696-6697,U+6734,U+679d,U+67dc,U+67f3-67f4,U+680f,U+683d,U+684c,U+68af,U+699c,U+6bc1,U+6c0f,U+6c1b,U+6c57,U+6c6a,U+6d3d,U+6d82,U+6db5,U+6dee,U+6e58,U+6eaa,U+6ecb,U+6ede,U+6ee9,U+6f0f,U+6f20,U+6f58,U+704c,U+7070,U+718a,U+7238,U+7262,U+7275,U+72b9,U+72e0,U+741b,U+7434,U+7483,U+74f6-74f7,U+75ab,U+764c,U+7761,U+7855,U+7891,U+78c1,U+79d2,U+7a00,U+7a3b,U+7c97,U+7ea4,U+7eb2,U+7ed2,U+7eea,U+7ef8,U+7f18,U+7fbd,U+8000,U+8010,U+8096,U+80a9,U+817f,U+81e3,U+8206,U+8212,U+82ac,U+8302,U+8361,U+8377,U+83f2,U+848b,U+8870,U+8881,U+88ad,U+88c2,U+8986,U+8bd1,U+8bf1,U+8d24,U+8d2a,U+8d3e-8d3f,U+8d41,U+8d64,U+8d6b,U+8e0f,U+8f70,U+8f85,U+8fa9,U+9003,U+90b9,U+90ce,U+94a6,U+94f8,U+95ea,U+95ef,U+95f2,U+95f9,U+9601,U+9605,U+966a,U+9677,U+9690,U+9694,U+96d5,U+971e,U+9896-9897,U+9972,U+9a71,U+9a7e,U+9e1f,U+9e23;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/a5c147b47f107e2dda0101491ab02453.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+4e43,U+4ea6,U+4ef2,U+4eff,U+4f2f,U+4f69,U+4fca,U+4fd7,U+5021,U+5141,U+51c0,U+51dd,U+51e4,U+5200,U+5237,U+5269,U+5362,U+5401,U+5427,U+54a8,U+5609,U+5708,U+5723,U+5740,U+575b,U+57c3,U+5899,U+58c1,U+5976,U+5988,U+59bb,U+59d0,U+5a18,U+5a31,U+5a92,U+5b54,U+5b85,U+5baa,U+5bc4,U+5bd2,U+5be8,U+5bff,U+5c65,U+5d07,U+5e1d,U+5e7c,U+5f03,U+5f6d,U+5f92,U+5ffd,U+602a,U+6108,U+6162,U+622a,U+6234,U+626b,U+626d,U+6270,U+62b1,U+62bc,U+62c6,U+62d2,U+62d6,U+62dc,U+6316,U+6355,U+63ed,U+6447,U+64a4,U+65e8,U+65f1,U+6606,U+6628,U+664b,U+6668,U+6682,U+66f9,U+66fc,U+66ff,U+6717,U+675c,U+676d,U+679a,U+67aa,U+67ec,U+6842,U+6851,U+695a,U+6b20,U+6b23,U+6b32,U+6bc5,U+6c60,U+6cea,U+6cf3,U+6d53,U+6d66,U+6d69,U+6d6e,U+6d9b,U+6db2,U+6de1,U+6dfb,U+6ed1,U+6eda,U+6ee8,U+6f2b,U+706d,U+7089,U+708e,U+70ad-70ae,U+70b8,U+70e7,U+7126,U+71c3,U+71d5,U+7206,U+731b,U+73a9,U+73bb,U+74dc,U+7532,U+75c7,U+7687,U+76d7,U+76f2,U+788d,U+78e8,U+79e6,U+7a46,U+7a97,U+7af9,U+7bee,U+7c4d,U+7c89,U+7cd6,U+7eb5,U+7ebd,U+7ed8,U+8017,U+8033,U+80c0,U+80f6,U+8138,U+81a8,U+827e,U+82b3,U+82d7,U+83b1,U+84dd,U+864e,U+865a,U+866b,U+86cb,U+888b,U+89e6,U+8bca,U+8bde,U+8c0a,U+8c46,U+8c8c,U+8d29,U+8d4f,U+8d56,U+8d5a,U+8d60,U+8d62,U+8f7f,U+8f88,U+8f96,U+8f9e-8f9f,U+8fdf,U+8fea,U+8ff7,U+9012,U+9075,U+90bb,U+90ca,U+9178,U+9192,U+91ca,U+94bb,U+94dc,U+94fa,U+9501,U+9505,U+950b,U+9521,U+955c,U+9634,U+963b,U+9655,U+9675,U+9887,U+9891,U+9971,U+9a76,U+9a97,U+9ed8;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/1e1943302be7a103043baf5026f347cf.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+4e01,U+4e39,U+4e4f,U+4e54,U+4e58,U+4ea1,U+4f10,U+4f19,U+4f26,U+4f2a,U+4f34,U+4f5b,U+4fa8,U+4fb5,U+504f,U+51ac,U+51b0,U+51ed,U+5211,U+5242,U+52a3,U+52b2,U+52c3,U+52d2,U+53a6,U+53e5,U+5417,U+5448,U+5510,U+552f,U+5706,U+5851,U+58a8,U+5949,U+5954,U+59d3,U+5a5a,U+5b8b,U+5bab,U+5c0a,U+5c4b,U+5ce1,U+5e10,U+5e78,U+5e8a,U+5e9f,U+5ec9,U+5f1f,U+5f31,U+5f39,U+5f84,U+5faa,U+5fbd,U+5fd8-5fd9,U+5fe0,U+5fe7,U+6015,U+6062,U+6069,U+6076,U+6089,U+60a8,U+60ef,U+624e,U+6263,U+6298,U+62bd,U+62d4,U+62e8,U+6311,U+6350,U+6389,U+63f4,U+6446,U+644a,U+6469,U+654f,U+6562,U+656c,U+65e6,U+65fa,U+66b4,U+670b,U+6740,U+676f,U+67f1,U+6881,U+68a6,U+68cb,U+6982,U+6a2a,U+6b96,U+6beb,U+6c89,U+6c9f,U+6cc9,U+6d01,U+6d17,U+6d1b,U+6d1e,U+6d6a,U+6d8c,U+6df7,U+6e10,U+6e14,U+6e21,U+706f,U+70bc,U+714c,U+7259,U+732a,U+73cd,U+7518,U+7545,U+755c,U+756a,U+7586,U+7591,U+75db,U+76c8,U+76d0,U+76d8,U+7801,U+795d,U+7965,U+79e9,U+7a3f,U+7a77,U+7a7f,U+7aef,U+7bb1,U+7ea0,U+7ed5,U+7edc,U+7f13,U+7f29,U+7f34,U+7f8a,U+8015,U+8058,U+8083,U+80af,U+80c6,U+80de,U+811a,U+817e,U+820d,U+8352,U+83ab,U+84c4,U+852c,U+8584,U+88d5,U+8bda,U+8be2,U+8bf8,U+8bfa,U+8c0b,U+8c13,U+8c37,U+8c6a,U+8d1d,U+8d34,U+8d3a,U+8d54,U+8d74,U+8da3,U+8dd1,U+8ddf,U+8df3,U+8f6f,U+8f91,U+8f9b,U+8fc1,U+8fc8,U+8fd4,U+8feb,U+900f,U+906d,U+907f-9080,U+90a6,U+90ed,U+95ed,U+9614,U+9635,U+9676,U+9686,U+96c5,U+9759,U+978b,U+9876,U+9881,U+9910,U+9970,U+9e21,U+9ea6,U+9ebb,U+9ece,U+9f84;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/a80aedb146493b646b6b0d66ff755eeb.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+4e1d,U+4e3d,U+4e4c,U+4e95,U+4eab,U+4eae,U+4ec1,U+4eea,U+4f0a,U+4f11,U+4f30,U+4f3c,U+4f73,U+5012,U+503e,U+507f,U+50a8,U+517c,U+518c,U+5192,U+51a0,U+51b7,U+51e1,U+52c7,U+52e4,U+5377,U+539a,U+53eb,U+53f6,U+542b,U+542f,U+5462,U+5473,U+54e5,U+54ea,U+5531,U+574f-5750,U+5757,U+5761,U+5766,U+5802,U+5854,U+585e,U+58ee,U+593a,U+5b59,U+5b88,U+5b97,U+5b9c,U+5bbd-5bbe,U+5bfb,U+5c01,U+5c04,U+5c24,U+5c97,U+5cf0,U+5e84,U+5ef6,U+5f02,U+5f52,U+5f90,U+5fae,U+6000,U+60a3,U+60ca,U+60e0,U+620f,U+62a2,U+62b5,U+62d3,U+62e9,U+6302,U+638c,U+63e1,U+64cd,U+653b,U+6563,U+65d7,U+660c,U+6653,U+667a,U+672b,U+6731,U+6770,U+6790,U+67b6,U+67d3,U+6863,U+6885,U+68b0,U+68ee,U+6b8a-6b8b,U+6bcd,U+6bd2,U+6bd5,U+6c61,U+6c88,U+6cdb,U+6ce5,U+6cf0,U+6d59,U+6d89,U+6da6,U+6da8,U+6e20,U+6f5c,U+7075,U+719f,U+7236,U+725b,U+7267,U+73e0,U+745e,U+74e6,U+75be,U+76d6,U+76db,U+76df,U+76fe,U+77db,U+786c,U+793c,U+7981,U+79cb,U+79d8,U+79df,U+7adf,U+7ae5,U+7b11,U+7b26,U+7b54,U+7bc7,U+7d2f,U+7eaf,U+7eba,U+7f72,U+7ffb,U+805a,U+8089,U+80a5,U+80cc,U+80e1,U+8150,U+82e5,U+8336,U+8428,U+8463,U+8499,U+8651,U+8861,U+8863,U+88c1,U+89c8,U+8bcd,U+8bd7,U+8bef,U+8bfe,U+8c01,U+8c22,U+8d25,U+8d5e,U+8d75-8d76,U+8d8b,U+8dc3,U+8ddd,U+8de8,U+8f68,U+8f7d,U+8f86,U+8f89,U+8fbd,U+8ff9,U+9014,U+9057,U+90ae,U+90d1,U+91ce,U+9274,U+949f,U+9519,U+9526,U+952e,U+9644,U+96c4,U+96e8,U+96ea,U+96f6-96f7,U+9707,U+9732,U+996e,U+9a7b,U+9aa8,U+9c7c,U+9c81,U+9e4f,U+9f50;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/9368b95a170036d22d586b63ce1aebcf.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+4e45,U+4e4e,U+4e71,U+4e8f,U+4e92,U+4ea9,U+4ed8,U+4f0d,U+4f1f,U+4f24,U+4fc4,U+500d,U+5019,U+501f,U+503a,U+5047,U+505c,U+514d,U+5178,U+51b2,U+520a,U+521a,U+5224,U+5238,U+523b,U+52b1,U+5348,U+535a,U+5361,U+5371,U+5385,U+538b,U+53ec,U+53f3,U+5409,U+5426,U+5434,U+5438,U+547c,U+54c8,U+559c,U+56ed,U+56fa,U+5733,U+590f,U+591c,U+591f,U+5947,U+594b,U+5965,U+5987,U+5a01,U+5b63,U+5b69,U+5b8f,U+5c1a,U+5c3c,U+5c9b,U+5cb8,U+5ddd,U+5de6,U+5de8,U+5e01,U+5e2e,U+5e45,U+5e55,U+5e86,U+5e8f,U+5e93,U+5f55,U+5f69,U+5f7b,U+600e,U+6025,U+613f,U+6258,U+626c,U+6276,U+627e,U+6297,U+62cd,U+62e5,U+62ec,U+62ff,U+632f,U+6388,U+63a2,U+6444,U+64ad,U+6545,U+6551,U+65a4,U+65e2,U+65e7,U+6620,U+665a,U+66f2,U+671d,U+6728,U+6742,U+675f,U+6768,U+677e-677f,U+6865,U+68c9,U+690d,U+697c,U+6b22,U+6b4c,U+6b7b,U+6c38,U+6c49,U+6cbf,U+6d0b,U+6d25,U+6d2a,U+6d4b,U+6e29,U+6e7e,U+6f6e,U+6fb3,U+6fc0,U+707e,U+70df,U+7164,U+7389,U+751a,U+7533,U+7537,U+7597,U+767b,U+76ae,U+77ed,U+7956,U+79c0,U+79fb,U+7b14,U+7b51,U+7b79,U+7b7e,U+7b80,U+7d22,U+7e41,U+7eb7-7eb8,U+7ec3,U+7ec6,U+7ecd,U+7edd,U+7ee9,U+7eff,U+7f3a,U+7f51,U+7f5a,U+7f6a,U+8111,U+8131,U+821e,U+822c,U+8239,U+8270,U+8349,U+85cf,U+867d,U+8840,U+8857,U+89c9,U+89d2,U+8a89,U+8b66,U+8ba2,U+8ba8,U+8bc9,U+8bed,U+8bfb,U+8d21,U+8d2f,U+8d35,U+8d37,U+8df5,U+8f6e,U+8f93,U+8fc5,U+8fce,U+8fdd,U+8ff0,U+8ffd,U+9000,U+904d,U+9093,U+9152,U+9646,U+969c,U+97e9,U+97f3,U+987a,U+987f,U+996d,U+9c9c,U+9f13;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/fd1a5d4bc3274d5448a4eb757011a008.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+4e03,U+4e14,U+4e30,U+4e34,U+4e60,U+4e70,U+4e88,U+4e91,U+4eb2,U+4ec0,U+4ecb,U+4ecd,U+4ee4,U+4f59,U+4f8b,U+4fc3,U+4fee,U+5065,U+50cf,U+5145,U+516d,U+5170,U+5175,U+5199,U+51cf,U+51fb,U+5218,U+5267,U+52aa,U+5347,U+534a,U+5356,U+536b,U+5370,U+5374,U+53cb,U+53e4,U+53e6,U+53f7,U+5403,U+5428,U+542c,U+5584,U+5668,U+56f0,U+56f4,U+56fe,U+57df,U+57f9,U+58f0,U+592a-592b,U+5957,U+5b57,U+5b81,U+5b98,U+5b9d,U+5ba1,U+5ba3-5ba4,U+5bb3,U+5bb9,U+5bc6,U+5bdf,U+5c3d,U+5c45,U+5c4a,U+5c5e,U+5c81,U+5dee,U+5df4,U+5e0c,U+5e95,U+5e97,U+5ea7,U+5ead,U+5eb7,U+5f81,U+5f85,U+5ff5,U+6001,U+6267,U+6269,U+62c5,U+62db,U+6325,U+635f,U+6362,U+6392,U+63a7,U+63aa,U+641e,U+6597,U+65c5,U+65e9,U+661f,U+6625,U+663e,U+666e-666f,U+66fe,U+6750,U+67d0,U+6811,U+6838,U+6b27,U+6b62,U+6b66,U+6bdb,U+6c47,U+6c7d,U+6c99,U+6ce2,U+6cfd,U+6d32,U+6d3e,U+6e56,U+6f14,U+706b,U+70c8,U+7231,U+7247,U+724c,U+72af,U+72b6,U+72ec,U+732e,U+73ed,U+7403,U+7530,U+753b,U+7559,U+7565,U+767d,U+76d1,U+773c,U+7763,U+77ff,U+7968,U+798f,U+79bb,U+79c1,U+79f0,U+7a33,U+7a81,U+7ad9,U+7ade,U+7ae0,U+7cae,U+7d20,U+7d27,U+7ea2,U+7eb3,U+7ec8,U+7ee7,U+7efc,U+7f16,U+7f57,U+7f6e,U+80dc,U+81f4,U+822a,U+826f,U+82cf,U+82e6,U+8363,U+83dc,U+8457,U+878d,U+8865,U+8a00,U+8bad,U+8bb2,U+8bd5,U+8bf7,U+8d2b,U+8d2d,U+8d85,U+8f7b,U+9001-9002,U+9010,U+9047,U+9488,U+94a2,U+9633,U+9636,U+963f,U+9648,U+964d,U+9664,U+9669,U+968f,U+9760,U+987e,U+9884,U+98de,U+9986,U+9999,U+9ed1,U+9f99;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/2152ffd36af4d0cf4d20bc4246f44e80.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+4e25,U+4e48,U+4e50,U+4e5d,U+4e9a,U+4ec5,U+4efd,U+4f17,U+4f4e-4f4f,U+4f55,U+4f60,U+4f9b,U+4f9d,U+4fbf,U+503c,U+513f,U+5149,U+514b,U+516b,U+5174,U+517b,U+518d,U+51b5,U+5207,U+5217,U+5219,U+521d,U+526f,U+529f,U+52a9,U+52b3,U+5305,U+533b,U+5343,U+5360,U+5373,U+5386,U+53c2,U+53cc-53cd,U+53f2,U+544a,U+5468,U+547d,U+54cd,U+552e,U+56de,U+571f,U+5747,U+575a,U+5883,U+58eb,U+5907,U+590d,U+592e,U+5931,U+5956,U+5973,U+5979,U+59cb,U+5b58,U+5b83,U+5b8c,U+5ba2,U+5bcc,U+5c14,U+5c42,U+5dde,U+5df1,U+5e03,U+5e08,U+5e26,U+5e2d,U+5f15,U+5f71,U+5f80,U+5f8b,U+5fb7,U+5feb,U+601d,U+606f,U+611f,U+6237,U+623f,U+6253,U+627f,U+6293,U+62a4,U+62c9,U+6309,U+63a8,U+6574,U+6599,U+65ad,U+65af,U+65cf,U+671b,U+672a,U+674e,U+6781,U+6821,U+6839,U+6848,U+68c0,U+6a21,U+6b3e,U+6bb5,U+6c14,U+6cb3,U+6cb9,U+6ce8,U+6d88,U+6e05,U+6e2f,U+6e38,U+6e90,U+6ee1,U+70ed,U+7167,U+7248,U+7387,U+738b,U+73af,U+75c5,U+76f4,U+771f,U+77e5,U+77f3,U+7834,U+7840,U+786e,U+793a,U+795e,U+7a7a,U+7b56,U+7b97,U+7c73,U+7c7b,U+7ea6,U+7eaa,U+7ebf,U+7eed,U+7ef4,U+7fa4,U+8003,U+80a1,U+8272,U+827a,U+8282,U+82b1,U+82f1,U+8303,U+836f,U+83b7,U+843d,U+88c5,U+8ba9,U+8baf,U+8bb8,U+8bbf,U+8bc4,U+8bc6,U+8bdd,U+8be5,U+8c08,U+8c61,U+8d1f,U+8d22-8d23,U+8d27,U+8d38,U+8d5b,U+8d70,U+8d8a,U+8db3,U+8eab,U+8f83,U+8fb9,U+8fdc,U+8fde,U+9009,U+901f,U+914d,U+91c7,U+94b1,U+94c1,U+94f6,U+9547,U+95fb,U+9632,U+9650,U+9752,U+975e,U+987b,U+989d,U+98df,U+9996,U+9a6c,U+9a8c,U+9ec4;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/47bd6edb5e7a01df57836c447fc5222b.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+4e13,U+4e16,U+4e1c,U+4e24,U+4e3e,U+4e49,U+4e61,U+4e66,U+4e89,U+4e8c,U+4e94,U+4e9b,U+4ea4,U+4eac,U+4ebf,U+4eca,U+4ef6-4ef7,U+4efb,U+4f18,U+4f20,U+4f46,U+4fe1,U+505a,U+5148,U+515a,U+5171,U+5177,U+519b,U+51b3,U+51c6,U+51e0,U+5212,U+521b,U+522b,U+529e,U+52bf,U+534e-534f,U+5355,U+5357,U+5382,U+539f,U+53bb,U+53bf,U+53c8,U+53ca,U+53d6-53d8,U+53e3,U+53ea,U+53f0,U+5411,U+56db,U+56e0,U+56e2,U+578b,U+57ce,U+5904,U+5934,U+5982,U+5b89,U+5bfc,U+5c11,U+5c40,U+5c71,U+5e38,U+5e72,U+5e76,U+5e7f,U+5e94,U+5e9c,U+5f0f,U+5f20,U+5f3a,U+5f62,U+5f88,U+5fc5,U+5fd7,U+6027,U+60c5,U+60f3,U+610f,U+6216,U+6218,U+624b,U+624d,U+6279,U+628a,U+6295,U+6301,U+6307,U+636e,U+63a5,U+652f,U+6536,U+653e,U+6548,U+6559,U+6570,U+65bd,U+65e0,U+6613,U+66f4,U+6700,U+670d,U+671f,U+6743,U+6751,U+6761,U+6784,U+6797,U+679c,U+67e5,U+6807,U+6837,U+683c,U+6b63-6b65,U+6bcf,U+6bd4,U+6c42,U+6c5f,U+6ca1,U+6cbb,U+6d3b,U+6d41,U+6df1,U+70b9,U+7136,U+7269,U+7279,U+7531,U+754c,U+767e,U+76ca,U+76f8,U+770b,U+7814,U+79ef,U+7a0b,U+7a0e,U+7a76,U+7cbe,U+7cfb,U+7ea7,U+7ec4,U+7ec7,U+7ed3,U+7ed9,U+7edf,U+7f8e,U+8001,U+804c,U+8054,U+80b2,U+81f3,U+8425,U+8868,U+88ab,U+897f,U+89c1-89c2,U+89c4,U+89c6,U+89e3,U+8ba1,U+8ba4,U+8bae,U+8bb0,U+8bba,U+8bc1,U+8c03,U+8d28,U+8d39,U+8def,U+8f66,U+8f6c,U+8fbe,U+8fd0-8fd1,U+9020,U+9053,U+90a3,U+91cc,U+9500,U+95e8,U+95f4,U+961f,U+9645,U+9662,U+96be,U+96c6,U+9700,U+9769,U+9879,U+9886,U+9898,U+98ce;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/0cccf572d140d2c4a599207da239a084.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+3001-3002,U+3008-3011,U+3014-3017,U+4e00,U+4e07,U+4e09-4e0b,U+4e0d-4e0e,U+4e1a,U+4e2a,U+4e2d,U+4e3a-4e3b,U+4e4b,U+4e5f,U+4e86,U+4e8b,U+4e8e,U+4ea7,U+4eba,U+4ece,U+4ed6,U+4ee3,U+4ee5,U+4eec,U+4f01,U+4f1a,U+4f4d,U+4f53,U+4f5c,U+4f7f,U+4fdd,U+5143,U+5165,U+5168,U+516c,U+5173,U+5176,U+5185,U+519c,U+51fa,U+5206,U+5229,U+5230,U+5236,U+524d,U+529b,U+52a0-52a1,U+52a8,U+5316-5317,U+533a,U+5341,U+53d1,U+53ef,U+53f8,U+5404,U+5408,U+540c-540e,U+5458,U+548c,U+54c1,U+5546,U+56fd,U+5728,U+5730,U+573a,U+57fa,U+589e,U+5916,U+591a,U+5927,U+5929,U+597d,U+59d4,U+5b50,U+5b66,U+5b9a,U+5b9e,U+5bb6,U+5bf9,U+5c06,U+5c0f,U+5c31,U+5c55,U+5de5,U+5df2,U+5e02,U+5e73-5e74,U+5ea6,U+5efa,U+5f00,U+5f53,U+5f97,U+5fc3,U+603b,U+6210-6211,U+6240,U+6280,U+62a5,U+63d0,U+6539,U+653f,U+6587,U+65b0,U+65b9,U+65e5,U+65f6,U+660e,U+662f,U+6708-6709,U+672c,U+672f,U+673a,U+6765,U+6b21,U+6c11,U+6c34,U+6cd5,U+6d4e,U+6d77,U+73b0,U+7406,U+751f,U+7528,U+7535,U+7684,U+76ee,U+7740,U+793e,U+79cd,U+79d1,U+7acb,U+7b2c,U+7b49,U+7ba1,U+7ecf,U+8005,U+800c,U+80fd,U+81ea,U+884c,U+8981,U+8bbe,U+8bf4,U+8d44,U+8d77,U+8fc7,U+8fd8-8fd9,U+8fdb,U+901a,U+90e8,U+90fd,U+91cd,U+91cf,U+91d1,U+957f,U+95ee,U+9762,U+9ad8,U+fe30-fe31,U+fe33,U+fe35-fe44;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/be1cdfb76875e99653006ba405cedb5a.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+9943,U+9945-994e,U+9950-9953,U+9956-9959,U+995b-995c,U+995e-9962,U+9966,U+9973,U+9978-9979,U+997b,U+9983,U+9989,U+998c,U+999c-999e,U+99a1,U+99a3,U+99a6-99a7,U+99ab-99b5,U+99b9-99bd,U+99bf,U+99c1-99c9,U+99cb-99d9,U+99db-99df,U+99e1-99e5,U+99e7,U+99e9-99ea,U+99ec-99ee,U+99f0-99f2,U+99f4,U+99f6-99ff,U+9a01-9a0b,U+9a0d-9a0f,U+9a11-9a16,U+9a19-9a1e,U+9a20,U+9a22;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/5b86645531fae7e93c95ba7cafc8a3b2.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+7b6d,U+7b6f-7b70,U+7b73-7b74,U+7b76,U+7b78,U+7b7a,U+7b7c,U+7b7f,U+7b82,U+7b84,U+7b86-7b88,U+7b8a-7b8c,U+7b8e-7b8f,U+7b91-7b93,U+7b96,U+7b98-7b9b,U+7b9e-7ba0,U+7ba4,U+7baf,U+7bb5,U+7bb7,U+7bb9,U+7bbc,U+7bbe,U+7bc0,U+7bc4-7bc5,U+7bc9-7bcb,U+7bce-7bcf,U+7bd4-7bd8,U+7bdb-7bdc,U+7bde-7be0,U+7be2-7be4,U+7be7-7be9,U+7beb,U+7bed,U+7bef-7bf0,U+7bf2-7bf4,U+7bf6,U+7bf8-7bf9,U+7bfb,U+7bfd,U+7bff-7c03,U+7c05-7c06,U+7c09-7c0a,U+7c0d-7c0e,U+7c10-7c15,U+7c17,U+7c19,U+7c1c-7c1e,U+7c20-7c23,U+7c25,U+7c28-7c29,U+7c2b-7c2d,U+7c30-7c31,U+7c33,U+7c36-7c37,U+7c39,U+7c3b-7c3e,U+7c43,U+7c45,U+7c47-7c4a,U+7c4c,U+7c4f-7c51,U+7c53-7c54,U+7c56-7c5c,U+7c5e-7c5f;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/4eeed238e74d9b28900871fc871198ae.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+9859-9860,U+9862-9867,U+9869-986c,U+986f-9874,U+988b,U+988e,U+9899,U+98a8-98b3,U+98b6-98b8,U+98ba-98c4,U+98c6-98c9,U+98cb-98cc,U+98cf-98d0,U+98d4,U+98d7,U+98db-98dc,U+98e0-98e3,U+98e5,U+98e9-98eb,U+98ed-98f4,U+98f6,U+98f9-98fa,U+98fc-98fe,U+9900,U+9902-9903,U+9905,U+9907-990a,U+990c,U+9911-9918,U+991a-9921,U+9924-9925,U+9927-992d,U+992f-9933,U+9935,U+9937,U+993a-9942;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/47c0cd66aa782b179a716be052b8904a.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+ed-113,U+116-122,U+124-12b,U+12e-14d,U+150-17e,U+192-193,U+1c0,U+1c2,U+1cd-1dc,U+1f5,U+2da,U+2dc,U+304,U+308,U+2002-2003,U+2010-2016,U+2018-201a,U+201c-201e,U+2020-2022,U+2025-2026,U+2030,U+2032-2033,U+2039-203e,U+2044,U+2049,U+2074,U+20ac,U+2122,U+2191,U+2193,U+2212;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/ea681ba2827a2a5490fce1f1a00092c5.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+1-7,U+a-c,U+e-7e,U+a0-ec;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/c02c7506acc9c722a654facb060eb1af.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+4e2c,U+4e3f,U+5107,U+5155,U+5216,U+5281,U+5293,U+53b6,U+54f3,U+5523,U+553c,U+570a,U+5819,U+5adc,U+5c88,U+5c8d,U+5e3b,U+5f61,U+5fee,U+6067,U+614a,U+615d,U+62f6,U+63ad,U+63be,U+6420,U+6434,U+6484,U+6499,U+67d9,U+67dd,U+6920,U+6a8e,U+6aa0,U+6b43,U+6bf5,U+6c15,U+6d5e,U+6ee0,U+6f46,U+717a,U+71e0,U+72c1,U+72e8,U+73e7,U+746d,U+754e,U+75b0,U+7603,U+7722,U+778d,U+77e7,U+7809,U+7811,U+7857,U+78c9,U+78f2,U+7946,U+7967,U+799a,U+7b45,U+7b9d,U+7ba2,U+7cd7,U+7f32,U+8014,U+80c2,U+80d9,U+810e,U+8159,U+817d,U+81a3,U+81aa,U+8201,U+833c,U+836e,U+83e5,U+8459,U+848e,U+84cd,U+84d0,U+84f0,U+85b7,U+871e,U+8723,U+8729,U+8753,U+877e,U+878b,U+8793,U+87d3,U+88d2,U+8966,U+89dc,U+89eb,U+8b26,U+8bf6,U+8c2e,U+8d33,U+8d47,U+8d55,U+8e2c,U+8e9c,U+8e9e,U+8ece,U+8fee,U+9139,U+914f,U+9174,U+9191,U+928e,U+94f4,U+952b,U+953c,U+9564,U+960c,U+9622,U+9674,U+98a5,U+9a98,U+9a9f,U+9b48,U+9ca6,U+9cb0,U+9cba,U+9ccb,U+9e32,U+9e88,U+9eb4,U+9f3d,U+9f44,U+9f86;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/dd1681d11e9bea2958ffc2c5f69b7f53.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+932e-932f,U+9332-9336,U+9338-933c,U+9340-9341,U+9343-9344,U+9346-9352,U+9354-935c,U+935e,U+9360-9361,U+9363-9365,U+9367,U+9369,U+936b-936e,U+9370-9371,U+9373,U+9375-9377,U+9379-937c,U+937e,U+9380,U+9382-9384,U+9387-938e,U+9391-9392,U+9394-939b,U+939d-939f,U+93a1-93aa,U+93ac-93b5,U+93b7-93ba,U+93bf-93c4,U+93c6-93c8,U+93cc-93d2,U+93d4-93d5,U+93d7-93d9;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/22162d6887e0ad3e1eca464c6b970f78.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+9b0c-9b0e,U+9b10-9b12,U+9b15-9b1a,U+9b1c-9b1e,U+9b20,U+9b22,U+9b24-9b25,U+9b27-9b2b,U+9b2d-9b2e,U+9b31,U+9b33-9b37,U+9b3a,U+9b3d-9b3f,U+9b46,U+9b4a-9b4c,U+9b4e,U+9b52,U+9b55-9b56,U+9b58-9b5b,U+9b5e-9b61,U+9b63-9b68,U+9b6b-9b6c,U+9b6f-9b77,U+9b79-9b7e,U+9b80-9b88,U+9b8a,U+9b8e-9b93,U+9b95-9b97,U+9b9a-9b9b,U+9b9d-9ba2,U+9ba4-9ba8,U+9baa-9baf,U+9bb1-9bb2,U+9bb4-9bb6,U+9bb8-9bb9,U+9bbb,U+9bbd-9bc1,U+9bc3-9bc4,U+9bc6-9bc7;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/0bdea1503a6f3bfa0aade031ee58e092.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+6a9b-6a9f,U+6aa1-6aa6,U+6aa8,U+6aaa,U+6aad-6aaf,U+6ab3-6ab4,U+6ab6-6abb,U+6abd,U+6ac1-6ac3,U+6ac5-6ac7,U+6acb-6acd,U+6acf-6ad1,U+6ad3-6ad4,U+6ad9-6ae2,U+6ae4-6ae5,U+6ae7-6ae8,U+6aea-6aec,U+6aee-6af3,U+6af8-6afd,U+6b00,U+6b02-6b05,U+6b08-6b0b,U+6b0f-6b13,U+6b16-6b1b,U+6b1d-6b1f,U+6b25,U+6b28,U+6b2c-6b2d,U+6b2f,U+6b31,U+6b33-6b36,U+6b38,U+6b3b-6b3d,U+6b3f,U+6b41-6b42,U+6b45,U+6b48,U+6b4a-6b4b,U+6b4d-6b4e,U+6b50-6b51,U+6b53-6b56,U+6b58,U+6b5b-6b5c,U+6b5e-6b61,U+6b69,U+6b6c;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/68d73384f5e42947fc37f4894d23ef1e.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+9c4e-9c50,U+9c52-9c60,U+9c62-9c63,U+9c65-9c6b,U+9c6d-9c6e,U+9c70-9c7a,U+9c7d-9c7e,U+9c80,U+9c83,U+9c89-9c8a,U+9c8c,U+9c8f,U+9c96-9c99,U+9c9d,U+9caa,U+9cac,U+9caf,U+9cb9,U+9cbe-9cc2,U+9cc8-9cc9,U+9cd1-9cd2,U+9cda-9cdb,U+9ce0-9ce1,U+9ce3-9ce7,U+9ce9-9ced,U+9cf0-9cf7,U+9cf9-9cfd,U+9cff-9d00,U+9d02-9d09,U+9d0b,U+9d0e,U+9d10-9d12,U+9d14-9d15,U+9d17-9d19,U+9d1b,U+9d1d-9d20,U+9d22-9d23,U+9d25-9d26,U+9d28-9d2c;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/ff339763f9cc6f798ca18d838fa46e0f.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+9f35-9f36,U+9f38,U+9f3a,U+9f40-9f43,U+9f45-9f4f,U+9f52-9f59,U+9f5b-9f72,U+9f74-9f7b,U+9f7e,U+9f81-9f82,U+9f8d,U+9f90-9f92,U+9f94-9f95,U+9f97-9f98,U+9f9c-9f9d,U+9fa2,U+9fa4,U+9fb4,U+9fbc-9fbe,U+9fcd-9fd0,U+9fd4,U+9feb-9fed,U+f91d,U+f928-f929,U+f936,U+f970,U+f992-f993,U+f999,U+f9c3,U+f9d0,U+f9dc,U+f9ec,U+fa0e-fa27;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/1d983980cb19c5a95af58cac1c219b08.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+85be-85c0,U+85c2-85c8,U+85ca-85cb,U+85cd-85ce,U+85d1-85d2,U+85d7-85da,U+85dd-85e3,U+85e5-85e6,U+85e8,U+85ea-85ed,U+85ef-85f2,U+85f4,U+85f6-85fa,U+85fd-85fe,U+8600-8602,U+8604,U+8606-8607,U+8609-860c,U+8610,U+8612-8613,U+8617-861c,U+861e-8626,U+862a,U+862c-8636,U+8639-863b,U+863e-8641,U+8643,U+8646-8648,U+864b-864c,U+8652-8653,U+8655-8657,U+8659,U+865b-865c,U+865f,U+8661,U+8663-8665,U+8667-866a,U+866d,U+866f-8670,U+8673-8675,U+8677;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/ab9a6fae60d97e95d32e80a0a4c8884d.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+9e54,U+9e56,U+9e59,U+9e5d,U+9e5f-9e62,U+9e65,U+9e6e-9e6f,U+9e72,U+9e74-9e75,U+9e78-9e7d,U+9e80-9e81,U+9e83,U+9e85-9e86,U+9e89-9e8a,U+9e8c-9e8e,U+9e91,U+9e94-9e97,U+9e99-9e9c,U+9e9e,U+9ea0-9ea1,U+9ea4-9ea5,U+9ea7-9eaa,U+9eac-9eb0,U+9eb5-9eb7,U+9eb9-9eba,U+9ebc,U+9ebf-9ec0,U+9ec2-9ec3,U+9ec7-9ec8,U+9ecc,U+9ed0,U+9ed2-9ed3,U+9ed5-9ed6,U+9ed9-9eda,U+9ede,U+9ee1,U+9ee4,U+9ee6,U+9ee8,U+9eeb,U+9eed-9eee,U+9ef0,U+9ef2-9ef7,U+9efa,U+9efd,U+9eff-9f03,U+9f06-9f0a,U+9f0f,U+9f12,U+9f15-9f16,U+9f18,U+9f1a-9f1c,U+9f1e,U+9f21,U+9f23-9f25,U+9f28-9f2b,U+9f2d-9f2e,U+9f30-9f34;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/34795bda0f85c495bf9d39dfcac19535.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+6fce-6fcf,U+6fd4-6fd5,U+6fd8,U+6fda-6fdd,U+6fdf,U+6fe2-6fe4,U+6fe6-6fe9,U+6feb-6fed,U+6ff0-6ff7,U+6ff9-6ffc,U+6ffe-7001,U+7004-7007,U+7009-700f,U+7014-7019,U+701c-7022,U+7024,U+7026-702c,U+702f-7034,U+7037-7038,U+703a-703c,U+703e-704b,U+704e,U+7051-7052,U+7054-7058,U+705a-705b,U+705d,U+705f-706a,U+706e,U+7071,U+7074,U+707a,U+707d,U+7081;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/49289ead0c5745ed82a863177aa6681c.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+9a23-9a25,U+9a27-9a2e,U+9a30-9a32,U+9a34-9a3a,U+9a3d-9a46,U+9a48-9a4a,U+9a4c-9a50,U+9a52-9a5b,U+9a5e-9a60,U+9a62,U+9a64-9a6b,U+9a72,U+9a83,U+9a89,U+9a8d-9a8e,U+9a95,U+9a99,U+9aa6,U+9aab,U+9aad,U+9aaf,U+9ab3-9ab5,U+9ab9,U+9abb,U+9abe-9abf,U+9ac3-9ac4,U+9ac6-9ac7,U+9aca,U+9acd-9ad0,U+9ad2,U+9ad4-9ad6,U+9ad9,U+9adc,U+9ade,U+9ae0,U+9ae2-9ae3,U+9ae5,U+9ae7,U+9ae9-9aea,U+9aec,U+9aee,U+9af1-9af4,U+9af6-9af7,U+9afa,U+9afc-9afe,U+9b01-9b02,U+9b04-9b06,U+9b0a-9b0b;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/0abc51d9d523e9ab2891221902264885.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+93da,U+93dc-93df,U+93e1-93e8,U+93ec,U+93ee,U+93f1,U+93f5-9400,U+9403-9404,U+9406-9407,U+9409-941a,U+941d,U+9420-9421,U+9426-942c,U+942e,U+9430-943d,U+943f-9441,U+9444-944c,U+944f-9455,U+9457,U+945a-945b,U+945d-945e,U+9460,U+9462-9465,U+9468-946a,U+946d-9470;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/eed5e6293a7157071b787bd5459a1fc0.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+7e0d-7e17,U+7e1a-7e3a,U+7e3c-7e40,U+7e42-7e46,U+7e48-7e4d,U+7e50-7e5a,U+7e5c-7e63,U+7e66-7e6b,U+7e6d-7e70,U+7e72-7e81,U+7e83,U+7e86-7e8f;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/62e27af1958832a154f548a7248feb92.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+9db9-9dd3,U+9dd5-9de9,U+9deb-9df0,U+9df2-9dfb,U+9dfd-9e07,U+9e09-9e0d,U+9e0f-9e15,U+9e17,U+9e19-9e1e,U+9e24,U+9e27,U+9e2e,U+9e30,U+9e3b-9e3c,U+9e40,U+9e4d,U+9e50,U+9e52;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/b59efbfcda44227109802c2ce200764a.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+9bc8-9bca,U+9bce-9bcf,U+9bd1-9be8,U+9bea-9bec,U+9bee-9bf5,U+9bf7-9bf8,U+9bfa,U+9bfd,U+9bff-9c00,U+9c04-9c0e,U+9c10,U+9c12-9c1d,U+9c20-9c25,U+9c27-9c37,U+9c39-9c41,U+9c44-9c4d;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/ed5703e74d08ea6f788a1e3a6ecd4c30.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+9d2d-9d33,U+9d36-9d38,U+9d3b,U+9d3d-9d48,U+9d4a-9d4c,U+9d4f-9d54,U+9d56-9d64,U+9d67-9d6c,U+9d6f-9d82,U+9d84-9d8d,U+9d8f-9d90,U+9d92-9d94,U+9d96-9dad,U+9daf,U+9db1-9db8;}
@font-face {font-family: "Yozai";src:local("Yozai"),url("../fonts/yozai/0d9c4d7a193ebe4d39972d03b0ec3e85.woff2") format("woff2");font-style: normal;font-weight: 400;font-display: swap;unicode-range:U+1f8-1f9,U+1fd,U+250-25a,U+25c,U+25e-261,U+264-268,U+26c-273,U+275,U+279-27b,U+27d-27e,U+281-284,U+288-28e,U+290-292,U+294-295,U+298,U+29d,U+2a1-2a2,U+2113,U+2cda0,U+2cda8,U+2cdad-2cdae,U+2cdd5,U+2ce18,U+2ce1a,U+2ce23,U+2ce26,U+2ce2a,U+2ce7c,U+2ce88,U+2ce93,U+2e9f5,U+2f804,U+2f80f,U+2f818,U+2f81a,U+2f822,U+2f828,U+2f82c,U+2f833,U+2f83f,U+2f852,U+2f862,U+2f86d,U+2f873,U+2f877,U+2f884,U+2f89a,U+2f8ac,U+2f8b2,U+2f8b6,U+2f8d3,U+2f8db-2f8dc,U+2f8e1,U+2f8ea,U+2f8ed,U+2f8fc,U+2f903,U+2f90b,U+2f90f,U+2f91a,U+2f920-2f921,U+2f945,U+2f947,U+2f96c,U+2f995,U+2f9d0,U+2f9df,U+30edd-30ede,U+ffffd;}
