<#assign majid1 = ctxMap.majid1!>
<#assign majid2 = ctxMap.majid2!>
<#if !majors??>
  <#assign majors = sqlt.sqlQueryForList("obe.getMajors")>
</#if>

<@p.card title="不同专业间对比">
  <@f.form id="compareForm2" action="compare_diff.ftl" ajaxSubmit=false method="get">
    <@f.ctrl tag="select" label="专业 1" name="majid1" class="form-control" required=true size=4>
      <option value=""></option>
      <#list majors as item>
        <option value="${item.majid}" ${(majid1 == item.majid)?then("selected", "")}>${item.majid} - ${item.majname}</option>
      </#list>
    </@f.ctrl>
    <@f.ctrl tag="select" label="专业 2" name="majid2" class="form-control" required=true size=4>
      <option value=""></option>
      <#list majors as item>
        <option value="${item.majid}" ${(majid2 == item.majid)?then("selected", "")}>${item.majid} - ${item.majname}</option>
      </#list>
    </@f.ctrl>
    <@f.ctrl tag="select" label="对比内容" name="type" required=true size=3>
      <option value=""></option>
      <#list app.compareTypes?filter(x -> x.for == 'multi') as item>
        <option value="${item.id}" ${(ctxMap.type! == item.id)?then("selected", "")}>${item.name}</option>
      </#list>
    </@f.ctrl>
    <@f.ctrl tag="input" label="　" type="submit" class="btn btn-primary btn-block" groupClass="mb-0" value="确定" size=1 />
  </@f.form>
</@p.card>
