(function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):e.holmes=t()})(this,function(){"use strict";var s="undefined"==typeof window?global:window,r=function(e,t){return-1!==e.indexOf(t)},l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e=function(){function a(e){this.value=e}function t(i){function s(e,t){try{var n=i[e](t),o=n.value;o instanceof a?Promise.resolve(o.value).then(function(e){s("next",e)},function(e){s("throw",e)}):r(n.done?"return":"normal",n.value)}catch(e){r("throw",e)}}function r(e,t){"return"===e?l.resolve({value:t,done:!0}):"throw"===e?l.reject(t):l.resolve({value:t,done:!1});l=l.next,l?s(l.key,l.arg):h=null}var l,h;this._invoke=function(o,i){return new Promise(function(e,t){var n={key:o,arg:i,resolve:e,reject:t,next:null};h?h=h.next=n:(l=h=n,s(o,i))})},"function"!=typeof i.return&&(this.return=void 0)}return"function"==typeof Symbol&&Symbol.asyncIterator&&(t.prototype[Symbol.asyncIterator]=function(){return this}),t.prototype.next=function(e){return this._invoke("next",e)},t.prototype.throw=function(e){return this._invoke("throw",e)},t.prototype.return=function(e){return this._invoke("return",e)},{wrap:function(e){return function(){return new t(e.apply(this,arguments))}},await:function(e){return new a(e)}}}(),h=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},t=function(){function o(e,t){for(var n,o=0;o<t.length;o++)n=t[o],n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}return function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e}}();var a={invalidInput:"The Holmes input was no <input> or contenteditable.",optionsObject:'The options need to be given inside an object like this:\n\nnew Holmes({\n  find:".result"\n});\n\nsee also https://haroen.me/holmes/doc/holmes.html',findOption:'A find argument is needed. That should be a querySelectorAll for each of the items you want to match individually. You should have something like:\n\nnew Holmes({\n  find:".result"\n});\n\nsee also https://haroen.me/holmes/doc/holmes.html',noInput:"Your Holmes.input didn't match a querySelector",impossiblePlaceholder:"The Holmes placeholder couldn't be put; the elements had no parent."},n=function(){function i(e){var n=this;h(this,i);var o=!1;if("object"!==("undefined"==typeof e?"undefined":l(e)))throw new Error(a.optionsObject);if("string"!=typeof e.find)throw new Error(a.findOption);var t={input:"input[type=search]",find:"",placeholder:void 0,mark:!1,class:{visible:void 0,hidden:"hidden"},dynamic:!1,minCharacters:0,hiddenAttr:!1,shouldShow:r,onHidden:void 0,onVisible:void 0,onEmpty:void 0,onFound:void 0,onInput:void 0};this.options=Object.assign({},t,e),this.options.class=Object.assign({},t.class,e.class),this.hidden=0,this.running=!1,window.addEventListener("DOMContentLoaded",function(){return n.start()}),this.search=function(){n.running=!0;var t=!1;n.searchString=n.inputString(),n.options.minCharacters&&0!==n.searchString.length&&n.options.minCharacters>n.searchString.length||(n.options.dynamic&&(n.elements=document.querySelectorAll(n.options.find),n.elementsLength=n.elements.length,n.elementsArray=Array.prototype.slice.call(n.elements)),n.options.mark&&(n._regex=new RegExp("("+n.searchString+")(?![^<]*>)","gi")),n.elementsArray.forEach(function(e){n.options.shouldShow(e.textContent.toLowerCase(),n.searchString)?(n._showElement(e),o&&"function"==typeof n.options.onFound&&n.options.onFound(n.placeholderNode),t=!0):n._hideElement(e)}),"function"==typeof n.options.onInput&&n.options.onInput(n.searchString),t?n.options.placeholder&&n._hideElement(n.placeholderNode):(n.options.placeholder&&n._showElement(n.placeholderNode),!1==o&&(o=!0,"function"==typeof n.options.onEmpty&&n.options.onEmpty(n.placeholderNode))))}}return t(i,[{key:"_hideElement",value:function(e){this.options.class.visible&&e.classList.remove(this.options.class.visible),e.classList.contains(this.options.class.hidden)||(e.classList.add(this.options.class.hidden),this.hidden++,"function"==typeof this.options.onHidden&&this.options.onHidden(e)),this.options.hiddenAttr&&e.setAttribute("hidden","true"),this.options.mark&&(e.innerHTML=e.innerHTML.replace(/<\/?mark>/g,""))}},{key:"_showElement",value:function(e){this.options.class.visible&&e.classList.add(this.options.class.visible),e.classList.contains(this.options.class.hidden)&&(e.classList.remove(this.options.class.hidden),this.hidden--,"function"==typeof this.options.onVisible&&this.options.onVisible(e)),this.options.hiddenAttr&&e.removeAttribute("hidden"),this.options.mark&&(e.innerHTML=e.innerHTML.replace(/<\/?mark>/g,""),this.searchString.length&&(e.innerHTML=e.innerHTML.replace(this._regex,"<mark>$1</mark>")))}},{key:"_inputHandler",value:function(){console.warn("You can now directly call .search() to refresh the results"),this.search()}},{key:"inputString",value:function(){if(this.input instanceof HTMLInputElement)return this.input.value.toLowerCase();if(this.input.isContentEditable)return this.input.textContent.toLowerCase();throw new Error(a.invalidInput)}},{key:"setInput",value:function(e){if(this.input instanceof HTMLInputElement)this.input.value=e;else if(this.input.isContentEditable)this.input.textContent=e;else throw new Error(a.invalidInput)}},{key:"start",value:function(){var e=document.querySelector(this.options.input);if(e instanceof HTMLElement)this.input=e;else throw new Error(a.noInput);if("string"==typeof this.options.find)this.elements=document.querySelectorAll(this.options.find);else throw new Error(a.findOption);if(this.elementsLength=this.elements.length,this.elementsArray=Array.prototype.slice.call(this.elements),this.hidden=0,"string"==typeof this.options.placeholder){var t=this.options.placeholder;if(this.placeholderNode=document.createElement("div"),this.placeholderNode.id="holmes-placeholder",this._hideElement(this.placeholderNode),this.placeholderNode.innerHTML=t,this.elements[0].parentNode instanceof Element)this.elements[0].parentNode.appendChild(this.placeholderNode);else throw new Error(a.impossiblePlaceholder)}if(this.options.class.visible){var n=this.options.class.visible;this.elementsArray.forEach(function(e){e.classList.add(n)})}this.input.addEventListener("input",this.search)}},{key:"stop",value:function(){var n=this;return new Promise(function(e,t){try{n.input.removeEventListener("input",n.search),n.options.placeholder&&(n.placeholderNode.parentNode?n.placeholderNode.parentNode.removeChild(n.placeholderNode):t(new Error(a.impossiblePlaceholder))),n.options.mark&&n.elementsArray.forEach(function(e){e.innerHTML=e.innerHTML.replace(/<\/?mark>/g,"")}),n.running=!1,e("This instance of Holmes has been stopped.")}catch(e){t(e)}})}},{key:"clear",value:function(){var t=this;this.setInput(""),this.elementsArray.forEach(function(e){t._showElement(e)}),this.options.placeholder&&this._hideElement(this.placeholderNode),this.hidden=0}},{key:"count",value:function(){return{all:this.elementsLength,hidden:this.hidden,visible:this.elementsLength-this.hidden}}}]),i}(),o=function(i){var e=function(){for(var e,t=arguments.length,n=Array(t),o=0;o<t;o++)n[o]=arguments[o];return e="undefined"!=typeof this&&this!==s?i.call.apply(i,[this].concat(n)):new(Function.prototype.bind.apply(i,[null].concat(n))),e};return e.__proto__=i,e.prototype=i.prototype,e}(n);return o});