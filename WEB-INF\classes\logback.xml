<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
      <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} -%kvp- %msg%n</pattern>
    </encoder>
  </appender>

  <!-- project default level -->
  <logger name="neo" level="DEBUG" />

  <root level="ERROR">
    <appender-ref ref="STDOUT" />
  </root>
</configuration>