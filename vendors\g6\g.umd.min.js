/*!
 * @antv/g
 * @description A core module for rendering engine implements DOM API.
 * @version 6.1.25
 * @date 5/23/2025, 7:01:37 AM
 * <AUTHOR>
 * @docs https://g.antv.antgroup.com/
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).G={})}(this,(function(t){"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},e(t)}function n(t){var n=function(t,n){if("object"!=e(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var i=r.call(t,n||"default");if("object"!=e(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==e(n)?n:n+""}function r(t,e,r){return(e=n(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function i(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function a(t){for(var e=1;arguments.length>e;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?i(Object(n),!0).forEach((function(e){r(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):i(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function o(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function s(t,e){for(var r=0;e.length>r;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,n(i.key),i)}}function u(t,e,n){return e&&s(t.prototype,e),n&&s(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function l(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);e>n;n++)r[n]=t[n];return r}function c(t,e){if(t){if("string"==typeof t)return l(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?l(t,e):void 0}}function h(t){return function(t){if(Array.isArray(t))return l(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||c(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(t){return f=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},f(t)}function d(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(d=function(){return!!t})()}function v(t,n){if(n&&("object"==e(n)||"function"==typeof n))return n;if(void 0!==n)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function p(t,e,n){return e=f(e),v(t,d()?Reflect.construct(e,n||[],f(t).constructor):e.apply(t,n))}function m(t,e){return m=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},m(t,e)}function y(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&m(t,e)}function g(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,a,o,s=[],u=!0,l=!1;try{if(a=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=a.call(n)).done)&&(s.push(r.value),s.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(l)throw i}}return s}}(t,e)||c(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var k={exports:{}};!function(t){var e=Object.prototype.hasOwnProperty,n="~";function r(){}function i(t,e,n){this.fn=t,this.context=e,this.once=n||!1}function a(t,e,r,a,o){if("function"!=typeof r)throw new TypeError("The listener must be a function");var s=new i(r,a||t,o),u=n?n+e:e;return t._events[u]?t._events[u].fn?t._events[u]=[t._events[u],s]:t._events[u].push(s):(t._events[u]=s,t._eventsCount++),t}function o(t,e){0===--t._eventsCount?t._events=new r:delete t._events[e]}function s(){this._events=new r,this._eventsCount=0}Object.create&&(r.prototype=Object.create(null),(new r).__proto__||(n=!1)),s.prototype.eventNames=function(){var t,r,i=[];if(0===this._eventsCount)return i;for(r in t=this._events)e.call(t,r)&&i.push(n?r.slice(1):r);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(t)):i},s.prototype.listeners=function(t){var e=this._events[n?n+t:t];if(!e)return[];if(e.fn)return[e.fn];for(var r=0,i=e.length,a=Array(i);i>r;r++)a[r]=e[r].fn;return a},s.prototype.listenerCount=function(t){var e=this._events[n?n+t:t];return e?e.fn?1:e.length:0},s.prototype.emit=function(t,e,r,i,a,o){var s=n?n+t:t;if(!this._events[s])return!1;var u,l,c=this._events[s],h=arguments.length;if(c.fn){switch(c.once&&this.removeListener(t,c.fn,void 0,!0),h){case 1:return c.fn.call(c.context),!0;case 2:return c.fn.call(c.context,e),!0;case 3:return c.fn.call(c.context,e,r),!0;case 4:return c.fn.call(c.context,e,r,i),!0;case 5:return c.fn.call(c.context,e,r,i,a),!0;case 6:return c.fn.call(c.context,e,r,i,a,o),!0}for(l=1,u=Array(h-1);h>l;l++)u[l-1]=arguments[l];c.fn.apply(c.context,u)}else{var f,d=c.length;for(l=0;d>l;l++)switch(c[l].once&&this.removeListener(t,c[l].fn,void 0,!0),h){case 1:c[l].fn.call(c[l].context);break;case 2:c[l].fn.call(c[l].context,e);break;case 3:c[l].fn.call(c[l].context,e,r);break;case 4:c[l].fn.call(c[l].context,e,r,i);break;default:if(!u)for(f=1,u=Array(h-1);h>f;f++)u[f-1]=arguments[f];c[l].fn.apply(c[l].context,u)}}return!0},s.prototype.on=function(t,e,n){return a(this,t,e,n,!1)},s.prototype.once=function(t,e,n){return a(this,t,e,n,!0)},s.prototype.removeListener=function(t,e,r,i){var a=n?n+t:t;if(!this._events[a])return this;if(!e)return o(this,a),this;var s=this._events[a];if(s.fn)s.fn!==e||i&&!s.once||r&&s.context!==r||o(this,a);else{for(var u=0,l=[],c=s.length;c>u;u++)(s[u].fn!==e||i&&!s[u].once||r&&s[u].context!==r)&&l.push(s[u]);l.length?this._events[a]=1===l.length?l[0]:l:o(this,a)}return this},s.prototype.removeAllListeners=function(t){var e;return t?this._events[e=n?n+t:t]&&o(this,e):(this._events=new r,this._eventsCount=0),this},s.prototype.off=s.prototype.removeListener,s.prototype.addListener=s.prototype.on,s.prefixed=n,s.EventEmitter=s,t.exports=s}(k);var E=k.exports,x=1e-6,b="undefined"!=typeof Float32Array?Float32Array:Array;function T(){var t=new b(9);return b!=Float32Array&&(t[1]=0,t[2]=0,t[3]=0,t[5]=0,t[6]=0,t[7]=0),t[0]=1,t[4]=1,t[8]=1,t}function M(){var t=new b(16);return b!=Float32Array&&(t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[11]=0,t[12]=0,t[13]=0,t[14]=0),t[0]=1,t[5]=1,t[10]=1,t[15]=1,t}function w(t){var e=new b(16);return e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e[4]=t[4],e[5]=t[5],e[6]=t[6],e[7]=t[7],e[8]=t[8],e[9]=t[9],e[10]=t[10],e[11]=t[11],e[12]=t[12],e[13]=t[13],e[14]=t[14],e[15]=t[15],e}function N(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t[6]=e[6],t[7]=e[7],t[8]=e[8],t[9]=e[9],t[10]=e[10],t[11]=e[11],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15],t}function P(t,e,n,r,i,a,o,s,u,l,c,h,f,d,v,p,m){return t[0]=e,t[1]=n,t[2]=r,t[3]=i,t[4]=a,t[5]=o,t[6]=s,t[7]=u,t[8]=l,t[9]=c,t[10]=h,t[11]=f,t[12]=d,t[13]=v,t[14]=p,t[15]=m,t}function S(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=1,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=1,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t}function A(t,e){if(t===e){var n=e[1],r=e[2],i=e[3],a=e[6],o=e[7],s=e[11];t[1]=e[4],t[2]=e[8],t[3]=e[12],t[4]=n,t[6]=e[9],t[7]=e[13],t[8]=r,t[9]=a,t[11]=e[14],t[12]=i,t[13]=o,t[14]=s}else t[0]=e[0],t[1]=e[4],t[2]=e[8],t[3]=e[12],t[4]=e[1],t[5]=e[5],t[6]=e[9],t[7]=e[13],t[8]=e[2],t[9]=e[6],t[10]=e[10],t[11]=e[14],t[12]=e[3],t[13]=e[7],t[14]=e[11],t[15]=e[15];return t}function C(t,e){var n=e[0],r=e[1],i=e[2],a=e[3],o=e[4],s=e[5],u=e[6],l=e[7],c=e[8],h=e[9],f=e[10],d=e[11],v=e[12],p=e[13],m=e[14],y=e[15],g=n*s-r*o,k=n*u-i*o,E=n*l-a*o,x=r*u-i*s,b=r*l-a*s,T=i*l-a*u,M=c*p-h*v,w=c*m-f*v,N=c*y-d*v,P=h*m-f*p,S=h*y-d*p,A=f*y-d*m,C=g*A-k*S+E*P+x*N-b*w+T*M;return C?(t[0]=(s*A-u*S+l*P)*(C=1/C),t[1]=(i*S-r*A-a*P)*C,t[2]=(p*T-m*b+y*x)*C,t[3]=(f*b-h*T-d*x)*C,t[4]=(u*N-o*A-l*w)*C,t[5]=(n*A-i*N+a*w)*C,t[6]=(m*E-v*T-y*k)*C,t[7]=(c*T-f*E+d*k)*C,t[8]=(o*S-s*N+l*M)*C,t[9]=(r*N-n*S-a*M)*C,t[10]=(v*b-p*E+y*g)*C,t[11]=(h*E-c*b-d*g)*C,t[12]=(s*w-o*P-u*M)*C,t[13]=(n*P-r*w+i*M)*C,t[14]=(p*k-v*x-m*g)*C,t[15]=(c*x-h*k+f*g)*C,t):null}function R(t){var e=t[0],n=t[1],r=t[2],i=t[3],a=t[4],o=t[5],s=t[6],u=t[7],l=t[8],c=t[9],h=t[10],f=t[11],d=t[12],v=t[13],p=t[14],m=t[15];return(e*o-n*a)*(h*m-f*p)-(e*s-r*a)*(c*m-f*v)+(e*u-i*a)*(c*p-h*v)+(n*s-r*o)*(l*m-f*d)-(n*u-i*o)*(l*p-h*d)+(r*u-i*s)*(l*v-c*d)}function O(t,e,n){var r=e[0],i=e[1],a=e[2],o=e[3],s=e[4],u=e[5],l=e[6],c=e[7],h=e[8],f=e[9],d=e[10],v=e[11],p=e[12],m=e[13],y=e[14],g=e[15],k=n[0],E=n[1],x=n[2],b=n[3];return t[0]=k*r+E*s+x*h+b*p,t[1]=k*i+E*u+x*f+b*m,t[2]=k*a+E*l+x*d+b*y,t[3]=k*o+E*c+x*v+b*g,t[4]=(k=n[4])*r+(E=n[5])*s+(x=n[6])*h+(b=n[7])*p,t[5]=k*i+E*u+x*f+b*m,t[6]=k*a+E*l+x*d+b*y,t[7]=k*o+E*c+x*v+b*g,t[8]=(k=n[8])*r+(E=n[9])*s+(x=n[10])*h+(b=n[11])*p,t[9]=k*i+E*u+x*f+b*m,t[10]=k*a+E*l+x*d+b*y,t[11]=k*o+E*c+x*v+b*g,t[12]=(k=n[12])*r+(E=n[13])*s+(x=n[14])*h+(b=n[15])*p,t[13]=k*i+E*u+x*f+b*m,t[14]=k*a+E*l+x*d+b*y,t[15]=k*o+E*c+x*v+b*g,t}function L(t,e,n){var r,i,a,o,s,u,l,c,h,f,d,v,p=n[0],m=n[1],y=n[2];return e===t?(t[12]=e[0]*p+e[4]*m+e[8]*y+e[12],t[13]=e[1]*p+e[5]*m+e[9]*y+e[13],t[14]=e[2]*p+e[6]*m+e[10]*y+e[14],t[15]=e[3]*p+e[7]*m+e[11]*y+e[15]):(i=e[1],a=e[2],o=e[3],s=e[4],u=e[5],l=e[6],c=e[7],h=e[8],f=e[9],d=e[10],v=e[11],t[0]=r=e[0],t[1]=i,t[2]=a,t[3]=o,t[4]=s,t[5]=u,t[6]=l,t[7]=c,t[8]=h,t[9]=f,t[10]=d,t[11]=v,t[12]=r*p+s*m+h*y+e[12],t[13]=i*p+u*m+f*y+e[13],t[14]=a*p+l*m+d*y+e[14],t[15]=o*p+c*m+v*y+e[15]),t}function I(t,e,n){var r=Math.sin(n),i=Math.cos(n),a=e[4],o=e[5],s=e[6],u=e[7],l=e[8],c=e[9],h=e[10],f=e[11];return e!==t&&(t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15]),t[4]=a*i+l*r,t[5]=o*i+c*r,t[6]=s*i+h*r,t[7]=u*i+f*r,t[8]=l*i-a*r,t[9]=c*i-o*r,t[10]=h*i-s*r,t[11]=f*i-u*r,t}function _(t,e,n){var r=Math.sin(n),i=Math.cos(n),a=e[0],o=e[1],s=e[2],u=e[3],l=e[8],c=e[9],h=e[10],f=e[11];return e!==t&&(t[4]=e[4],t[5]=e[5],t[6]=e[6],t[7]=e[7],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15]),t[0]=a*i-l*r,t[1]=o*i-c*r,t[2]=s*i-h*r,t[3]=u*i-f*r,t[8]=a*r+l*i,t[9]=o*r+c*i,t[10]=s*r+h*i,t[11]=u*r+f*i,t}function D(t,e){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=1,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=1,t[11]=0,t[12]=e[0],t[13]=e[1],t[14]=e[2],t[15]=1,t}function F(t,e){return t[0]=e[0],t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=e[1],t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=e[2],t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t}function G(t,e,n){var r,i,a,o=n[0],s=n[1],u=n[2],l=Math.hypot(o,s,u);return x>l?null:(o*=l=1/l,s*=l,u*=l,r=Math.sin(e),t[0]=o*o*(a=1-(i=Math.cos(e)))+i,t[1]=s*o*a+u*r,t[2]=u*o*a-s*r,t[3]=0,t[4]=o*s*a-u*r,t[5]=s*s*a+i,t[6]=u*s*a+o*r,t[7]=0,t[8]=o*u*a+s*r,t[9]=s*u*a-o*r,t[10]=u*u*a+i,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t)}function B(t,e){var n=Math.sin(e),r=Math.cos(e);return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=r,t[6]=n,t[7]=0,t[8]=0,t[9]=-n,t[10]=r,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t}function V(t,e){var n=Math.sin(e),r=Math.cos(e);return t[0]=r,t[1]=0,t[2]=-n,t[3]=0,t[4]=0,t[5]=1,t[6]=0,t[7]=0,t[8]=n,t[9]=0,t[10]=r,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t}function Y(t,e){var n=Math.sin(e),r=Math.cos(e);return t[0]=r,t[1]=n,t[2]=0,t[3]=0,t[4]=-n,t[5]=r,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=1,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t}function U(t,e,n){var r=e[0],i=e[1],a=e[2],o=e[3],s=r+r,u=i+i,l=a+a,c=r*s,h=r*u,f=r*l,d=i*u,v=i*l,p=a*l,m=o*s,y=o*u,g=o*l;return t[0]=1-(d+p),t[1]=h+g,t[2]=f-y,t[3]=0,t[4]=h-g,t[5]=1-(c+p),t[6]=v+m,t[7]=0,t[8]=f+y,t[9]=v-m,t[10]=1-(c+d),t[11]=0,t[12]=n[0],t[13]=n[1],t[14]=n[2],t[15]=1,t}function z(t,e){return t[0]=e[12],t[1]=e[13],t[2]=e[14],t}function j(t,e){var n=e[4],r=e[5],i=e[6],a=e[8],o=e[9],s=e[10];return t[0]=Math.hypot(e[0],e[1],e[2]),t[1]=Math.hypot(n,r,i),t[2]=Math.hypot(a,o,s),t}function X(t,e){var n=new b(3);j(n,e);var r=1/n[0],i=1/n[1],a=1/n[2],o=e[0]*r,s=e[1]*i,u=e[2]*a,l=e[4]*r,c=e[5]*i,h=e[6]*a,f=e[8]*r,d=e[9]*i,v=e[10]*a,p=o+c+v,m=0;return p>0?(t[3]=.25*(m=2*Math.sqrt(p+1)),t[0]=(h-d)/m,t[1]=(f-u)/m,t[2]=(s-l)/m):o>c&&o>v?(t[3]=(h-d)/(m=2*Math.sqrt(1+o-c-v)),t[0]=.25*m,t[1]=(s+l)/m,t[2]=(f+u)/m):c>v?(t[3]=(f-u)/(m=2*Math.sqrt(1+c-o-v)),t[0]=(s+l)/m,t[1]=.25*m,t[2]=(h+d)/m):(t[3]=(s-l)/(m=2*Math.sqrt(1+v-o-c)),t[0]=(f+u)/m,t[1]=(h+d)/m,t[2]=.25*m),t}function H(t,e,n,r,i){var a=e[0],o=e[1],s=e[2],u=e[3],l=a+a,c=o+o,h=s+s,f=a*l,d=a*c,v=a*h,p=o*c,m=o*h,y=s*h,g=u*l,k=u*c,E=u*h,x=r[0],b=r[1],T=r[2],M=i[0],w=i[1],N=i[2],P=(1-(p+y))*x,S=(d+E)*x,A=(v-k)*x,C=(d-E)*b,R=(1-(f+y))*b,O=(m+g)*b,L=(v+k)*T,I=(m-g)*T,_=(1-(f+p))*T;return t[0]=P,t[1]=S,t[2]=A,t[3]=0,t[4]=C,t[5]=R,t[6]=O,t[7]=0,t[8]=L,t[9]=I,t[10]=_,t[11]=0,t[12]=n[0]+M-(P*M+C*w+L*N),t[13]=n[1]+w-(S*M+R*w+I*N),t[14]=n[2]+N-(A*M+O*w+_*N),t[15]=1,t}function W(t,e){var n=e[0],r=e[1],i=e[2],a=e[3],o=n+n,s=r+r,u=i+i,l=n*o,c=r*o,h=r*s,f=i*o,d=i*s,v=i*u,p=a*o,m=a*s,y=a*u;return t[0]=1-h-v,t[1]=c+y,t[2]=f-m,t[3]=0,t[4]=c-y,t[5]=1-l-v,t[6]=d+p,t[7]=0,t[8]=f+m,t[9]=d-p,t[10]=1-l-h,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t}function q(t,e,n,r,i){var a,o=1/Math.tan(e/2);return t[0]=o/n,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=o,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[11]=-1,t[12]=0,t[13]=0,t[15]=0,null!=i&&i!==1/0?(t[10]=(i+r)*(a=1/(r-i)),t[14]=2*i*r*a):(t[10]=-1,t[14]=-2*r),t}function Z(t,e,n,r,i,a,o){var s=1/(e-n),u=1/(r-i),l=1/(a-o);return t[0]=-2*s,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=-2*u,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=2*l,t[11]=0,t[12]=(e+n)*s,t[13]=(i+r)*u,t[14]=(o+a)*l,t[15]=1,t}Math.hypot||(Math.hypot=function(){for(var t=0,e=arguments.length;e--;)t+=arguments[e]*arguments[e];return Math.sqrt(t)});var K=Z;function Q(t,e,n,r,i,a,o){var s=1/(e-n),u=1/(r-i),l=1/(a-o);return t[0]=-2*s,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=-2*u,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=l,t[11]=0,t[12]=(e+n)*s,t[13]=(i+r)*u,t[14]=a*l,t[15]=1,t}function $(t,e,n,r){var i,a,o,s,u,l,c,h,f,d,v=e[0],p=e[1],m=e[2],y=r[0],g=r[1],k=r[2],E=n[0],b=n[1],T=n[2];return x>Math.abs(v-E)&&x>Math.abs(p-b)&&x>Math.abs(m-T)?S(t):(d=1/Math.hypot(c=v-E,h=p-b,f=m-T),(d=Math.hypot(i=g*(f*=d)-k*(h*=d),a=k*(c*=d)-y*f,o=y*h-g*c))?(i*=d=1/d,a*=d,o*=d):(i=0,a=0,o=0),(d=Math.hypot(s=h*o-f*a,u=f*i-c*o,l=c*a-h*i))?(s*=d=1/d,u*=d,l*=d):(s=0,u=0,l=0),t[0]=i,t[1]=s,t[2]=c,t[3]=0,t[4]=a,t[5]=u,t[6]=h,t[7]=0,t[8]=o,t[9]=l,t[10]=f,t[11]=0,t[12]=-(i*v+a*p+o*m),t[13]=-(s*v+u*p+l*m),t[14]=-(c*v+h*p+f*m),t[15]=1,t)}function J(t,e,n){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t[2]=e[2]-n[2],t[3]=e[3]-n[3],t[4]=e[4]-n[4],t[5]=e[5]-n[5],t[6]=e[6]-n[6],t[7]=e[7]-n[7],t[8]=e[8]-n[8],t[9]=e[9]-n[9],t[10]=e[10]-n[10],t[11]=e[11]-n[11],t[12]=e[12]-n[12],t[13]=e[13]-n[13],t[14]=e[14]-n[14],t[15]=e[15]-n[15],t}var tt=O,et=Object.freeze({__proto__:null,add:function(t,e,n){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t[2]=e[2]+n[2],t[3]=e[3]+n[3],t[4]=e[4]+n[4],t[5]=e[5]+n[5],t[6]=e[6]+n[6],t[7]=e[7]+n[7],t[8]=e[8]+n[8],t[9]=e[9]+n[9],t[10]=e[10]+n[10],t[11]=e[11]+n[11],t[12]=e[12]+n[12],t[13]=e[13]+n[13],t[14]=e[14]+n[14],t[15]=e[15]+n[15],t},adjoint:function(t,e){var n=e[0],r=e[1],i=e[2],a=e[3],o=e[4],s=e[5],u=e[6],l=e[7],c=e[8],h=e[9],f=e[10],d=e[11],v=e[12],p=e[13],m=e[14],y=e[15];return t[0]=s*(f*y-d*m)-h*(u*y-l*m)+p*(u*d-l*f),t[1]=-(r*(f*y-d*m)-h*(i*y-a*m)+p*(i*d-a*f)),t[2]=r*(u*y-l*m)-s*(i*y-a*m)+p*(i*l-a*u),t[3]=-(r*(u*d-l*f)-s*(i*d-a*f)+h*(i*l-a*u)),t[4]=-(o*(f*y-d*m)-c*(u*y-l*m)+v*(u*d-l*f)),t[5]=n*(f*y-d*m)-c*(i*y-a*m)+v*(i*d-a*f),t[6]=-(n*(u*y-l*m)-o*(i*y-a*m)+v*(i*l-a*u)),t[7]=n*(u*d-l*f)-o*(i*d-a*f)+c*(i*l-a*u),t[8]=o*(h*y-d*p)-c*(s*y-l*p)+v*(s*d-l*h),t[9]=-(n*(h*y-d*p)-c*(r*y-a*p)+v*(r*d-a*h)),t[10]=n*(s*y-l*p)-o*(r*y-a*p)+v*(r*l-a*s),t[11]=-(n*(s*d-l*h)-o*(r*d-a*h)+c*(r*l-a*s)),t[12]=-(o*(h*m-f*p)-c*(s*m-u*p)+v*(s*f-u*h)),t[13]=n*(h*m-f*p)-c*(r*m-i*p)+v*(r*f-i*h),t[14]=-(n*(s*m-u*p)-o*(r*m-i*p)+v*(r*u-i*s)),t[15]=n*(s*f-u*h)-o*(r*f-i*h)+c*(r*u-i*s),t},clone:w,copy:N,create:M,determinant:R,equals:function(t,e){var n=t[0],r=t[1],i=t[2],a=t[3],o=t[4],s=t[5],u=t[6],l=t[7],c=t[8],h=t[9],f=t[10],d=t[11],v=t[12],p=t[13],m=t[14],y=t[15],g=e[0],k=e[1],E=e[2],b=e[3],T=e[4],M=e[5],w=e[6],N=e[7],P=e[8],S=e[9],A=e[10],C=e[11],R=e[12],O=e[13],L=e[14],I=e[15];return!(Math.abs(n-g)>x*Math.max(1,Math.abs(n),Math.abs(g))||Math.abs(r-k)>x*Math.max(1,Math.abs(r),Math.abs(k))||Math.abs(i-E)>x*Math.max(1,Math.abs(i),Math.abs(E))||Math.abs(a-b)>x*Math.max(1,Math.abs(a),Math.abs(b))||Math.abs(o-T)>x*Math.max(1,Math.abs(o),Math.abs(T))||Math.abs(s-M)>x*Math.max(1,Math.abs(s),Math.abs(M))||Math.abs(u-w)>x*Math.max(1,Math.abs(u),Math.abs(w))||Math.abs(l-N)>x*Math.max(1,Math.abs(l),Math.abs(N))||Math.abs(c-P)>x*Math.max(1,Math.abs(c),Math.abs(P))||Math.abs(h-S)>x*Math.max(1,Math.abs(h),Math.abs(S))||Math.abs(f-A)>x*Math.max(1,Math.abs(f),Math.abs(A))||Math.abs(d-C)>x*Math.max(1,Math.abs(d),Math.abs(C))||Math.abs(v-R)>x*Math.max(1,Math.abs(v),Math.abs(R))||Math.abs(p-O)>x*Math.max(1,Math.abs(p),Math.abs(O))||Math.abs(m-L)>x*Math.max(1,Math.abs(m),Math.abs(L))||Math.abs(y-I)>x*Math.max(1,Math.abs(y),Math.abs(I)))},exactEquals:function(t,e){return t[0]===e[0]&&t[1]===e[1]&&t[2]===e[2]&&t[3]===e[3]&&t[4]===e[4]&&t[5]===e[5]&&t[6]===e[6]&&t[7]===e[7]&&t[8]===e[8]&&t[9]===e[9]&&t[10]===e[10]&&t[11]===e[11]&&t[12]===e[12]&&t[13]===e[13]&&t[14]===e[14]&&t[15]===e[15]},frob:function(t){return Math.hypot(t[0],t[1],t[2],t[3],t[4],t[5],t[6],t[7],t[8],t[9],t[10],t[11],t[12],t[13],t[14],t[15])},fromQuat:W,fromQuat2:function(t,e){var n=new b(3),r=-e[0],i=-e[1],a=-e[2],o=e[3],s=e[4],u=e[5],l=e[6],c=e[7],h=r*r+i*i+a*a+o*o;return h>0?(n[0]=2*(s*o+c*r+u*a-l*i)/h,n[1]=2*(u*o+c*i+l*r-s*a)/h,n[2]=2*(l*o+c*a+s*i-u*r)/h):(n[0]=2*(s*o+c*r+u*a-l*i),n[1]=2*(u*o+c*i+l*r-s*a),n[2]=2*(l*o+c*a+s*i-u*r)),U(t,e,n),t},fromRotation:G,fromRotationTranslation:U,fromRotationTranslationScale:function(t,e,n,r){var i=e[0],a=e[1],o=e[2],s=e[3],u=i+i,l=a+a,c=o+o,h=i*u,f=i*l,d=i*c,v=a*l,p=a*c,m=o*c,y=s*u,g=s*l,k=s*c,E=r[0],x=r[1],b=r[2];return t[0]=(1-(v+m))*E,t[1]=(f+k)*E,t[2]=(d-g)*E,t[3]=0,t[4]=(f-k)*x,t[5]=(1-(h+m))*x,t[6]=(p+y)*x,t[7]=0,t[8]=(d+g)*b,t[9]=(p-y)*b,t[10]=(1-(h+v))*b,t[11]=0,t[12]=n[0],t[13]=n[1],t[14]=n[2],t[15]=1,t},fromRotationTranslationScaleOrigin:H,fromScaling:F,fromTranslation:D,fromValues:function(t,e,n,r,i,a,o,s,u,l,c,h,f,d,v,p){var m=new b(16);return m[0]=t,m[1]=e,m[2]=n,m[3]=r,m[4]=i,m[5]=a,m[6]=o,m[7]=s,m[8]=u,m[9]=l,m[10]=c,m[11]=h,m[12]=f,m[13]=d,m[14]=v,m[15]=p,m},fromXRotation:B,fromYRotation:V,fromZRotation:Y,frustum:function(t,e,n,r,i,a,o){var s=1/(n-e),u=1/(i-r),l=1/(a-o);return t[0]=2*a*s,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=2*a*u,t[6]=0,t[7]=0,t[8]=(n+e)*s,t[9]=(i+r)*u,t[10]=(o+a)*l,t[11]=-1,t[12]=0,t[13]=0,t[14]=o*a*2*l,t[15]=0,t},getRotation:X,getScaling:j,getTranslation:z,identity:S,invert:C,lookAt:$,mul:tt,multiply:O,multiplyScalar:function(t,e,n){return t[0]=e[0]*n,t[1]=e[1]*n,t[2]=e[2]*n,t[3]=e[3]*n,t[4]=e[4]*n,t[5]=e[5]*n,t[6]=e[6]*n,t[7]=e[7]*n,t[8]=e[8]*n,t[9]=e[9]*n,t[10]=e[10]*n,t[11]=e[11]*n,t[12]=e[12]*n,t[13]=e[13]*n,t[14]=e[14]*n,t[15]=e[15]*n,t},multiplyScalarAndAdd:function(t,e,n,r){return t[0]=e[0]+n[0]*r,t[1]=e[1]+n[1]*r,t[2]=e[2]+n[2]*r,t[3]=e[3]+n[3]*r,t[4]=e[4]+n[4]*r,t[5]=e[5]+n[5]*r,t[6]=e[6]+n[6]*r,t[7]=e[7]+n[7]*r,t[8]=e[8]+n[8]*r,t[9]=e[9]+n[9]*r,t[10]=e[10]+n[10]*r,t[11]=e[11]+n[11]*r,t[12]=e[12]+n[12]*r,t[13]=e[13]+n[13]*r,t[14]=e[14]+n[14]*r,t[15]=e[15]+n[15]*r,t},ortho:K,orthoNO:Z,orthoZO:Q,perspective:q,perspectiveFromFieldOfView:function(t,e,n,r){var i=Math.tan(e.upDegrees*Math.PI/180),a=Math.tan(e.downDegrees*Math.PI/180),o=Math.tan(e.leftDegrees*Math.PI/180),s=Math.tan(e.rightDegrees*Math.PI/180),u=2/(o+s),l=2/(i+a);return t[0]=u,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=l,t[6]=0,t[7]=0,t[8]=-(o-s)*u*.5,t[9]=(i-a)*l*.5,t[10]=r/(n-r),t[11]=-1,t[12]=0,t[13]=0,t[14]=r*n/(n-r),t[15]=0,t},perspectiveNO:q,perspectiveZO:function(t,e,n,r,i){var a,o=1/Math.tan(e/2);return t[0]=o/n,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=o,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[11]=-1,t[12]=0,t[13]=0,t[15]=0,null!=i&&i!==1/0?(t[10]=i*(a=1/(r-i)),t[14]=i*r*a):(t[10]=-1,t[14]=-r),t},rotate:function(t,e,n,r){var i,a,o,s,u,l,c,h,f,d,v,p,m,y,g,k,E,b,T,M,w,N,P,S,A=r[0],C=r[1],R=r[2],O=Math.hypot(A,C,R);return x>O?null:(A*=O=1/O,C*=O,R*=O,i=Math.sin(n),u=e[1],l=e[2],c=e[3],f=e[5],d=e[6],v=e[7],m=e[9],y=e[10],g=e[11],T=A*C*(o=1-(a=Math.cos(n)))-R*i,M=C*C*o+a,w=R*C*o+A*i,N=A*R*o+C*i,P=C*R*o-A*i,S=R*R*o+a,t[0]=(s=e[0])*(k=A*A*o+a)+(h=e[4])*(E=C*A*o+R*i)+(p=e[8])*(b=R*A*o-C*i),t[1]=u*k+f*E+m*b,t[2]=l*k+d*E+y*b,t[3]=c*k+v*E+g*b,t[4]=s*T+h*M+p*w,t[5]=u*T+f*M+m*w,t[6]=l*T+d*M+y*w,t[7]=c*T+v*M+g*w,t[8]=s*N+h*P+p*S,t[9]=u*N+f*P+m*S,t[10]=l*N+d*P+y*S,t[11]=c*N+v*P+g*S,e!==t&&(t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15]),t)},rotateX:I,rotateY:_,rotateZ:function(t,e,n){var r=Math.sin(n),i=Math.cos(n),a=e[0],o=e[1],s=e[2],u=e[3],l=e[4],c=e[5],h=e[6],f=e[7];return e!==t&&(t[8]=e[8],t[9]=e[9],t[10]=e[10],t[11]=e[11],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15]),t[0]=a*i+l*r,t[1]=o*i+c*r,t[2]=s*i+h*r,t[3]=u*i+f*r,t[4]=l*i-a*r,t[5]=c*i-o*r,t[6]=h*i-s*r,t[7]=f*i-u*r,t},scale:function(t,e,n){var r=n[0],i=n[1],a=n[2];return t[0]=e[0]*r,t[1]=e[1]*r,t[2]=e[2]*r,t[3]=e[3]*r,t[4]=e[4]*i,t[5]=e[5]*i,t[6]=e[6]*i,t[7]=e[7]*i,t[8]=e[8]*a,t[9]=e[9]*a,t[10]=e[10]*a,t[11]=e[11]*a,t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15],t},set:P,str:function(t){return"mat4("+t[0]+", "+t[1]+", "+t[2]+", "+t[3]+", "+t[4]+", "+t[5]+", "+t[6]+", "+t[7]+", "+t[8]+", "+t[9]+", "+t[10]+", "+t[11]+", "+t[12]+", "+t[13]+", "+t[14]+", "+t[15]+")"},sub:J,subtract:J,targetTo:function(t,e,n,r){var i=e[0],a=e[1],o=e[2],s=r[0],u=r[1],l=r[2],c=i-n[0],h=a-n[1],f=o-n[2],d=c*c+h*h+f*f;d>0&&(c*=d=1/Math.sqrt(d),h*=d,f*=d);var v=u*f-l*h,p=l*c-s*f,m=s*h-u*c;return(d=v*v+p*p+m*m)>0&&(v*=d=1/Math.sqrt(d),p*=d,m*=d),t[0]=v,t[1]=p,t[2]=m,t[3]=0,t[4]=h*m-f*p,t[5]=f*v-c*m,t[6]=c*p-h*v,t[7]=0,t[8]=c,t[9]=h,t[10]=f,t[11]=0,t[12]=i,t[13]=a,t[14]=o,t[15]=1,t},translate:L,transpose:A});function nt(){var t=new b(3);return b!=Float32Array&&(t[0]=0,t[1]=0,t[2]=0),t}function rt(t){var e=new b(3);return e[0]=t[0],e[1]=t[1],e[2]=t[2],e}function it(t){return Math.hypot(t[0],t[1],t[2])}function at(t,e,n){var r=new b(3);return r[0]=t,r[1]=e,r[2]=n,r}function ot(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t}function st(t,e,n,r){return t[0]=e,t[1]=n,t[2]=r,t}function ut(t,e,n){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t[2]=e[2]+n[2],t}function lt(t,e,n){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t[2]=e[2]-n[2],t}function ct(t,e,n){return t[0]=e[0]*n,t[1]=e[1]*n,t[2]=e[2]*n,t}function ht(t,e){var n=e[0],r=e[1],i=e[2],a=n*n+r*r+i*i;return a>0&&(a=1/Math.sqrt(a)),t[0]=e[0]*a,t[1]=e[1]*a,t[2]=e[2]*a,t}function ft(t,e){return t[0]*e[0]+t[1]*e[1]+t[2]*e[2]}function dt(t,e,n){var r=e[0],i=e[1],a=e[2],o=n[0],s=n[1],u=n[2];return t[0]=i*u-a*s,t[1]=a*o-r*u,t[2]=r*s-i*o,t}function vt(t,e,n,r){var i=e[0],a=e[1],o=e[2];return t[0]=i+r*(n[0]-i),t[1]=a+r*(n[1]-a),t[2]=o+r*(n[2]-o),t}function pt(t,e,n){var r=e[0],i=e[1],a=e[2],o=n[3]*r+n[7]*i+n[11]*a+n[15];return t[0]=(n[0]*r+n[4]*i+n[8]*a+n[12])/(o=o||1),t[1]=(n[1]*r+n[5]*i+n[9]*a+n[13])/o,t[2]=(n[2]*r+n[6]*i+n[10]*a+n[14])/o,t}function mt(t,e){var n=t[0],r=t[1],i=t[2],a=e[0],o=e[1],s=e[2];return x*Math.max(1,Math.abs(n),Math.abs(a))>=Math.abs(n-a)&&x*Math.max(1,Math.abs(r),Math.abs(o))>=Math.abs(r-o)&&x*Math.max(1,Math.abs(i),Math.abs(s))>=Math.abs(i-s)}var yt=lt,gt=function(t,e){return Math.hypot(e[0]-t[0],e[1]-t[1],e[2]-t[2])},kt=it;function Et(){var t=new b(4);return b!=Float32Array&&(t[0]=0,t[1]=0,t[2]=0,t[3]=0),t}function xt(t,e,n,r){var i=new b(4);return i[0]=t,i[1]=e,i[2]=n,i[3]=r,i}function bt(t,e,n){var r=e[0],i=e[1],a=e[2],o=e[3];return t[0]=n[0]*r+n[4]*i+n[8]*a+n[12]*o,t[1]=n[1]*r+n[5]*i+n[9]*a+n[13]*o,t[2]=n[2]*r+n[6]*i+n[10]*a+n[14]*o,t[3]=n[3]*r+n[7]*i+n[11]*a+n[15]*o,t}function Tt(){var t=new b(4);return b!=Float32Array&&(t[0]=0,t[1]=0,t[2]=0),t[3]=1,t}function Mt(t,e,n){var r=Math.sin(n*=.5);return t[0]=r*e[0],t[1]=r*e[1],t[2]=r*e[2],t[3]=Math.cos(n),t}function wt(t,e,n){var r=e[0],i=e[1],a=e[2],o=e[3],s=n[0],u=n[1],l=n[2],c=n[3];return t[0]=r*c+o*s+i*l-a*u,t[1]=i*c+o*u+a*s-r*l,t[2]=a*c+o*l+r*u-i*s,t[3]=o*c-r*s-i*u-a*l,t}function Nt(t,e){var n=e[0],r=e[1],i=e[2],a=e[3],o=n*n+r*r+i*i+a*a,s=o?1/o:0;return t[0]=-n*s,t[1]=-r*s,t[2]=-i*s,t[3]=a*s,t}function Pt(t,e,n,r){var i=.5*Math.PI/180;n*=i,r*=i;var a=Math.sin(e*=i),o=Math.cos(e),s=Math.sin(n),u=Math.cos(n),l=Math.sin(r),c=Math.cos(r);return t[0]=a*u*c-o*s*l,t[1]=o*s*c+a*u*l,t[2]=o*u*l-a*s*c,t[3]=o*u*c+a*s*l,t}nt(),function(){var t=Et()}();var St=xt,At=function(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t},Ct=function(t,e,n,r,i){return t[0]=e,t[1]=n,t[2]=r,t[3]=i,t},Rt=wt,Ot=function(t,e){var n=e[0],r=e[1],i=e[2],a=e[3],o=n*n+r*r+i*i+a*a;return o>0&&(o=1/Math.sqrt(o)),t[0]=n*o,t[1]=r*o,t[2]=i*o,t[3]=a*o,t};function Lt(){var t=new b(2);return b!=Float32Array&&(t[0]=0,t[1]=0),t}function It(t){return"function"==typeof t}function _t(t){return null==t}function Dt(t){return Array.isArray(t)}nt(),at(1,0,0),at(0,1,0),Tt(),Tt(),T(),function(){var t=Lt()}();var Ft={}.toString,Gt=function(t,e){return Ft.call(t)==="[object "+e+"]"};function Bt(t){if(!Array.isArray(t))return-1/0;var e=t.length;if(!e)return-1/0;for(var n=t[0],r=1;e>r;r++)n=Math.max(n,t[r]);return n}var Vt=function(t){if(Dt(t))return t.reduce((function(t,e){return Math.min(t,e)}),t[0])};function Yt(t){return"string"==typeof t}var Ut=function(t,e,n){return e>t?e:t>n?n:t};function zt(t){return"number"==typeof t}function jt(t,e,n){return void 0===n&&(n=1e-5),t===e||n>Math.abs(t-e)}var Xt=function(t,e){return(t%e+e)%e},Ht=function(t){return Gt(t,"Boolean")},Wt=function(t){return void 0===t},qt=function(){return qt=Object.assign||function(t){for(var e,n=1,r=arguments.length;r>n;n++)for(var i in e=arguments[n])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t},qt.apply(this,arguments)};function Zt(t,e,n){if(n||2===arguments.length)for(var r,i=0,a=e.length;a>i;i++)!r&&i in e||(r||(r=Array.prototype.slice.call(e,0,i)),r[i]=e[i]);return t.concat(r||Array.prototype.slice.call(e))}"function"==typeof SuppressedError&&SuppressedError;var Kt={x1:0,y1:0,x2:0,y2:0,x:0,y:0,qx:null,qy:null};function Qt(t,e,n){if(t[n].length>7){t[n].shift();for(var r=t[n],i=n;r.length;)e[n]="A",t.splice(i+=1,0,["C"].concat(r.splice(0,6)));t.splice(n,1)}}var $t={a:7,c:6,h:1,l:2,m:2,r:4,q:4,s:4,t:2,v:1,z:0};function Jt(t){return Array.isArray(t)&&t.every((function(t){var e=t[0].toLowerCase();return $t[e]===t.length-1&&"achlmqstvz".includes(e)}))}function te(t){return Jt(t)&&t.every((function(t){var e=t[0];return e===e.toUpperCase()}))}function ee(t){return te(t)&&t.every((function(t){return"ACLMQZ".includes(t[0])}))}function ne(t){for(var e=t.pathValue[t.segmentStart],n=e.toLowerCase(),r=t.data;r.length>=$t[n]&&("m"===n&&r.length>2?(t.segments.push([e].concat(r.splice(0,2))),n="l",e="m"===e?"l":"L"):t.segments.push([e].concat(r.splice(0,$t[n]))),$t[n]););}function re(t){var e=t.index,n=t.pathValue,r=n.charCodeAt(e);return 48===r?(t.param=0,void(t.index+=1)):49===r?(t.param=1,void(t.index+=1)):void(t.err='[path-util]: invalid Arc flag "'.concat(n[e],'", expecting 0 or 1 at index ').concat(e))}function ie(t){return t>=48&&57>=t||43===t||45===t||46===t}function ae(t){return t>=48&&57>=t}function oe(t){var e,n=t.max,r=t.pathValue,i=t.index,a=i,o=!1,s=!1,u=!1,l=!1;if(n>a)if(43!==(e=r.charCodeAt(a))&&45!==e||(e=r.charCodeAt(a+=1)),ae(e)||46===e){if(46!==e){if(o=48===e,e=r.charCodeAt(a+=1),o&&n>a&&e&&ae(e))return void(t.err="[path-util]: Invalid path value at index ".concat(i,', "').concat(r[i],'" illegal number'));for(;n>a&&ae(r.charCodeAt(a));)a+=1,s=!0;e=r.charCodeAt(a)}if(46===e){for(l=!0,a+=1;ae(r.charCodeAt(a));)a+=1,u=!0;e=r.charCodeAt(a)}if(101===e||69===e){if(l&&!s&&!u)return void(t.err="[path-util]: Invalid path value at index ".concat(a,', "').concat(r[a],'" invalid float exponent'));if(43!==(e=r.charCodeAt(a+=1))&&45!==e||(a+=1),a>=n||!ae(r.charCodeAt(a)))return void(t.err="[path-util]: Invalid path value at index ".concat(a,', "').concat(r[a],'" invalid integer exponent'));for(;n>a&&ae(r.charCodeAt(a));)a+=1}t.index=a,t.param=+t.pathValue.slice(i,a)}else t.err="[path-util]: Invalid path value at index ".concat(a,', "').concat(r[a],'" is not a number');else t.err="[path-util]: Invalid path value at index ".concat(a,', "pathValue" is missing param')}function se(t){for(var e,n=t.pathValue,r=t.max;r>t.index&&(10===(e=n.charCodeAt(t.index))||13===e||8232===e||8233===e||32===e||9===e||11===e||12===e||160===e||e>=5760&&[5760,6158,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8239,8287,12288,65279].includes(e));)t.index+=1}function ue(t){var e=t.max,n=t.pathValue,r=t.index,i=n.charCodeAt(r),a=$t[n[r].toLowerCase()];if(t.segmentStart=r,function(t){switch(32|t){case 109:case 122:case 108:case 104:case 118:case 99:case 115:case 113:case 116:case 97:return!0;default:return!1}}(i))if(t.index+=1,se(t),t.data=[],a){for(;;){for(var o=a;o>0;o-=1){if(97!=(32|i)||3!==o&&4!==o?oe(t):re(t),t.err.length)return;t.data.push(t.param),se(t),e>t.index&&44===n.charCodeAt(t.index)&&(t.index+=1,se(t))}if(t.index>=t.max)break;if(!ie(n.charCodeAt(t.index)))break}ne(t)}else ne(t);else t.err='[path-util]: Invalid path value "'.concat(n[r],'" is not a path command')}var le=function(t){this.pathValue=t,this.segments=[],this.max=t.length,this.index=0,this.param=0,this.segmentStart=0,this.data=[],this.err=""};function ce(t){if(te(t))return[].concat(t);var e=function(t){if(Jt(t))return[].concat(t);var e=new le(t);for(se(e);e.max>e.index&&!e.err.length;)ue(e);return e.err?e.err:e.segments}(t),n=0,r=0,i=0,a=0;return e.map((function(t){var e,o=t.slice(1).map(Number),s=t[0],u=s.toUpperCase();if("M"===s)return i=n=o[0],a=r=o[1],["M",n,r];if(s!==u)switch(u){case"A":e=[u,o[0],o[1],o[2],o[3],o[4],o[5]+n,o[6]+r];break;case"V":e=[u,o[0]+r];break;case"H":e=[u,o[0]+n];break;default:var l=o.map((function(t,e){return t+(e%2?r:n)}));e=[u].concat(l)}else e=[u].concat(o);var c=e.length;switch(u){case"Z":n=i,r=a;break;case"H":n=e[1];break;case"V":r=e[1];break;default:n=e[c-2],r=e[c-1],"M"===u&&(i=n,a=r)}return e}))}function he(t,e){var n=t[0],r=e.x1,i=e.y1,a=e.x2,o=e.y2,s=t.slice(1).map(Number),u=t;if("TQ".includes(n)||(e.qx=null,e.qy=null),"H"===n)u=["L",t[1],i];else if("V"===n)u=["L",r,t[1]];else if("S"===n){var l=2*r-a,c=2*i-o;e.x1=l,e.y1=c,u=["C",l,c].concat(s)}else if("T"===n){var h=2*r-e.qx,f=2*i-e.qy;e.qx=h,e.qy=f,u=["Q",h,f].concat(s)}else if("Q"===n){var d=s[1];e.qx=s[0],e.qy=d}return u}function fe(t){if(ee(t))return[].concat(t);for(var e=ce(t),n=qt({},Kt),r=0;e.length>r;r+=1){e[r]=he(e[r],n);var i=e[r],a=i.length;n.x1=+i[a-2],n.y1=+i[a-1],n.x2=+i[a-4]||n.x1,n.y2=+i[a-3]||n.y1}return e}function de(t,e,n){return{x:t*Math.cos(n)-e*Math.sin(n),y:t*Math.sin(n)+e*Math.cos(n)}}function ve(t,e,n,r,i,a,o,s,u,l){var c,h,f,d,v,p=t,m=e,y=n,g=r,k=s,E=u,x=120*Math.PI/180,b=Math.PI/180*(+i||0),T=[];if(l)h=l[0],f=l[1],d=l[2],v=l[3];else{m=(c=de(p,m,-b)).y;var M=((p=c.x)-(k=(c=de(k,E,-b)).x))/2,w=(m-(E=c.y))/2,N=M*M/(y*y)+w*w/(g*g);N>1&&(y*=N=Math.sqrt(N),g*=N);var P=y*y,S=g*g,A=(a===o?-1:1)*Math.sqrt(Math.abs((P*S-P*w*w-S*M*M)/(P*w*w+S*M*M)));d=A*y*w/g+(p+k)/2,h=Math.asin(((m-(v=A*-g*M/y+(m+E)/2))/g*1e9|0)/1e9),f=Math.asin(((E-v)/g*1e9|0)/1e9),0>(h=d>p?Math.PI-h:h)&&(h=2*Math.PI+h),0>(f=d>k?Math.PI-f:f)&&(f=2*Math.PI+f),o&&h>f&&(h-=2*Math.PI),!o&&f>h&&(f-=2*Math.PI)}var C=f-h;if(Math.abs(C)>x){var R=f,O=k,L=E;T=ve(k=d+y*Math.cos(f=h+x*(o&&f>h?1:-1)),E=v+g*Math.sin(f),y,g,i,0,o,O,L,[f,R,d,v])}C=f-h;var I=Math.cos(h),_=Math.sin(h),D=Math.cos(f),F=Math.sin(f),G=Math.tan(C/4),B=4/3*y*G,V=4/3*g*G,Y=[p,m],U=[p+B*_,m-V*I],z=[k+B*F,E-V*D],j=[k,E];if(U[0]=2*Y[0]-U[0],U[1]=2*Y[1]-U[1],l)return U.concat(z,j,T);for(var X=[],H=0,W=(T=U.concat(z,j,T)).length;W>H;H+=1)X[H]=H%2?de(T[H-1],T[H],b).y:de(T[H],T[H+1],b).x;return X}function pe(t,e,n){var r=t[0],i=t[1];return[r+(e[0]-r)*n,i+(e[1]-i)*n]}var me=function(t,e,n,r){return Zt(Zt([],pe([t,e],[n,r],.5),!0),[n,r,n,r],!1)};function ye(t,e){var n,r,i,a,o,s,u,l=t[0],c=t.slice(1).map(Number),h=c[0],f=c[1],d=e.x1,v=e.y1,p=e.x,m=e.y;switch("TQ".includes(l)||(e.qx=null,e.qy=null),l){case"M":return e.x=h,e.y=f,t;case"A":return n=[d,v].concat(c),["C"].concat(ve(n[0],n[1],n[2],n[3],n[4],n[5],n[6],n[7],n[8],n[9]));case"Q":return e.qx=h,e.qy=f,n=[d,v].concat(c),["C"].concat([(s=1/3)*n[0]+(u=2/3)*(r=n[2]),s*n[1]+u*(i=n[3]),s*(a=n[4])+u*r,s*(o=n[5])+u*i,a,o]);case"L":return["C"].concat(me(d,v,h,f));case"Z":return d===p&&v===m?["C",d,v,p,m,p,m]:["C"].concat(me(d,v,p,m))}return t}function ge(t,e){if(void 0===e&&(e=!1),function(t){return ee(t)&&t.every((function(t){return"MC".includes(t[0])}))}(t)){var n=[].concat(t);return e?[n,[]]:n}for(var r,i,a=fe(t),o=qt({},Kt),s=[],u="",l=a.length,c=[],h=0;l>h;h+=1){a[h]&&(u=a[h][0]),s[h]=u;var f=ye(a[h],o);a[h]=f,Qt(a,s,h),l=a.length,"Z"===u&&c.push(h),o.x1=+(r=a[h])[(i=r.length)-2],o.y1=+r[i-1],o.x2=+r[i-4]||o.x1,o.y2=+r[i-3]||o.y1}return e?[a,c]:a}function ke(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}function Ee(t,e,n,r,i){var a=ke([t,e],[n,r]),o={x:0,y:0};if("number"==typeof i)if(0<i)if(i<a){var s=pe([t,e],[n,r],i/a);o={x:s[0],y:s[1]}}else o={x:n,y:r};else o={x:t,y:e};return{length:a,point:o,min:{x:Math.min(t,n),y:Math.min(e,r)},max:{x:Math.max(t,n),y:Math.max(e,r)}}}function xe(t,e){var n=t.x,r=t.y,i=e.x,a=e.y;return(0>n*a-r*i?-1:1)*Math.acos((n*i+r*a)/Math.sqrt((Math.pow(n,2)+Math.pow(r,2))*(Math.pow(i,2)+Math.pow(a,2))))}function be(t,e,n,r,i,a,o,s,u,l){var c=Math.abs,h=Math.sin,f=Math.cos,d=Math.sqrt,v=Math.PI,p=c(n),m=c(r),y=(i%360+360)%360*(v/180);if(t===s&&e===u)return{x:t,y:e};if(0===p||0===m)return Ee(t,e,s,u,l).point;var g=(t-s)/2,k=(e-u)/2,E={x:f(y)*g+h(y)*k,y:-h(y)*g+f(y)*k},x=Math.pow(E.x,2)/Math.pow(p,2)+Math.pow(E.y,2)/Math.pow(m,2);x>1&&(p*=d(x),m*=d(x));var b=(Math.pow(p,2)*Math.pow(m,2)-Math.pow(p,2)*Math.pow(E.y,2)-Math.pow(m,2)*Math.pow(E.x,2))/(Math.pow(p,2)*Math.pow(E.y,2)+Math.pow(m,2)*Math.pow(E.x,2)),T=(a!==o?1:-1)*d(b=0>b?0:b),M=T*(p*E.y/m),w=T*(-m*E.x/p),N=f(y)*M-h(y)*w+(t+s)/2,P=h(y)*M+f(y)*w+(e+u)/2,S={x:(E.x-M)/p,y:(E.y-w)/m},A=xe({x:1,y:0},S),C=xe(S,{x:(-E.x-M)/p,y:(-E.y-w)/m});!o&&C>0?C-=2*v:o&&0>C&&(C+=2*v);var R=A+(C%=2*v)*l,O=p*f(R),L=m*h(R);return{x:f(y)*O-h(y)*L+N,y:h(y)*O+f(y)*L+P}}function Te(t,e,n,r,i,a,o,s,u,l,c){var h,f=c.bbox,d=void 0===f||f,v=c.length,p=void 0===v||v,m=c.sampleSize,y=void 0===m?30:m,g="number"==typeof l,k=t,E=e,x=0,b=[k,E,x],T=[k,E],M={x:0,y:0},w=[{x:k,y:E}];g&&0>=l&&(M={x:k,y:E});for(var N=0;y>=N;N+=1){if(k=(h=be(t,e,n,r,i,a,o,s,u,N/y)).x,E=h.y,d&&w.push({x:k,y:E}),p&&(x+=ke(T,[k,E])),T=[k,E],g&&x>=l&&l>b[2]){var P=(x-l)/(x-b[2]);M={x:T[0]*(1-P)+b[0]*P,y:T[1]*(1-P)+b[1]*P}}b=[k,E,x]}return g&&l>=x&&(M={x:s,y:u}),{length:x,point:M,min:{x:Math.min.apply(null,w.map((function(t){return t.x}))),y:Math.min.apply(null,w.map((function(t){return t.y})))},max:{x:Math.max.apply(null,w.map((function(t){return t.x}))),y:Math.max.apply(null,w.map((function(t){return t.y})))}}}function Me(t,e,n,r,i,a,o,s,u){var l=1-u;return{x:Math.pow(l,3)*t+3*Math.pow(l,2)*u*n+3*l*Math.pow(u,2)*i+Math.pow(u,3)*o,y:Math.pow(l,3)*e+3*Math.pow(l,2)*u*r+3*l*Math.pow(u,2)*a+Math.pow(u,3)*s}}function we(t,e,n,r,i,a,o,s,u,l){var c,h=l.bbox,f=void 0===h||h,d=l.length,v=void 0===d||d,p=l.sampleSize,m=void 0===p?10:p,y="number"==typeof u,g=t,k=e,E=0,x=[g,k,E],b=[g,k],T={x:0,y:0},M=[{x:g,y:k}];y&&0>=u&&(T={x:g,y:k});for(var w=0;m>=w;w+=1){if(g=(c=Me(t,e,n,r,i,a,o,s,w/m)).x,k=c.y,f&&M.push({x:g,y:k}),v&&(E+=ke(b,[g,k])),b=[g,k],y&&E>=u&&u>x[2]){var N=(E-u)/(E-x[2]);T={x:b[0]*(1-N)+x[0]*N,y:b[1]*(1-N)+x[1]*N}}x=[g,k,E]}return y&&u>=E&&(T={x:o,y:s}),{length:E,point:T,min:{x:Math.min.apply(null,M.map((function(t){return t.x}))),y:Math.min.apply(null,M.map((function(t){return t.y})))},max:{x:Math.max.apply(null,M.map((function(t){return t.x}))),y:Math.max.apply(null,M.map((function(t){return t.y})))}}}function Ne(t,e,n,r,i,a,o){var s=1-o;return{x:Math.pow(s,2)*t+2*s*o*n+Math.pow(o,2)*i,y:Math.pow(s,2)*e+2*s*o*r+Math.pow(o,2)*a}}function Pe(t,e,n,r,i,a,o,s){var u,l=s.bbox,c=void 0===l||l,h=s.length,f=void 0===h||h,d=s.sampleSize,v=void 0===d?10:d,p="number"==typeof o,m=t,y=e,g=0,k=[m,y,g],E=[m,y],x={x:0,y:0},b=[{x:m,y:y}];p&&0>=o&&(x={x:m,y:y});for(var T=0;v>=T;T+=1){if(m=(u=Ne(t,e,n,r,i,a,T/v)).x,y=u.y,c&&b.push({x:m,y:y}),f&&(g+=ke(E,[m,y])),E=[m,y],p&&g>=o&&o>k[2]){var M=(g-o)/(g-k[2]);x={x:E[0]*(1-M)+k[0]*M,y:E[1]*(1-M)+k[1]*M}}k=[m,y,g]}return p&&o>=g&&(x={x:i,y:a}),{length:g,point:x,min:{x:Math.min.apply(null,b.map((function(t){return t.x}))),y:Math.min.apply(null,b.map((function(t){return t.y})))},max:{x:Math.max.apply(null,b.map((function(t){return t.x}))),y:Math.max.apply(null,b.map((function(t){return t.y})))}}}function Se(t,e,n){for(var r,i,a,o,s,u,l,c,h,f=fe(t),d="number"==typeof e,v=[],p=0,m=0,y=0,g=0,k=[],E=[],x=0,b={x:0,y:0},T=b,M=b,w=b,N=0,P=0,S=f.length;S>P;P+=1)v=(l="M"===(c=(h=f[P])[0]))?v:[p,m].concat(h.slice(1)),l?(T=b={x:y=h[1],y:g=h[2]},x=0,d&&.001>e&&(w=b)):"L"===c?(x=(r=Ee(v[0],v[1],v[2],v[3],(e||0)-N)).length,b=r.min,T=r.max,M=r.point):"A"===c?(x=(i=Te(v[0],v[1],v[2],v[3],v[4],v[5],v[6],v[7],v[8],(e||0)-N,n||{})).length,b=i.min,T=i.max,M=i.point):"C"===c?(x=(a=we(v[0],v[1],v[2],v[3],v[4],v[5],v[6],v[7],(e||0)-N,n||{})).length,b=a.min,T=a.max,M=a.point):"Q"===c?(x=(o=Pe(v[0],v[1],v[2],v[3],v[4],v[5],(e||0)-N,n||{})).length,b=o.min,T=o.max,M=o.point):"Z"===c&&(x=(s=Ee((v=[p,m,y,g])[0],v[1],v[2],v[3],(e||0)-N)).length,b=s.min,T=s.max,M=s.point),d&&e>N&&N+x>=e&&(w=M),E.push(T),k.push(b),N+=x,p=(u="Z"!==c?h.slice(-2):[y,g])[0],m=u[1];return d&&e>=N&&(w={x:p,y:m}),{length:N,point:w,min:{x:Math.min.apply(null,k.map((function(t){return t.x}))),y:Math.min.apply(null,k.map((function(t){return t.y})))},max:{x:Math.max.apply(null,E.map((function(t){return t.x}))),y:Math.max.apply(null,E.map((function(t){return t.y})))}}}function Ae(t){return function(t){var e=0,n=0,r=0;return ge(t).map((function(t){var i;if("M"===t[0])return e=t[1],n=t[2],0;var a,o,s,u,l,c,h,f,d=t.slice(1);return r=3*(((f=d[5])-(o=n))*((s=d[0])+(l=d[2]))-((h=d[4])-(a=e))*((u=d[1])+(c=d[3]))+u*(a-l)-s*(o-c)+f*(l+a/3)-h*(c+o/3))/20,i=t.slice(-2),e=i[0],n=i[1],r})).reduce((function(t,e){return t+e}),0)}(t)>=0}function Ce(t){return t.map((function(t,e,n){var r=e&&n[e-1].slice(-2).concat(t.slice(1)),i=e?we(r[0],r[1],r[2],r[3],r[4],r[5],r[6],r[7],r[8],{bbox:!1}).length:0;return{s:t,ss:e?i?function(t,e){void 0===e&&(e=.5);var n=t.slice(0,2),r=t.slice(2,4),i=t.slice(4,6),a=t.slice(6,8),o=pe(n,r,e),s=pe(r,i,e),u=pe(i,a,e),l=pe(o,s,e),c=pe(s,u,e),h=pe(l,c,e);return[["C"].concat(o,l,h),["C"].concat(c,u,a)]}(r):[t,t]:[t],l:i}}))}function Re(t,e,n){var r=Ce(t),i=Ce(e),a=r.length,o=i.length,s=r.filter((function(t){return t.l})).length,u=i.filter((function(t){return t.l})).length,l=r.filter((function(t){return t.l})).reduce((function(t,e){return t+e.l}),0)/s||0,c=i.filter((function(t){return t.l})).reduce((function(t,e){return t+e.l}),0)/u||0,h=n||Math.max(a,o),f=[l,c],d=[h-a,h-o],v=0,p=[r,i].map((function(t,e){return t.l===h?t.map((function(t){return t.s})):t.map((function(t,n){return d[e]-=(v=n&&d[e]&&t.l>=f[e])?1:0,v?t.ss:[t.s]})).flat()}));return p[0].length===p[1].length?p:Re(p[0],p[1],h)}function Oe(){return Oe="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,n){var r=function(t,e){for(;!{}.hasOwnProperty.call(t,e)&&null!==(t=f(t)););return t}(t,e);if(r){var i=Object.getOwnPropertyDescriptor(r,e);return i.get?i.get.call(3>arguments.length?t:n):i.value}},Oe.apply(null,arguments)}function Le(t,e,n,r){var i=Oe(f(1&r?t.prototype:t),e,n);return 2&r&&"function"==typeof i?function(t){return i.apply(n,t)}:i}
/*!
   * @antv/g-math
   * @description Geometry util
   * @version 3.0.1
   * @date 5/23/2025, 6:59:22 AM
   * <AUTHOR>
   * @docs https://g.antv.antgroup.com/
   */function Ie(t,e,n,r){var i=t-n,a=e-r;return Math.sqrt(i*i+a*a)}function _e(t,e){var n=Math.min.apply(Math,h(t)),r=Math.min.apply(Math,h(e));return{x:n,y:r,width:Math.max.apply(Math,h(t))-n,height:Math.max.apply(Math,h(e))-r}}function De(t,e,n,r,i,a){return n*Math.cos(i)*Math.cos(a)-r*Math.sin(i)*Math.sin(a)+t}function Fe(t,e,n,r,i,a){return n*Math.sin(i)*Math.cos(a)+r*Math.cos(i)*Math.sin(a)+e}function Ge(t,e,n,r,i,a,o){for(var s=function(t,e,n){return Math.atan(-e/t*Math.tan(n))}(n,r,i),u=1/0,l=-1/0,c=[a,o],h=2*-Math.PI;2*Math.PI>=h;h+=Math.PI){var f=s+h;o>a?f>a&&o>f&&c.push(f):f>o&&a>f&&c.push(f)}for(var d=0;c.length>d;d++){var v=De(t,0,n,r,i,c[d]);u>v&&(u=v),v>l&&(l=v)}for(var p=function(t,e,n){return Math.atan(e/(t*Math.tan(n)))}(n,r,i),m=1/0,y=-1/0,g=[a,o],k=2*-Math.PI;2*Math.PI>=k;k+=Math.PI){var E=p+k;o>a?E>a&&o>E&&g.push(E):E>o&&a>E&&g.push(E)}for(var x=0;g.length>x;x++){var b=Fe(0,e,n,r,i,g[x]);m>b&&(m=b),b>y&&(y=b)}return{x:u,y:m,width:l-u,height:y-m}}function Be(t,e,n,r){return Ie(t,e,n,r)}function Ve(t,e,n,r,i){return{x:(1-i)*t+i*n,y:(1-i)*e+i*r}}function Ye(t,e,n,r,i){var a=1-i;return a*a*a*t+3*e*i*a*a+3*n*i*i*a+r*i*i*i}function Ue(t,e,n,r){var i,a,o,s=-3*t+9*e-9*n+3*r,u=6*t-12*e+6*n,l=3*e-3*t,c=[];if(jt(s,0))jt(u,0)||0>(i=-l/u)||i>1||c.push(i);else{var h=u*u-4*s*l;jt(h,0)?c.push(-u/(2*s)):h>0&&(a=(-u-(o=Math.sqrt(h)))/(2*s),0>(i=(-u+o)/(2*s))||i>1||c.push(i),0>a||a>1||c.push(a))}return c}function ze(t,e,n,r,i,a,o,s){for(var u=[t,o],l=[e,s],c=Ue(t,n,i,o),h=Ue(e,r,a,s),f=0;c.length>f;f++)u.push(Ye(t,n,i,o,c[f]));for(var d=0;h.length>d;d++)l.push(Ye(e,r,a,s,h[d]));return _e(u,l)}function je(t){return function(t){if(2>t.length)return 0;for(var e=0,n=0;t.length-1>n;n++){var r=t[n],i=t[n+1];e+=Ie(r[0],r[1],i[0],i[1])}return e}(t)}function Xe(t,e,n,r){var i=1-r;return i*i*t+2*r*i*e+r*r*n}function He(t,e,n){var r=t+n-2*e;if(jt(r,0))return[.5];var i=(t-e)/r;return i>1||0>i?[]:[i]}function We(t,e,n,r,i,a){var o=He(t,n,i)[0],s=He(e,r,a)[0],u=[t,i],l=[e,a];return void 0!==o&&u.push(Xe(t,n,i,o)),void 0!==s&&l.push(Xe(e,r,a,s)),_e(u,l)}function qe(){qe=function(){return n};var t,n={},r=Object.prototype,i=r.hasOwnProperty,a="function"==typeof Symbol?Symbol:{},o=a.iterator||"@@iterator",s=a.asyncIterator||"@@asyncIterator",u=a.toStringTag||"@@toStringTag";function l(t,e,n,r){return Object.defineProperty(t,e,{value:n,enumerable:!r,configurable:!r,writable:!r})}try{l({},"")}catch(t){l=function(t,e,n){return t[e]=n}}function c(e,n,r,i){var a=Object.create((n&&n.prototype instanceof d?n:d).prototype);return l(a,"_invoke",function(e,n,r){var i=1;return function(a,o){if(3===i)throw Error("Generator is already running");if(4===i){if("throw"===a)throw o;return{value:t,done:!0}}for(r.method=a,r.arg=o;;){var s=r.delegate;if(s){var u=b(s,r);if(u){if(u===f)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(1===i)throw i=4,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=3;var l=h(e,n,r);if("normal"===l.type){if(i=r.done?4:2,l.arg===f)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(i=4,r.method="throw",r.arg=l.arg)}}}(e,r,new w(i||[])),!0),a}function h(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}n.wrap=c;var f={};function d(){}function v(){}function p(){}var m={};l(m,o,(function(){return this}));var y=Object.getPrototypeOf,g=y&&y(y(N([])));g&&g!==r&&i.call(g,o)&&(m=g);var k=p.prototype=d.prototype=Object.create(m);function E(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function x(t,n){function r(a,o,s,u){var l=h(t[a],t,o);if("throw"!==l.type){var c=l.arg,f=c.value;return f&&"object"==e(f)&&i.call(f,"__await")?n.resolve(f.__await).then((function(t){r("next",t,s,u)}),(function(t){r("throw",t,s,u)})):n.resolve(f).then((function(t){c.value=t,s(c)}),(function(t){return r("throw",t,s,u)}))}u(l.arg)}var a;l(this,"_invoke",(function(t,e){function i(){return new n((function(n,i){r(t,e,n,i)}))}return a=a?a.then(i,i):i()}),!0)}function b(e,n){var r=n.method,i=e.i[r];if(i===t)return n.delegate=null,"throw"===r&&e.i.return&&(n.method="return",n.arg=t,b(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),f;var a=h(i,e.i,n.arg);if("throw"===a.type)return n.method="throw",n.arg=a.arg,n.delegate=null,f;var o=a.arg;return o?o.done?(n[e.r]=o.value,n.next=e.n,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,f):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,f)}function T(t){this.tryEntries.push(t)}function M(e){var n=e[4]||{};n.type="normal",n.arg=t,e[4]=n}function w(t){this.tryEntries=[[-1]],t.forEach(T,this),this.reset(!0)}function N(n){if(null!=n){var r=n[o];if(r)return r.call(n);if("function"==typeof n.next)return n;if(!isNaN(n.length)){var a=-1,s=function e(){for(;++a<n.length;)if(i.call(n,a))return e.value=n[a],e.done=!1,e;return e.value=t,e.done=!0,e};return s.next=s}}throw new TypeError(e(n)+" is not iterable")}return v.prototype=p,l(k,"constructor",p),l(p,"constructor",v),v.displayName=l(p,u,"GeneratorFunction"),n.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===v||"GeneratorFunction"===(e.displayName||e.name))},n.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,p):(t.__proto__=p,l(t,u,"GeneratorFunction")),t.prototype=Object.create(k),t},n.awrap=function(t){return{__await:t}},E(x.prototype),l(x.prototype,s,(function(){return this})),n.AsyncIterator=x,n.async=function(t,e,r,i,a){void 0===a&&(a=Promise);var o=new x(c(t,e,r,i),a);return n.isGeneratorFunction(e)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},E(k),l(k,u,"Generator"),l(k,o,(function(){return this})),l(k,"toString",(function(){return"[object Generator]"})),n.keys=function(t){var e=Object(t),n=[];for(var r in e)n.unshift(r);return function t(){for(;n.length;)if((r=n.pop())in e)return t.value=r,t.done=!1,t;return t.done=!0,t}},n.values=N,w.prototype={constructor:w,reset:function(e){if(this.prev=this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(M),!e)for(var n in this)"t"===n.charAt(0)&&i.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0][4];if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function r(t){o.type="throw",o.arg=e,n.next=t}for(var i=n.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],o=a[4],s=this.prev,u=a[1],l=a[2];if(-1===a[0])return r("end"),!1;if(!u&&!l)throw Error("try statement without catch or finally");if(null!=a[0]&&s>=a[0]){if(u>s)return this.method="next",this.arg=t,r(u),!0;if(l>s)return r(l),!1}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r[0]>-1&&this.prev>=r[0]&&r[2]>this.prev){var i=r;break}}i&&("break"===t||"continue"===t)&&e>=i[0]&&i[2]>=e&&(i=null);var a=i?i[4]:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i[2],f):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),f},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n[2]===t)return this.complete(n[4],n[3]),M(n),f}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n[0]===t){var r=n[4];if("throw"===r.type){var i=r.arg;M(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={i:N(e),r:n,n:r},"next"===this.method&&(this.arg=t),f}},n}function Ze(t,e,n,r,i,a,o){try{var s=t[a](o),u=s.value}catch(t){return void n(t)}s.done?e(u):Promise.resolve(u).then(r,i)}function Ke(t){return function(){var e=this,n=arguments;return new Promise((function(r,i){var a=t.apply(e,n);function o(t){Ze(a,r,i,o,s,"next",t)}function s(t){Ze(a,r,i,o,s,"throw",t)}o(void 0)}))}}function Qe(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=c(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return t.length>r?{done:!1,value:t[r++]}:{done:!0}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return o=t.done,t},e:function(t){s=!0,a=t},f:function(){try{o||null==n.return||n.return()}finally{if(s)throw a}}}}function $e(t,e){if(null==t)return{};var n,r,i=function(t,e){if(null==t)return{};var n={};for(var r in t)if({}.hasOwnProperty.call(t,r)){if(-1!==e.indexOf(r))continue;n[r]=t[r]}return n}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(r=0;a.length>r;r++)-1===e.indexOf(n=a[r])&&{}.propertyIsEnumerable.call(t,n)&&(i[n]=t[n])}return i}"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self&&self;var Je={exports:{}};!function(t){t.exports=function(){function t(t,n,i,a,o){e(t,n,i||0,a||t.length-1,o||r)}function e(t,r,i,a,o){for(;a>i;){if(a-i>600){var s=a-i+1,u=r-i+1,l=Math.log(s),c=.5*Math.exp(2*l/3),h=.5*Math.sqrt(l*c*(s-c)/s)*(0>u-s/2?-1:1);e(t,r,Math.max(i,Math.floor(r-u*c/s+h)),Math.min(a,Math.floor(r+(s-u)*c/s+h)),o)}var f=t[r],d=i,v=a;for(n(t,i,r),o(t[a],f)>0&&n(t,i,a);v>d;){for(n(t,d,v),d++,v--;0>o(t[d],f);)d++;for(;o(t[v],f)>0;)v--}0===o(t[i],f)?n(t,i,v):n(t,++v,a),v>r||(i=v+1),r>v||(a=v-1)}}function n(t,e,n){var r=t[e];t[e]=t[n],t[n]=r}function r(t,e){return e>t?-1:t>e?1:0}var i=function(t){void 0===t&&(t=9),this._maxEntries=Math.max(4,t),this._minEntries=Math.max(2,Math.ceil(.4*this._maxEntries)),this.clear()};function a(t,e,n){if(!n)return e.indexOf(t);for(var r=0;e.length>r;r++)if(n(t,e[r]))return r;return-1}function o(t,e){s(t,0,t.children.length,e,t)}function s(t,e,n,r,i){i||(i=y(null)),i.minX=1/0,i.minY=1/0,i.maxX=-1/0,i.maxY=-1/0;for(var a=e;n>a;a++){var o=t.children[a];u(i,t.leaf?r(o):o)}return i}function u(t,e){return t.minX=Math.min(t.minX,e.minX),t.minY=Math.min(t.minY,e.minY),t.maxX=Math.max(t.maxX,e.maxX),t.maxY=Math.max(t.maxY,e.maxY),t}function l(t,e){return t.minX-e.minX}function c(t,e){return t.minY-e.minY}function h(t){return(t.maxX-t.minX)*(t.maxY-t.minY)}function f(t){return t.maxX-t.minX+(t.maxY-t.minY)}function d(t,e){return(Math.max(e.maxX,t.maxX)-Math.min(e.minX,t.minX))*(Math.max(e.maxY,t.maxY)-Math.min(e.minY,t.minY))}function v(t,e){var n=Math.max(t.minX,e.minX),r=Math.max(t.minY,e.minY),i=Math.min(t.maxX,e.maxX),a=Math.min(t.maxY,e.maxY);return Math.max(0,i-n)*Math.max(0,a-r)}function p(t,e){return!(t.minX>e.minX||t.minY>e.minY||e.maxX>t.maxX||e.maxY>t.maxY)}function m(t,e){return!(e.minX>t.maxX||e.minY>t.maxY||t.minX>e.maxX||t.minY>e.maxY)}function y(t){return{children:t,height:1,leaf:!0,minX:1/0,minY:1/0,maxX:-1/0,maxY:-1/0}}function g(e,n,r,i,a){for(var o=[n,r];o.length;)if((r=o.pop())-(n=o.pop())>i){var s=n+Math.ceil((r-n)/i/2)*i;t(e,s,n,r,a),o.push(n,s,s,r)}}return i.prototype.all=function(){return this._all(this.data,[])},i.prototype.search=function(t){var e=this.data,n=[];if(!m(t,e))return n;for(var r=this.toBBox,i=[];e;){for(var a=0;e.children.length>a;a++){var o=e.children[a],s=e.leaf?r(o):o;m(t,s)&&(e.leaf?n.push(o):p(t,s)?this._all(o,n):i.push(o))}e=i.pop()}return n},i.prototype.collides=function(t){var e=this.data;if(!m(t,e))return!1;for(var n=[];e;){for(var r=0;e.children.length>r;r++){var i=e.children[r],a=e.leaf?this.toBBox(i):i;if(m(t,a)){if(e.leaf||p(t,a))return!0;n.push(i)}}e=n.pop()}return!1},i.prototype.load=function(t){if(!t||!t.length)return this;if(this._minEntries>t.length){for(var e=0;t.length>e;e++)this.insert(t[e]);return this}var n=this._build(t.slice(),0,t.length-1,0);if(this.data.children.length)if(this.data.height===n.height)this._splitRoot(this.data,n);else{if(n.height>this.data.height){var r=this.data;this.data=n,n=r}this._insert(n,this.data.height-n.height-1,!0)}else this.data=n;return this},i.prototype.insert=function(t){return t&&this._insert(t,this.data.height-1),this},i.prototype.clear=function(){return this.data=y([]),this},i.prototype.remove=function(t,e){if(!t)return this;for(var n,r,i,o=this.data,s=this.toBBox(t),u=[],l=[];o||u.length;){if(o||(o=u.pop(),r=u[u.length-1],n=l.pop(),i=!0),o.leaf){var c=a(t,o.children,e);if(-1!==c)return o.children.splice(c,1),u.push(o),this._condense(u),this}i||o.leaf||!p(o,s)?r?(n++,o=r.children[n],i=!1):o=null:(u.push(o),l.push(n),n=0,r=o,o=o.children[0])}return this},i.prototype.toBBox=function(t){return t},i.prototype.compareMinX=function(t,e){return t.minX-e.minX},i.prototype.compareMinY=function(t,e){return t.minY-e.minY},i.prototype.toJSON=function(){return this.data},i.prototype.fromJSON=function(t){return this.data=t,this},i.prototype._all=function(t,e){for(var n=[];t;)t.leaf?e.push.apply(e,t.children):n.push.apply(n,t.children),t=n.pop();return e},i.prototype._build=function(t,e,n,r){var i,a=n-e+1,s=this._maxEntries;if(s>=a)return o(i=y(t.slice(e,n+1)),this.toBBox),i;r||(s=Math.ceil(a/Math.pow(s,(r=Math.ceil(Math.log(a)/Math.log(s)))-1))),(i=y([])).leaf=!1,i.height=r;var u=Math.ceil(a/s),l=u*Math.ceil(Math.sqrt(s));g(t,e,n,l,this.compareMinX);for(var c=e;n>=c;c+=l){var h=Math.min(c+l-1,n);g(t,c,h,u,this.compareMinY);for(var f=c;h>=f;f+=u)i.children.push(this._build(t,f,Math.min(f+u-1,h),r-1))}return o(i,this.toBBox),i},i.prototype._chooseSubtree=function(t,e,n,r){for(;r.push(e),!e.leaf&&r.length-1!==n;){for(var i=1/0,a=1/0,o=void 0,s=0;e.children.length>s;s++){var u=e.children[s],l=h(u),c=d(t,u)-l;a>c?(a=c,i=i>l?l:i,o=u):c===a&&i>l&&(i=l,o=u)}e=o||e.children[0]}return e},i.prototype._insert=function(t,e,n){var r=n?t:this.toBBox(t),i=[],a=this._chooseSubtree(r,this.data,e,i);for(a.children.push(t),u(a,r);e>=0&&i[e].children.length>this._maxEntries;)this._split(i,e),e--;this._adjustParentBBoxes(r,i,e)},i.prototype._split=function(t,e){var n=t[e],r=n.children.length,i=this._minEntries;this._chooseSplitAxis(n,i,r);var a=this._chooseSplitIndex(n,i,r),s=y(n.children.splice(a,n.children.length-a));s.height=n.height,s.leaf=n.leaf,o(n,this.toBBox),o(s,this.toBBox),e?t[e-1].children.push(s):this._splitRoot(n,s)},i.prototype._splitRoot=function(t,e){this.data=y([t,e]),this.data.height=t.height+1,this.data.leaf=!1,o(this.data,this.toBBox)},i.prototype._chooseSplitIndex=function(t,e,n){for(var r,i=1/0,a=1/0,o=e;n-e>=o;o++){var u=s(t,0,o,this.toBBox),l=s(t,o,n,this.toBBox),c=v(u,l),f=h(u)+h(l);i>c?(i=c,r=o,a=a>f?f:a):c===i&&a>f&&(a=f,r=o)}return r||n-e},i.prototype._chooseSplitAxis=function(t,e,n){var r=t.leaf?this.compareMinX:l,i=t.leaf?this.compareMinY:c,a=this._allDistMargin(t,e,n,r);this._allDistMargin(t,e,n,i)>a&&t.children.sort(r)},i.prototype._allDistMargin=function(t,e,n,r){t.children.sort(r);for(var i=this.toBBox,a=s(t,0,e,i),o=s(t,n-e,n,i),l=f(a)+f(o),c=e;n-e>c;c++){var h=t.children[c];u(a,t.leaf?i(h):h),l+=f(a)}for(var d=n-e-1;d>=e;d--){var v=t.children[d];u(o,t.leaf?i(v):v),l+=f(o)}return l},i.prototype._adjustParentBBoxes=function(t,e,n){for(var r=n;r>=0;r--)u(e[r],t)},i.prototype._condense=function(t){for(var e=t.length-1,n=void 0;e>=0;e--)0===t[e].children.length?e>0?(n=t[e-1].children).splice(n.indexOf(t[e]),1):this.clear():o(t[e],this.toBBox)},i}()}(Je);var tn=Je.exports,en=function(t){return t.GROUP="g",t.FRAGMENT="fragment",t.CIRCLE="circle",t.ELLIPSE="ellipse",t.IMAGE="image",t.RECT="rect",t.LINE="line",t.POLYLINE="polyline",t.POLYGON="polygon",t.TEXT="text",t.PATH="path",t.HTML="html",t.MESH="mesh",t}({}),nn=function(t){return t[t.ZERO=0]="ZERO",t[t.NEGATIVE_ONE=1]="NEGATIVE_ONE",t}({}),rn=function(){return u((function t(){o(this,t),this.plugins=[]}),[{key:"addRenderingPlugin",value:function(t){this.plugins.push(t),this.context.renderingPlugins.push(t)}},{key:"removeAllRenderingPlugins",value:function(){var t=this;this.plugins.forEach((function(e){var n=t.context.renderingPlugins.indexOf(e);0>n||t.context.renderingPlugins.splice(n,1)}))}}])}(),an=function(){return u((function t(e){o(this,t),this.clipSpaceNearZ=nn.NEGATIVE_ONE,this.plugins=[],this.config=a({enableDirtyCheck:!0,enableCulling:!1,enableAutoRendering:!0,enableDirtyRectangleRendering:!0,enableDirtyRectangleRenderingDebug:!1,enableSizeAttenuation:!0,enableRenderingOptimization:!1},e)}),[{key:"registerPlugin",value:function(t){-1===this.plugins.findIndex((function(e){return e===t}))&&this.plugins.push(t)}},{key:"unregisterPlugin",value:function(t){var e=this.plugins.findIndex((function(e){return e===t}));e>-1&&this.plugins.splice(e,1)}},{key:"getPlugins",value:function(){return this.plugins}},{key:"getPlugin",value:function(t){return this.plugins.find((function(e){return e.name===t}))}},{key:"getConfig",value:function(){return this.config}},{key:"setConfig",value:function(t){Object.assign(this.config,t)}}])}(),on=ut,sn=ot,un=function(t,e,n){return t[0]=Math.max(e[0],n[0]),t[1]=Math.max(e[1],n[1]),t[2]=Math.max(e[2],n[2]),t},ln=function(t,e,n){return t[0]=Math.min(e[0],n[0]),t[1]=Math.min(e[1],n[1]),t[2]=Math.min(e[2],n[2]),t},cn=ct,hn=yt,fn=function(){function t(){o(this,t),this.center=[0,0,0],this.halfExtents=[0,0,0],this.min=[0,0,0],this.max=[0,0,0]}return u(t,[{key:"update",value:function(t,e){sn(this.center,t),sn(this.halfExtents,e),hn(this.min,this.center,this.halfExtents),on(this.max,this.center,this.halfExtents)}},{key:"setMinMax",value:function(t,e){on(this.center,e,t),cn(this.center,this.center,.5),hn(this.halfExtents,e,t),cn(this.halfExtents,this.halfExtents,.5),sn(this.min,t),sn(this.max,e)}},{key:"getMin",value:function(){return this.min}},{key:"getMax",value:function(){return this.max}},{key:"add",value:function(e){if(!t.isEmpty(e))if(t.isEmpty(this))this.setMinMax(e.getMin(),e.getMax());else{var n=this.center,r=n[0],i=n[1],a=n[2],o=this.halfExtents,s=o[0],u=o[1],l=o[2],c=r-s,h=r+s,f=i-u,d=i+u,v=a-l,p=a+l,m=e.center,y=m[0],g=m[1],k=m[2],E=e.halfExtents,x=E[0],b=E[1],T=E[2],M=y-x,w=y+x,N=g-b,P=g+b,S=k-T,A=k+T;c>M&&(c=M),w>h&&(h=w),f>N&&(f=N),P>d&&(d=P),v>S&&(v=S),A>p&&(p=A),n[0]=.5*(c+h),n[1]=.5*(f+d),n[2]=.5*(v+p),o[0]=.5*(h-c),o[1]=.5*(d-f),o[2]=.5*(p-v),this.min[0]=c,this.min[1]=f,this.min[2]=v,this.max[0]=h,this.max[1]=d,this.max[2]=p}}},{key:"setFromTransformedAABB",value:function(t,e){var n=this.center,r=this.halfExtents,i=t.center,a=t.halfExtents,o=e[0],s=e[4],u=e[8],l=e[1],c=e[5],h=e[9],f=e[2],d=e[6],v=e[10],p=Math.abs(o),m=Math.abs(s),y=Math.abs(u),g=Math.abs(l),k=Math.abs(c),E=Math.abs(h),x=Math.abs(f),b=Math.abs(d),T=Math.abs(v);n[0]=e[12]+o*i[0]+s*i[1]+u*i[2],n[1]=e[13]+l*i[0]+c*i[1]+h*i[2],n[2]=e[14]+f*i[0]+d*i[1]+v*i[2],r[0]=p*a[0]+m*a[1]+y*a[2],r[1]=g*a[0]+k*a[1]+E*a[2],r[2]=x*a[0]+b*a[1]+T*a[2],hn(this.min,n,r),on(this.max,n,r)}},{key:"intersects",value:function(t){var e=this.getMax(),n=this.getMin(),r=t.getMax(),i=t.getMin();return!(n[0]>r[0]||i[0]>e[0]||n[1]>r[1]||i[1]>e[1]||n[2]>r[2]||i[2]>e[2])}},{key:"intersection",value:function(e){if(!this.intersects(e))return null;var n=new t,r=un([0,0,0],this.getMin(),e.getMin()),i=ln([0,0,0],this.getMax(),e.getMax());return n.setMinMax(r,i),n}},{key:"getNegativeFarPoint",value:function(t){return 273===t.pnVertexFlag?sn([0,0,0],this.min):272===t.pnVertexFlag?[this.min[0],this.min[1],this.max[2]]:257===t.pnVertexFlag?[this.min[0],this.max[1],this.min[2]]:256===t.pnVertexFlag?[this.min[0],this.max[1],this.max[2]]:17===t.pnVertexFlag?[this.max[0],this.min[1],this.min[2]]:16===t.pnVertexFlag?[this.max[0],this.min[1],this.max[2]]:1===t.pnVertexFlag?[this.max[0],this.max[1],this.min[2]]:[this.max[0],this.max[1],this.max[2]]}},{key:"getPositiveFarPoint",value:function(t){return 273===t.pnVertexFlag?sn([0,0,0],this.max):272===t.pnVertexFlag?[this.max[0],this.max[1],this.min[2]]:257===t.pnVertexFlag?[this.max[0],this.min[1],this.max[2]]:256===t.pnVertexFlag?[this.max[0],this.min[1],this.min[2]]:17===t.pnVertexFlag?[this.min[0],this.max[1],this.max[2]]:16===t.pnVertexFlag?[this.min[0],this.max[1],this.min[2]]:1===t.pnVertexFlag?[this.min[0],this.min[1],this.max[2]]:[this.min[0],this.min[1],this.min[2]]}}],[{key:"isEmpty",value:function(t){return!t||0===t.halfExtents[0]&&0===t.halfExtents[1]&&0===t.halfExtents[2]}}])}(),dn=function(){return u((function t(e,n){o(this,t),this.distance=e||0,this.normal=n||at(0,1,0),this.updatePNVertexFlag()}),[{key:"updatePNVertexFlag",value:function(){this.pnVertexFlag=(Number(this.normal[0]>=0)<<8)+(Number(this.normal[1]>=0)<<4)+Number(this.normal[2]>=0)}},{key:"distanceToPoint",value:function(t){return ft(t,this.normal)-this.distance}},{key:"normalize",value:function(){var t=1/kt(this.normal);ct(this.normal,this.normal,t),this.distance*=t}},{key:"intersectsLine",value:function(t,e,n){var r=this.distanceToPoint(t),i=r/(r-this.distanceToPoint(e)),a=i>=0&&1>=i;return a&&n&&vt(n,t,e,i),a}}])}(),vn=function(t){return t[t.OUTSIDE=4294967295]="OUTSIDE",t[t.INSIDE=0]="INSIDE",t[t.INDETERMINATE=2147483647]="INDETERMINATE",t}({}),pn=function(){return u((function t(e){if(o(this,t),this.planes=[],e)this.planes=e;else for(var n=0;6>n;n++)this.planes.push(new dn)}),[{key:"extractFromVPMatrix",value:function(t){var e=g(t,16),n=e[0],r=e[1],i=e[2],a=e[3],o=e[4],s=e[5],u=e[6],l=e[7],c=e[8],h=e[9],f=e[10],d=e[11],v=e[12],p=e[13],m=e[14],y=e[15];st(this.planes[0].normal,a-n,l-o,d-c),this.planes[0].distance=y-v,st(this.planes[1].normal,a+n,l+o,d+c),this.planes[1].distance=y+v,st(this.planes[2].normal,a+r,l+s,d+h),this.planes[2].distance=y+p,st(this.planes[3].normal,a-r,l-s,d-h),this.planes[3].distance=y-p,st(this.planes[4].normal,a-i,l-u,d-f),this.planes[4].distance=y-m,st(this.planes[5].normal,a+i,l+u,d+f),this.planes[5].distance=y+m,this.planes.forEach((function(t){t.normalize(),t.updatePNVertexFlag()}))}}])}(),mn=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;o(this,t),this.x=0,this.y=0,this.x=e,this.y=n}return u(t,[{key:"clone",value:function(){return new t(this.x,this.y)}},{key:"copyFrom",value:function(t){this.x=t.x,this.y=t.y}}])}(),yn=function(){function t(e,n,r,i){o(this,t),this.x=e,this.y=n,this.width=r,this.height=i,this.left=e,this.right=e+r,this.top=n,this.bottom=n+i}return u(t,[{key:"toJSON",value:function(){}}],[{key:"fromRect",value:function(e){return new t(e.x,e.y,e.width,e.height)}},{key:"applyTransform",value:function(e,n){var r=xt(e.x,e.y,0,1),i=xt(e.x+e.width,e.y,0,1),a=xt(e.x,e.y+e.height,0,1),o=xt(e.x+e.width,e.y+e.height,0,1),s=Et(),u=Et(),l=Et(),c=Et();bt(s,r,n),bt(u,i,n),bt(l,a,n),bt(c,o,n);var h=Math.min(s[0],u[0],l[0],c[0]),f=Math.min(s[1],u[1],l[1],c[1]);return t.fromRect({x:h,y:f,width:Math.max(s[0],u[0],l[0],c[0])-h,height:Math.max(s[1],u[1],l[1],c[1])-f})}}])}(),gn="Method not implemented.",kn="Use document.documentElement instead.";function En(t){return void 0===t?0:t>360||-360>t?t%360:t}var xn=nt();function bn(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=3>=arguments.length||void 0===arguments[3]||arguments[3];return Array.isArray(t)&&3===t.length?r?rt(t):ot(xn,t):zt(t)?r?at(t,e,n):st(xn,t,e,n):r?at(t[0],t[1]||e,t[2]||n):st(xn,t[0],t[1]||e,t[2]||n)}var Tn=Math.PI/180;function Mn(t){return t*Tn}var wn=180/Math.PI;function Nn(t){return t*wn}function Pn(t){return 360*t}var Sn=Math.PI/2;function An(t,e){return 16===e.length?function(t,e){var n,r,i=g(j(nt(),e),3),a=i[0],o=i[1],s=i[2],u=Math.asin(-e[2]/a);return Sn>u?u>-Sn?(n=Math.atan2(e[6]/o,e[10]/s),r=Math.atan2(e[1]/a,e[0]/a)):(r=0,n=-Math.atan2(e[4]/o,e[5]/o)):(r=0,n=Math.atan2(e[4]/o,e[5]/o)),t[0]=n,t[1]=u,t[2]=r,t}(t,e):function(t,e){var n=e[0],r=e[1],i=e[2],a=e[3],o=r*r,s=i*i,u=a*a,l=n*n+o+s+u,c=n*a-r*i;return c>.499995*l?(t[0]=Sn,t[1]=2*Math.atan2(r,n),t[2]=0):-.499995*l>c?(t[0]=-Sn,t[1]=2*Math.atan2(r,n),t[2]=0):(t[0]=Math.asin(2*(n*i-a*r)),t[1]=Math.atan2(2*(n*a+r*i),1-2*(s+u)),t[2]=Math.atan2(2*(n*r+i*a),1-2*(o+s))),t}(t,e)}function Cn(t,e,n,r,i){var a,o,s,u,l,c,h,f,d,v,p=Math.cos(t),m=Math.sin(t);return a=r*p,o=i*m,s=0,u=-r*m,l=i*p,c=0,h=e,f=n,d=1,(v=new b(9))[0]=a,v[1]=o,v[2]=s,v[3]=u,v[4]=l,v[5]=c,v[6]=h,v[7]=f,v[8]=d,v}function Rn(t){var e=t[0],n=t[1],r=t[3],i=t[4],a=Math.sqrt(e*e+n*n),o=Math.sqrt(r*r+i*i);if(0>e*i-n*r&&(i>e?a=-a:o=-o),a){var s=1/a;e*=s,n*=s}if(o){var u=1/o;r*=u,i*=u}var l=Nn(Math.atan2(n,e));return[t[6],t[7],a,o,l]}var On=M(),Ln=M(),In=Et(),_n=[nt(),nt(),nt()],Dn=nt();function Fn(t,e,n,r,i){t[0]=e[0]*r+n[0]*i,t[1]=e[1]*r+n[1]*i,t[2]=e[2]*r+n[2]*i}var Gn=function(t){return t[t.ORBITING=0]="ORBITING",t[t.EXPLORING=1]="EXPLORING",t[t.TRACKING=2]="TRACKING",t}({}),Bn=function(t){return t[t.DEFAULT=0]="DEFAULT",t[t.ROTATIONAL=1]="ROTATIONAL",t[t.TRANSLATIONAL=2]="TRANSLATIONAL",t[t.CINEMATIC=3]="CINEMATIC",t}({}),Vn=function(t){return t[t.ORTHOGRAPHIC=0]="ORTHOGRAPHIC",t[t.PERSPECTIVE=1]="PERSPECTIVE",t}({}),Yn={UPDATED:"updated"},Un=2e-4,zn=function(){return u((function t(){o(this,t),this.clipSpaceNearZ=nn.NEGATIVE_ONE,this.eventEmitter=new E,this.matrix=M(),this.right=at(1,0,0),this.up=at(0,1,0),this.forward=at(0,0,1),this.position=at(0,0,1),this.focalPoint=at(0,0,0),this.distanceVector=at(0,0,-1),this.distance=1,this.azimuth=0,this.elevation=0,this.roll=0,this.relAzimuth=0,this.relElevation=0,this.relRoll=0,this.dollyingStep=0,this.maxDistance=1/0,this.minDistance=-1/0,this.zoom=1,this.rotateWorld=!1,this.fov=30,this.near=.1,this.far=1e3,this.aspect=1,this.projectionMatrix=M(),this.projectionMatrixInverse=M(),this.jitteredProjectionMatrix=void 0,this.enableUpdate=!0,this.type=Gn.EXPLORING,this.trackingMode=Bn.DEFAULT,this.projectionMode=Vn.PERSPECTIVE,this.frustum=new pn,this.orthoMatrix=M()}),[{key:"isOrtho",value:function(){return this.projectionMode===Vn.ORTHOGRAPHIC}},{key:"getProjectionMode",value:function(){return this.projectionMode}},{key:"getPerspective",value:function(){return this.jitteredProjectionMatrix||this.projectionMatrix}},{key:"getPerspectiveInverse",value:function(){return this.projectionMatrixInverse}},{key:"getFrustum",value:function(){return this.frustum}},{key:"getPosition",value:function(){return this.position}},{key:"getFocalPoint",value:function(){return this.focalPoint}},{key:"getDollyingStep",value:function(){return this.dollyingStep}},{key:"getNear",value:function(){return this.near}},{key:"getFar",value:function(){return this.far}},{key:"getZoom",value:function(){return this.zoom}},{key:"getOrthoMatrix",value:function(){return this.orthoMatrix}},{key:"getView",value:function(){return this.view}},{key:"setEnableUpdate",value:function(t){this.enableUpdate=t}},{key:"setType",value:function(t,e){return this.type=t,this.setWorldRotation(this.type===Gn.EXPLORING),this._getAngles(),this.type===Gn.TRACKING&&void 0!==e&&this.setTrackingMode(e),this}},{key:"setProjectionMode",value:function(t){return this.projectionMode=t,this}},{key:"setTrackingMode",value:function(t){if(this.type!==Gn.TRACKING)throw Error("Impossible to set a tracking mode if the camera is not of tracking type");return this.trackingMode=t,this}},{key:"setWorldRotation",value:function(t){return this.rotateWorld=t,this._getAngles(),this}},{key:"getViewTransform",value:function(){return C(M(),this.matrix)}},{key:"getWorldTransform",value:function(){return this.matrix}},{key:"jitterProjectionMatrix",value:function(t,e){var n=D(M(),[t,e,0]);this.jitteredProjectionMatrix=O(M(),n,this.projectionMatrix)}},{key:"clearJitterProjectionMatrix",value:function(){this.jitteredProjectionMatrix=void 0}},{key:"setMatrix",value:function(t){return this.matrix=t,this._update(),this}},{key:"setProjectionMatrix",value:function(t){this.projectionMatrix=t}},{key:"setFov",value:function(t){return this.setPerspective(this.near,this.far,t,this.aspect),this}},{key:"setAspect",value:function(t){return this.setPerspective(this.near,this.far,this.fov,t),this}},{key:"setNear",value:function(t){return this.projectionMode===Vn.PERSPECTIVE?this.setPerspective(t,this.far,this.fov,this.aspect):this.setOrthographic(this.left,this.rright,this.top,this.bottom,t,this.far),this}},{key:"setFar",value:function(t){return this.projectionMode===Vn.PERSPECTIVE?this.setPerspective(this.near,t,this.fov,this.aspect):this.setOrthographic(this.left,this.rright,this.top,this.bottom,this.near,t),this}},{key:"setViewOffset",value:function(t,e,n,r,i,a){return this.aspect=t/e,void 0===this.view&&(this.view={enabled:!0,fullWidth:1,fullHeight:1,offsetX:0,offsetY:0,width:1,height:1}),this.view.enabled=!0,this.view.fullWidth=t,this.view.fullHeight=e,this.view.offsetX=n,this.view.offsetY=r,this.view.width=i,this.view.height=a,this.projectionMode===Vn.PERSPECTIVE?this.setPerspective(this.near,this.far,this.fov,this.aspect):this.setOrthographic(this.left,this.rright,this.top,this.bottom,this.near,this.far),this}},{key:"clearViewOffset",value:function(){return void 0!==this.view&&(this.view.enabled=!1),this.projectionMode===Vn.PERSPECTIVE?this.setPerspective(this.near,this.far,this.fov,this.aspect):this.setOrthographic(this.left,this.rright,this.top,this.bottom,this.near,this.far),this}},{key:"setZoom",value:function(t){return this.zoom=t,this.projectionMode===Vn.ORTHOGRAPHIC?this.setOrthographic(this.left,this.rright,this.top,this.bottom,this.near,this.far):this.projectionMode===Vn.PERSPECTIVE&&this.setPerspective(this.near,this.far,this.fov,this.aspect),this}},{key:"setZoomByViewportPoint",value:function(t,e){var n=this.canvas.viewport2Canvas({x:e[0],y:e[1]}),r=n.x,i=n.y,a=this.roll;this.rotate(0,0,-a),this.setPosition(r,i),this.setFocalPoint(r,i),this.setZoom(t),this.rotate(0,0,a);var o=this.canvas.viewport2Canvas({x:e[0],y:e[1]}),s=at(o.x-r,o.y-i,0),u=ft(s,this.right)/it(this.right),l=ft(s,this.up)/it(this.up),c=g(this.getPosition(),2),h=c[0],f=c[1],d=g(this.getFocalPoint(),2),v=d[0],p=d[1];return this.setPosition(h-u,f-l),this.setFocalPoint(v-u,p-l),this}},{key:"setPerspective",value:function(t,e,n,r){var i;this.projectionMode=Vn.PERSPECTIVE,this.fov=n,this.near=t,this.far=e,this.aspect=r;var a=this.near*Math.tan(Mn(.5*this.fov))/this.zoom,o=2*a,s=this.aspect*o,u=-.5*s;if(null!==(i=this.view)&&void 0!==i&&i.enabled){var l=this.view.fullWidth,c=this.view.fullHeight;u+=this.view.offsetX*s/l,a-=this.view.offsetY*o/c,s*=this.view.width/l,o*=this.view.height/c}return function(t,e,n,r,i,a,o){var s,u,l=2*a,c=n-e,h=r-i,f=l/h,d=(n+e)/c,v=(r+i)/h,p=o-a,m=o*a;arguments.length>7&&void 0!==arguments[7]&&arguments[7]?(s=-o/p,u=-m/p):(s=-(o+a)/p,u=-2*m/p),t[0]=l/c,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=f,t[6]=0,t[7]=0,t[8]=d,t[9]=v,t[10]=s,t[11]=-1,t[12]=0,t[13]=0,t[14]=u,t[15]=0}(this.projectionMatrix,u,u+s,a-o,a,t,this.far,this.clipSpaceNearZ===nn.ZERO),C(this.projectionMatrixInverse,this.projectionMatrix),this.triggerUpdate(),this}},{key:"setOrthographic",value:function(t,e,n,r,i,a){var o;this.projectionMode=Vn.ORTHOGRAPHIC,this.rright=e,this.left=t,this.top=n,this.bottom=r,this.near=i,this.far=a;var s=(this.rright-this.left)/(2*this.zoom),u=(this.top-this.bottom)/(2*this.zoom),l=(this.rright+this.left)/2,c=(this.top+this.bottom)/2,h=l-s,f=l+s,d=c+u,v=c-u;if(null!==(o=this.view)&&void 0!==o&&o.enabled){var p=(this.rright-this.left)/this.view.fullWidth/this.zoom,m=(this.top-this.bottom)/this.view.fullHeight/this.zoom;f=(h+=p*this.view.offsetX)+p*this.view.width,v=(d-=m*this.view.offsetY)-m*this.view.height}return this.clipSpaceNearZ===nn.NEGATIVE_ONE?K(this.projectionMatrix,h,f,d,v,i,a):Q(this.projectionMatrix,h,f,d,v,i,a),C(this.projectionMatrixInverse,this.projectionMatrix),this._getOrthoMatrix(),this.triggerUpdate(),this}},{key:"setPosition",value:function(t){var e=bn(t,arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.position[1],arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.position[2]);return this._setPosition(e),this.setFocalPoint(this.focalPoint),this.triggerUpdate(),this}},{key:"setFocalPoint",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.focalPoint[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.focalPoint[2],r=at(0,1,0);if(this.focalPoint=bn(t,e,n),this.trackingMode===Bn.CINEMATIC){var i=lt(nt(),this.focalPoint,this.position);t=i[0],e=i[1],n=i[2];var a=it(i),o=Nn(Math.asin(e/a)),s=90+Nn(Math.atan2(n,t)),u=M();_(u,u,Mn(s)),I(u,u,Mn(o)),r=pt(nt(),[0,1,0],u)}return C(this.matrix,$(M(),this.position,this.focalPoint,r)),this._getAxes(),this._getDistance(),this._getAngles(),this.triggerUpdate(),this}},{key:"getDistance",value:function(){return this.distance}},{key:"getDistanceVector",value:function(){return this.distanceVector}},{key:"setDistance",value:function(t){if(this.distance===t||0>t)return this;this.distance=t,Un>this.distance&&(this.distance=Un),this.dollyingStep=this.distance/100;var e=nt(),n=this.forward,r=this.focalPoint;return e[0]=(t=this.distance)*n[0]+r[0],e[1]=t*n[1]+r[1],e[2]=t*n[2]+r[2],this._setPosition(e),this.triggerUpdate(),this}},{key:"setMaxDistance",value:function(t){return this.maxDistance=t,this}},{key:"setMinDistance",value:function(t){return this.minDistance=t,this}},{key:"setAzimuth",value:function(t){return this.azimuth=En(t),this.computeMatrix(),this._getAxes(),this.type===Gn.ORBITING||this.type===Gn.EXPLORING?this._getPosition():this.type===Gn.TRACKING&&this._getFocalPoint(),this.triggerUpdate(),this}},{key:"getAzimuth",value:function(){return this.azimuth}},{key:"setElevation",value:function(t){return this.elevation=En(t),this.computeMatrix(),this._getAxes(),this.type===Gn.ORBITING||this.type===Gn.EXPLORING?this._getPosition():this.type===Gn.TRACKING&&this._getFocalPoint(),this.triggerUpdate(),this}},{key:"getElevation",value:function(){return this.elevation}},{key:"setRoll",value:function(t){return this.roll=En(t),this.computeMatrix(),this._getAxes(),this.type===Gn.ORBITING||this.type===Gn.EXPLORING?this._getPosition():this.type===Gn.TRACKING&&this._getFocalPoint(),this.triggerUpdate(),this}},{key:"getRoll",value:function(){return this.roll}},{key:"_update",value:function(){this._getAxes(),this._getPosition(),this._getDistance(),this._getAngles(),this._getOrthoMatrix(),this.triggerUpdate()}},{key:"computeMatrix",value:function(){var t=Mt(Tt(),[0,0,1],Mn(this.roll));S(this.matrix);var e=Mt(Tt(),[1,0,0],Mn((this.rotateWorld&&this.type!==Gn.TRACKING||this.type===Gn.TRACKING?1:-1)*this.elevation)),n=Mt(Tt(),[0,1,0],Mn((this.rotateWorld&&this.type!==Gn.TRACKING||this.type===Gn.TRACKING?1:-1)*this.azimuth)),r=wt(Tt(),n,e);r=wt(Tt(),r,t);var i=W(M(),r);this.type===Gn.ORBITING||this.type===Gn.EXPLORING?(L(this.matrix,this.matrix,this.focalPoint),O(this.matrix,this.matrix,i),L(this.matrix,this.matrix,[0,0,this.distance])):this.type===Gn.TRACKING&&(L(this.matrix,this.matrix,this.position),O(this.matrix,this.matrix,i))}},{key:"_setPosition",value:function(t,e,n){this.position=bn(t,e,n);var r=this.matrix;r[12]=this.position[0],r[13]=this.position[1],r[14]=this.position[2],r[15]=1,this._getOrthoMatrix()}},{key:"_getAxes",value:function(){ot(this.right,bn(bt(Et(),[1,0,0,0],this.matrix))),ot(this.up,bn(bt(Et(),[0,1,0,0],this.matrix))),ot(this.forward,bn(bt(Et(),[0,0,1,0],this.matrix))),ht(this.right,this.right),ht(this.up,this.up),ht(this.forward,this.forward)}},{key:"_getAngles",value:function(){var t=this.distanceVector[0],e=this.distanceVector[1],n=this.distanceVector[2],r=it(this.distanceVector);if(0===r)return this.elevation=0,void(this.azimuth=0);this.type===Gn.TRACKING||this.rotateWorld?(this.elevation=Nn(Math.asin(e/r)),this.azimuth=Nn(Math.atan2(-t,-n))):(this.elevation=-Nn(Math.asin(e/r)),this.azimuth=-Nn(Math.atan2(-t,-n)))}},{key:"_getPosition",value:function(){ot(this.position,bn(bt(Et(),[0,0,0,1],this.matrix))),this._getDistance()}},{key:"_getFocalPoint",value:function(){var t,e;!function(t,e,n){var r=e[0],i=e[1],a=e[2];t[0]=r*n[0]+i*n[3]+a*n[6],t[1]=r*n[1]+i*n[4]+a*n[7],t[2]=r*n[2]+i*n[5]+a*n[8]}(this.distanceVector,[0,0,-this.distance],(t=T(),e=this.matrix,t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[4],t[4]=e[5],t[5]=e[6],t[6]=e[8],t[7]=e[9],t[8]=e[10],t)),ut(this.focalPoint,this.position,this.distanceVector),this._getDistance()}},{key:"_getDistance",value:function(){this.distanceVector=lt(nt(),this.focalPoint,this.position),this.distance=it(this.distanceVector),this.dollyingStep=this.distance/100}},{key:"_getOrthoMatrix",value:function(){if(this.projectionMode===Vn.ORTHOGRAPHIC){var t=this.position,e=Mt(Tt(),[0,0,1],-this.roll*Math.PI/180);H(this.orthoMatrix,e,at((this.rright-this.left)/2-t[0],(this.top-this.bottom)/2-t[1],0),at(this.zoom,this.zoom,1),t)}}},{key:"triggerUpdate",value:function(){if(this.enableUpdate){var t=this.getViewTransform(),e=O(M(),this.getPerspective(),t);this.getFrustum().extractFromVPMatrix(e),this.eventEmitter.emit(Yn.UPDATED)}}},{key:"rotate",value:function(t,e,n){throw Error(gn)}},{key:"pan",value:function(t,e){throw Error(gn)}},{key:"dolly",value:function(t){throw Error(gn)}},{key:"createLandmark",value:function(t,e){throw Error(gn)}},{key:"gotoLandmark",value:function(t,e){throw Error(gn)}},{key:"cancelLandmarkAnimation",value:function(){throw Error(gn)}}])}(),jn=function(t){return t[t.Standard=0]="Standard",t}({}),Xn=function(t){return t[t.ADDED=0]="ADDED",t[t.REMOVED=1]="REMOVED",t[t.Z_INDEX_CHANGED=2]="Z_INDEX_CHANGED",t}({}),Hn={absolutePath:[],hasArc:!1,segments:[],polygons:[],polylines:[],curve:null,totalLength:0,rect:new yn(0,0,0,0)},Wn=function(t){return t.COORDINATE="<coordinate>",t.COLOR="<color>",t.PAINT="<paint>",t.NUMBER="<number>",t.ANGLE="<angle>",t.OPACITY_VALUE="<opacity-value>",t.SHADOW_BLUR="<shadow-blur>",t.LENGTH="<length>",t.PERCENTAGE="<percentage>",t.LENGTH_PERCENTAGE="<length> | <percentage>",t.LENGTH_PERCENTAGE_12="[<length> | <percentage>]{1,2}",t.LENGTH_PERCENTAGE_14="[<length> | <percentage>]{1,4}",t.LIST_OF_POINTS="<list-of-points>",t.PATH="<path>",t.FILTER="<filter>",t.Z_INDEX="<z-index>",t.OFFSET_DISTANCE="<offset-distance>",t.DEFINED_PATH="<defined-path>",t.MARKER="<marker>",t.TRANSFORM="<transform>",t.TRANSFORM_ORIGIN="<transform-origin>",t.TEXT="<text>",t.TEXT_TRANSFORM="<text-transform>",t}({});function qn(t,e,n){t.prototype=e.prototype=n,n.constructor=t}function Zn(t,e){var n=Object.create(t.prototype);for(var r in e)n[r]=e[r];return n}function Kn(){}var Qn=.7,$n=1/Qn,Jn="\\s*([+-]?\\d+)\\s*",tr="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",er="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",nr=/^#([0-9a-f]{3,8})$/,rr=RegExp("^rgb\\(".concat(Jn,",").concat(Jn,",").concat(Jn,"\\)$")),ir=RegExp("^rgb\\(".concat(er,",").concat(er,",").concat(er,"\\)$")),ar=RegExp("^rgba\\(".concat(Jn,",").concat(Jn,",").concat(Jn,",").concat(tr,"\\)$")),or=RegExp("^rgba\\(".concat(er,",").concat(er,",").concat(er,",").concat(tr,"\\)$")),sr=RegExp("^hsl\\(".concat(tr,",").concat(er,",").concat(er,"\\)$")),ur=RegExp("^hsla\\(".concat(tr,",").concat(er,",").concat(er,",").concat(tr,"\\)$")),lr={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function cr(){return this.rgb().formatHex()}function hr(){return this.rgb().formatRgb()}function fr(t){var e,n;return t=(t+"").trim().toLowerCase(),(e=nr.exec(t))?(n=e[1].length,e=parseInt(e[1],16),6===n?dr(e):3===n?new pr(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===n?vr(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===n?vr(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=rr.exec(t))?new pr(e[1],e[2],e[3],1):(e=ir.exec(t))?new pr(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=ar.exec(t))?vr(e[1],e[2],e[3],e[4]):(e=or.exec(t))?vr(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=sr.exec(t))?xr(e[1],e[2]/100,e[3]/100,1):(e=ur.exec(t))?xr(e[1],e[2]/100,e[3]/100,e[4]):lr.hasOwnProperty(t)?dr(lr[t]):"transparent"===t?new pr(NaN,NaN,NaN,0):null}function dr(t){return new pr(t>>16&255,t>>8&255,255&t,1)}function vr(t,e,n,r){return r>0||(t=e=n=NaN),new pr(t,e,n,r)}function pr(t,e,n,r){this.r=+t,this.g=+e,this.b=+n,this.opacity=+r}function mr(){return"#".concat(Er(this.r)).concat(Er(this.g)).concat(Er(this.b))}function yr(){var t=gr(this.opacity);return"".concat(1===t?"rgb(":"rgba(").concat(kr(this.r),", ").concat(kr(this.g),", ").concat(kr(this.b)).concat(1===t?")":", ".concat(t,")"))}function gr(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function kr(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function Er(t){return(16>(t=kr(t))?"0":"")+t.toString(16)}function xr(t,e,n,r){return r>0?n>0&&1>n?e>0||(t=NaN):t=e=NaN:t=e=n=NaN,new Tr(t,e,n,r)}function br(t){if(t instanceof Tr)return new Tr(t.h,t.s,t.l,t.opacity);if(t instanceof Kn||(t=fr(t)),!t)return new Tr;if(t instanceof Tr)return t;var e=(t=t.rgb()).r/255,n=t.g/255,r=t.b/255,i=Math.min(e,n,r),a=Math.max(e,n,r),o=NaN,s=a-i,u=(a+i)/2;return s?(o=e===a?(n-r)/s+6*(r>n):n===a?(r-e)/s+2:(e-n)/s+4,s/=.5>u?a+i:2-a-i,o*=60):s=u>0&&1>u?0:o,new Tr(o,s,u,t.opacity)}function Tr(t,e,n,r){this.h=+t,this.s=+e,this.l=+n,this.opacity=+r}function Mr(t){return 0>(t=(t||0)%360)?t+360:t}function wr(t){return Math.max(0,Math.min(1,t||0))}function Nr(t,e,n){return 255*(60>t?e+(n-e)*t/60:180>t?n:240>t?e+(n-e)*(240-t)/60:e)}function Pr(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw new TypeError("Expected a function");var n=function(){for(var r=arguments.length,i=Array(r),a=0;r>a;a++)i[a]=arguments[a];var o=e?e.apply(this,i):i[0],s=n.cache;if(s.has(o))return s.get(o);var u=t.apply(this,i);return n.cache=s.set(o,u)||s,u};return n.cache=new(Pr.Cache||Map),Pr.cacheList.push(n.cache),n}qn(Kn,fr,{copy:function(t){return Object.assign(new this.constructor,this,t)},displayable:function(){return this.rgb().displayable()},hex:cr,formatHex:cr,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return br(this).formatHsl()},formatRgb:hr,toString:hr}),qn(pr,(function(t,e,n,r){return 1===arguments.length?((i=t)instanceof Kn||(i=fr(i)),i?new pr((i=i.rgb()).r,i.g,i.b,i.opacity):new pr):new pr(t,e,n,null==r?1:r);var i}),Zn(Kn,{brighter:function(t){return new pr(this.r*(t=null==t?$n:Math.pow($n,t)),this.g*t,this.b*t,this.opacity)},darker:function(t){return new pr(this.r*(t=null==t?Qn:Math.pow(Qn,t)),this.g*t,this.b*t,this.opacity)},rgb:function(){return this},clamp:function(){return new pr(kr(this.r),kr(this.g),kr(this.b),gr(this.opacity))},displayable:function(){return this.r>=-.5&&255.5>this.r&&this.g>=-.5&&255.5>this.g&&this.b>=-.5&&255.5>this.b&&this.opacity>=0&&1>=this.opacity},hex:mr,formatHex:mr,formatHex8:function(){return"#".concat(Er(this.r)).concat(Er(this.g)).concat(Er(this.b)).concat(Er(255*(isNaN(this.opacity)?1:this.opacity)))},formatRgb:yr,toString:yr})),qn(Tr,(function(t,e,n,r){return 1===arguments.length?br(t):new Tr(t,e,n,null==r?1:r)}),Zn(Kn,{brighter:function(t){return new Tr(this.h,this.s,this.l*(t=null==t?$n:Math.pow($n,t)),this.opacity)},darker:function(t){return new Tr(this.h,this.s,this.l*(t=null==t?Qn:Math.pow(Qn,t)),this.opacity)},rgb:function(){var t=this.h%360+360*(0>this.h),e=this.l,n=e+(.5>e?e:1-e)*(isNaN(t)||isNaN(this.s)?0:this.s),r=2*e-n;return new pr(Nr(240>t?t+120:t-240,r,n),Nr(t,r,n),Nr(120>t?t+240:t-120,r,n),this.opacity)},clamp:function(){return new Tr(Mr(this.h),wr(this.s),wr(this.l),gr(this.opacity))},displayable:function(){return(this.s>=0&&1>=this.s||isNaN(this.s))&&this.l>=0&&1>=this.l&&this.opacity>=0&&1>=this.opacity},formatHsl:function(){var t=gr(this.opacity);return"".concat(1===t?"hsl(":"hsla(").concat(Mr(this.h),", ").concat(100*wr(this.s),"%, ").concat(100*wr(this.l),"%").concat(1===t?")":", ".concat(t,")"))}})),Pr.Cache=Map,Pr.cacheList=[],Pr.clearCache=function(){Pr.cacheList.forEach((function(t){return t.clear()}))};var Sr=function(t){return t[t.kUnknown=0]="kUnknown",t[t.kNumber=1]="kNumber",t[t.kPercentage=2]="kPercentage",t[t.kEms=3]="kEms",t[t.kPixels=4]="kPixels",t[t.kRems=5]="kRems",t[t.kDegrees=6]="kDegrees",t[t.kRadians=7]="kRadians",t[t.kGradians=8]="kGradians",t[t.kTurns=9]="kTurns",t[t.kMilliseconds=10]="kMilliseconds",t[t.kSeconds=11]="kSeconds",t[t.kInteger=12]="kInteger",t}({}),Ar=function(t){return t[t.kUNumber=0]="kUNumber",t[t.kUPercent=1]="kUPercent",t[t.kULength=2]="kULength",t[t.kUAngle=3]="kUAngle",t[t.kUTime=4]="kUTime",t[t.kUOther=5]="kUOther",t}({}),Cr=function(t){return t[t.kYes=0]="kYes",t[t.kNo=1]="kNo",t}({}),Rr=function(t){return t[t.kYes=0]="kYes",t[t.kNo=1]="kNo",t}({}),Or=[{name:"em",unit_type:Sr.kEms},{name:"px",unit_type:Sr.kPixels},{name:"deg",unit_type:Sr.kDegrees},{name:"rad",unit_type:Sr.kRadians},{name:"grad",unit_type:Sr.kGradians},{name:"ms",unit_type:Sr.kMilliseconds},{name:"s",unit_type:Sr.kSeconds},{name:"rem",unit_type:Sr.kRems},{name:"turn",unit_type:Sr.kTurns}],Lr=function(t){return t[t.kUnknownType=0]="kUnknownType",t[t.kUnparsedType=1]="kUnparsedType",t[t.kKeywordType=2]="kKeywordType",t[t.kUnitType=3]="kUnitType",t[t.kSumType=4]="kSumType",t[t.kProductType=5]="kProductType",t[t.kNegateType=6]="kNegateType",t[t.kInvertType=7]="kInvertType",t[t.kMinType=8]="kMinType",t[t.kMaxType=9]="kMaxType",t[t.kClampType=10]="kClampType",t[t.kTransformType=11]="kTransformType",t[t.kPositionType=12]="kPositionType",t[t.kURLImageType=13]="kURLImageType",t[t.kColorType=14]="kColorType",t[t.kUnsupportedColorType=15]="kUnsupportedColorType",t}({}),Ir=function(t){return t?"number"===t?Sr.kNumber:"percent"===t||"%"===t?Sr.kPercentage:function(t){return Or.find((function(e){return e.name===t})).unit_type}(t):Sr.kUnknown},_r=function(t){var e=1;switch(t){case Sr.kPixels:case Sr.kDegrees:case Sr.kSeconds:break;case Sr.kMilliseconds:e=.001;break;case Sr.kRadians:e=180/Math.PI;break;case Sr.kGradians:e=.9;break;case Sr.kTurns:e=360}return e},Dr=function(t){switch(t){case Sr.kNumber:case Sr.kInteger:return"";case Sr.kPercentage:return"%";case Sr.kEms:return"em";case Sr.kRems:return"rem";case Sr.kPixels:return"px";case Sr.kDegrees:return"deg";case Sr.kRadians:return"rad";case Sr.kGradians:return"grad";case Sr.kMilliseconds:return"ms";case Sr.kSeconds:return"s";case Sr.kTurns:return"turn"}return""},Fr=function(){return u((function t(){o(this,t)}),[{key:"toString",value:function(){return this.buildCSSText(Cr.kNo,Rr.kNo,"")}},{key:"isNumericValue",value:function(){return this.getType()>=Lr.kUnitType&&this.getType()<=Lr.kClampType}}],[{key:"isAngle",value:function(t){return t===Sr.kDegrees||t===Sr.kRadians||t===Sr.kGradians||t===Sr.kTurns}},{key:"isLength",value:function(t){return t>=Sr.kEms&&Sr.kDegrees>t}},{key:"isRelativeUnit",value:function(t){return t===Sr.kPercentage||t===Sr.kEms||t===Sr.kRems}},{key:"isTime",value:function(t){return t===Sr.kSeconds||t===Sr.kMilliseconds}}])}(),Gr=function(t){function e(t){var n;return o(this,e),(n=p(this,e)).colorSpace=t,n}return y(e,t),u(e,[{key:"getType",value:function(){return Lr.kColorType}},{key:"to",value:function(t){return this}}])}(Fr),Br=function(t){return t[t.Constant=0]="Constant",t[t.LinearGradient=1]="LinearGradient",t[t.RadialGradient=2]="RadialGradient",t}({}),Vr=function(t){function e(t,n){var r;return o(this,e),(r=p(this,e)).type=t,r.value=n,r}return y(e,t),u(e,[{key:"clone",value:function(){return new e(this.type,this.value)}},{key:"buildCSSText",value:function(t,e,n){return n}},{key:"getType",value:function(){return Lr.kColorType}}])}(Fr),Yr=function(t){function e(t){var n;return o(this,e),(n=p(this,e)).value=t,n}return y(e,t),u(e,[{key:"clone",value:function(){return new e(this.value)}},{key:"getType",value:function(){return Lr.kKeywordType}},{key:"buildCSSText",value:function(t,e,n){return n+this.value}}])}(Fr),Ur=function(t){return function(t){switch(t){case Ar.kUNumber:return Sr.kNumber;case Ar.kULength:return Sr.kPixels;case Ar.kUPercent:return Sr.kPercentage;case Ar.kUTime:return Sr.kSeconds;case Ar.kUAngle:return Sr.kDegrees;default:return Sr.kUnknown}}(function(t){switch(t){case Sr.kNumber:case Sr.kInteger:return Ar.kUNumber;case Sr.kPercentage:return Ar.kUPercent;case Sr.kPixels:return Ar.kULength;case Sr.kMilliseconds:case Sr.kSeconds:return Ar.kUTime;case Sr.kDegrees:case Sr.kRadians:case Sr.kGradians:case Sr.kTurns:return Ar.kUAngle;default:return Ar.kUOther}}(t))},zr=function(t){function e(t){var n,r,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Sr.kNumber;return o(this,e),n=p(this,e),r="string"==typeof i?Ir(i):i,n.unit=r,n.value=t,n}return y(e,t),u(e,[{key:"clone",value:function(){return new e(this.value,this.unit)}},{key:"equals",value:function(t){return this.value===t.value&&this.unit===t.unit}},{key:"getType",value:function(){return Lr.kUnitType}},{key:"convertTo",value:function(t){if(this.unit===t)return new e(this.value,this.unit);var n=Ur(this.unit);if(n!==Ur(t)||n===Sr.kUnknown)return null;var r=_r(this.unit)/_r(t);return new e(this.value*r,t)}},{key:"buildCSSText",value:function(t,e,n){var r;switch(this.unit){case Sr.kUnknown:break;case Sr.kInteger:r=Number(this.value).toFixed(0);break;case Sr.kNumber:case Sr.kPercentage:case Sr.kEms:case Sr.kRems:case Sr.kPixels:case Sr.kDegrees:case Sr.kRadians:case Sr.kGradians:case Sr.kMilliseconds:case Sr.kSeconds:case Sr.kTurns:var i=this.value,a=Dr(this.unit);if(-999999>i||i>999999){var o=Dr(this.unit);r=!Number.isFinite(i)||Number.isNaN(i)?function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return(Number.isFinite(t)?"NaN":t>0?"infinity":"-infinity")+e}(i,o):i+(o||"")}else r="".concat(i).concat(a)}return n+=r}}])}(Fr),jr=new zr(0,"px");new zr(1,"px");var Xr=new zr(0,"deg"),Hr=function(t){function e(t,n,r){var i,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,s=arguments.length>4&&void 0!==arguments[4]&&arguments[4];return o(this,e),(i=p(this,e,["rgb"])).r=t,i.g=n,i.b=r,i.alpha=a,i.isNone=s,i}return y(e,t),u(e,[{key:"clone",value:function(){return new e(this.r,this.g,this.b,this.alpha)}},{key:"buildCSSText",value:function(t,e,n){return"".concat(n,"rgba(").concat(this.r,",").concat(this.g,",").concat(this.b,",").concat(this.alpha,")")}}])}(Gr),Wr=new Yr("unset"),qr={"":Wr,unset:Wr,initial:new Yr("initial"),inherit:new Yr("inherit")},Zr=new Hr(0,0,0,0,!0),Kr=new Hr(0,0,0,0),Qr=Pr((function(t,e,n,r){return new Hr(t,e,n,r)}),(function(t,e,n,r){return"rgba(".concat(t,",").concat(e,",").concat(n,",").concat(r,")")})),$r=function(t){return new zr(t,arguments.length>1&&void 0!==arguments[1]?arguments[1]:Sr.kNumber)};function Jr(t){var e=t.type,n=t.value;return"hex"===e?"#".concat(n):"literal"===e?n:"rgb"===e?"rgb(".concat(n.join(","),")"):"rgba(".concat(n.join(","),")")}new zr(50,"%");var ti=function(){var t=/^(linear\-gradient)/i,e=/^(repeating\-linear\-gradient)/i,n=/^(radial\-gradient)/i,r=/^(repeating\-radial\-gradient)/i,i=/^(conic\-gradient)/i,a=/^to (left (top|bottom)|right (top|bottom)|top (left|right)|bottom (left|right)|left|right|top|bottom)/i,o=/^(closest\-side|closest\-corner|farthest\-side|farthest\-corner|contain|cover)/,s=/^(left|center|right|top|bottom)/i,u=/^(-?(([0-9]*\.[0-9]+)|([0-9]+\.?)))px/,l=/^(-?(([0-9]*\.[0-9]+)|([0-9]+\.?)))\%/,c=/^(-?(([0-9]*\.[0-9]+)|([0-9]+\.?)))em/,h=/^(-?(([0-9]*\.[0-9]+)|([0-9]+\.?)))deg/,f=/^\(/,d=/^\)/,v=/^,/,p=/^\#([0-9a-fA-F]+)/,m=/^([a-zA-Z]+)/,y=/^rgb/i,g=/^rgba/i,k=/^(([0-9]*\.[0-9]+)|([0-9]+\.?))/,E="";function x(t){throw Error("".concat(E,": ").concat(t))}function b(){var t=O(T);return E.length>0&&x("Invalid input not EOF"),t}function T(){return M("linear-gradient",t,N)||M("repeating-linear-gradient",e,N)||M("radial-gradient",n,P)||M("repeating-radial-gradient",r,P)||M("conic-gradient",i,P)}function M(t,e,n){return w(e,(function(e){var r=n();return r&&(G(v)||x("Missing comma before color stops")),{type:t,orientation:r,colorStops:O(L)}}))}function w(t,e){var n=G(t);if(n){G(f)||x("Missing (");var r=e(n);return G(d)||x("Missing )"),r}}function N(){return F("directional",a,1)||F("angular",h,1)}function P(){var t,e,n=S();return n&&((t=[]).push(n),e=E,G(v)&&((n=S())?t.push(n):E=e)),t}function S(){var t=function(){var t=F("shape",/^(circle)/i,0);t&&(t.style=D()||A());return t}()||function(){var t=F("shape",/^(ellipse)/i,0);t&&(t.style=_()||A());return t}();if(t)t.at=C();else{var e=A();if(e){t=e;var n=C();n&&(t.at=n)}else{var r=R();r&&(t={type:"default-radial",at:r})}}return t}function A(){return F("extent-keyword",o,1)}function C(){if(F("position",/^at/,0)){var t=R();return t||x("Missing positioning value"),t}}function R(){var t={x:_(),y:_()};if(t.x||t.y)return{type:"position",value:t}}function O(t){var e=t(),n=[];if(e)for(n.push(e);G(v);)(e=t())?n.push(e):x("One extra comma");return n}function L(){var t=F("hex",p,1)||w(g,(function(){return{type:"rgba",value:O(I)}}))||w(y,(function(){return{type:"rgb",value:O(I)}}))||F("literal",m,0);return t||x("Expected color definition"),t.length=_(),t}function I(){return G(k)[1]}function _(){return F("%",l,1)||F("position-keyword",s,1)||D()}function D(){return F("px",u,1)||F("em",c,1)}function F(t,e,n){var r=G(e);if(r)return{type:t,value:r[n]}}function G(t){var e=/^[\n\r\t\s]+/.exec(E);e&&B(e[0].length);var n=t.exec(E);return n&&B(n[0].length),n}function B(t){E=E.substring(t)}return function(t){return E=t,b()}}();var ei=/^l\s*\(\s*([\d.]+)\s*\)\s*(.*)/i,ni=/^r\s*\(\s*([\d.]+)\s*,\s*([\d.]+)\s*,\s*([\d.]+)\s*\)\s*(.*)/i,ri=/^p\s*\(\s*([axyn])\s*\)\s*(.*)/i,ii=/[\d.]+:(#[^\s]+|[^\)]+\))/gi;var ai={left:180,top:-90,bottom:90,right:0,"left top":225,"top left":225,"left bottom":135,"bottom left":135,"right top":-45,"top right":-45,"right bottom":45,"bottom right":45},oi=Pr((function(t){return $r("angular"===t.type?Number(t.value):ai[t.value]||0,"deg")})),si=Pr((function(t){var e=50,n=50,r="%",i="%";if("position"===(null==t?void 0:t.type)){var a=t.value,o=a.x,s=a.y;"position-keyword"===(null==o?void 0:o.type)&&("left"===o.value?e=0:"center"===o.value?e=50:"right"===o.value?e=100:"top"===o.value?n=0:"bottom"===o.value&&(n=100)),"position-keyword"===(null==s?void 0:s.type)&&("left"===s.value?e=0:"center"===s.value?n=50:"right"===s.value?e=100:"top"===s.value?n=0:"bottom"===s.value&&(n=100)),"px"!==(null==o?void 0:o.type)&&"%"!==(null==o?void 0:o.type)&&"em"!==(null==o?void 0:o.type)||(r=null==o?void 0:o.type,e=Number(o.value)),"px"!==(null==s?void 0:s.type)&&"%"!==(null==s?void 0:s.type)&&"em"!==(null==s?void 0:s.type)||(i=null==s?void 0:s.type,n=Number(s.value))}return{cx:$r(e,r),cy:$r(n,i)}})),ui=Pr((function(t){if(t.indexOf("linear")>-1||t.indexOf("radial")>-1)return ti(t).map((function(t){var e=t.type,n=t.orientation,r=t.colorStops;!function(t){var e,n,r=t.length;t[r-1].length=null!==(e=t[r-1].length)&&void 0!==e?e:{type:"%",value:"100"},r>1&&(t[0].length=null!==(n=t[0].length)&&void 0!==n?n:{type:"%",value:"0"});for(var i=0,a=Number(t[0].length.value),o=1;r>o;o++){var s,u=null===(s=t[o].length)||void 0===s?void 0:s.value;if(!_t(u)&&!_t(a)){for(var l=1;o-i>l;l++)t[i+l].length={type:"%",value:"".concat(a+(Number(u)-a)*l/(o-i))};i=o,a=Number(u)}}}(r);var i,a=r.map((function(t){return{offset:$r(Number(t.length.value),"%"),color:Jr(t)}}));if("linear-gradient"===e)return new Vr(Br.LinearGradient,{angle:n?oi(n):Xr,steps:a});if("radial-gradient"===e&&(n||(n=[{type:"shape",value:"circle"}]),"shape"===n[0].type&&"circle"===n[0].value)){var o,s=si(n[0].at),u=s.cx,l=s.cy;if(n[0].style){var c=n[0].style,h=c.type,f=c.value;"extent-keyword"===h?(qr[i=f]||(qr[i]=new Yr(i)),o=qr[i]):o=$r(f,h)}return new Vr(Br.RadialGradient,{cx:u,cy:l,size:o,steps:a})}}));var e=t[0];if("("===t[1]||"("===t[2])if("l"===e){var n=ei.exec(t);if(n){var r,i=(null===(r=n[2].match(ii))||void 0===r?void 0:r.map((function(t){return t.split(":")})))||[];return[new Vr(Br.LinearGradient,{angle:$r(parseFloat(n[1]),"deg"),steps:i.map((function(t){var e=g(t,2),n=e[1];return{offset:$r(100*Number(e[0]),"%"),color:n}}))})]}}else if("r"===e){var a=function(t){var e=ni.exec(t);if(e){var n,r=(null===(n=e[4].match(ii))||void 0===n?void 0:n.map((function(t){return t.split(":")})))||[];return{cx:$r(50,"%"),cy:$r(50,"%"),steps:r.map((function(t){var e=g(t,2),n=e[1];return{offset:$r(100*Number(e[0]),"%"),color:n}}))}}return null}(t);if(a){if(!Yt(a))return[new Vr(Br.RadialGradient,a)];t=a}}else if("p"===e)return function(t){var e=ri.exec(t);if(e){var n=e[1],r=e[2];switch(n){case"a":n="repeat";break;case"x":n="repeat-x";break;case"y":n="repeat-y";break;default:n="no-repeat"}return{image:r,repetition:n}}return null}(t)}));function li(t){return t&&!!t.image}function ci(t){return t&&!_t(t.r)&&!_t(t.g)&&!_t(t.b)}var hi=Pr((function(t){if(li(t))return a({repetition:"repeat"},t);if(_t(t)&&(t=""),"transparent"===t)return Kr;if("currentColor"===t)t="black";else if("none"===t)return Zr;var e=ui(t);if(e)return e;var n=fr(t),r=[0,0,0,0];return null!==n&&(r[0]=n.r||0,r[1]=n.g||0,r[2]=n.b||0,r[3]=n.opacity),Qr.apply(void 0,r)}));function fi(t,e){if(ci(t)&&ci(e))return[[Number(t.r),Number(t.g),Number(t.b),Number(t.alpha)],[Number(e.r),Number(e.g),Number(e.b),Number(e.alpha)],function(t){var e=t.slice();if(e[3])for(var n=0;3>n;n++)e[n]=Math.round(Ut(e[n],0,255));return e[3]=Ut(e[3],0,1),"rgba(".concat(e.join(","),")")}]}function di(t,e){if(_t(e))return $r(0,"px");if(e="".concat(e).trim().toLowerCase(),isFinite(Number(e))){if("px".search(t)>=0)return $r(Number(e),"px");if("deg".search(t)>=0)return $r(Number(e),"deg")}var n=[];e=e.replace(t,(function(t){return n.push(t),"U".concat(t)}));var r="U(".concat(t.source,")");return n.map((function(t){return $r(Number(e.replace(RegExp("U".concat(t),"g"),"").replace(RegExp(r,"g"),"*0")),t)}))[0]}var vi=function(t){return di(/px/g,t)},pi=Pr(vi);Pr((function(t){return di(RegExp("%","g"),t)}));var mi=function(t){return zt(t)||isFinite(Number(t))?$r(Number(t)||0,"px"):di(RegExp("px|%|em|rem","g"),t)},yi=Pr(mi),gi=function(t){return di(/deg|rad|grad|turn/g,t)},ki=Pr(gi);function Ei(t,e,n,r){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,a="",o=t.value||0,s=e.value||0,u=Ur(t.unit),l=t.convertTo(u),c=e.convertTo(u);return l&&c?(o=l.value,s=c.value,a=Dr(t.unit)):(zr.isLength(t.unit)||zr.isLength(e.unit))&&(o=Ti(t,i,n),s=Ti(e,i,n),a="px"),[o,s,function(t){return r&&(t=Math.max(t,0)),t+a}]}function xi(t){var e=0;return t.unit===Sr.kDegrees?e=t.value:t.unit===Sr.kRadians?e=Nn(Number(t.value)):t.unit===Sr.kTurns?e=Pn(Number(t.value)):t.value&&(e=t.value),e}function bi(t,e){var n;return Array.isArray(t)?n=t.map((function(t){return Number(t)})):Yt(t)?n=t.split(" ").map((function(t){return Number(t)})):zt(t)&&(n=[t]),2===e?1===n.length?[n[0],n[0]]:[n[0],n[1]]:4===e?1===n.length?[n[0],n[0],n[0],n[0]]:2===n.length?[n[0],n[1],n[0],n[1]]:3===n.length?[n[0],n[1],n[2],n[1]]:[n[0],n[1],n[2],n[3]]:"even"===e&&n.length%2==1?[].concat(h(n),h(n)):n}function Ti(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(t.unit===Sr.kPixels)return Number(t.value);if(t.unit===Sr.kPercentage&&n){var i=n.nodeName===en.GROUP?n.getLocalBounds():n.getGeometryBounds();return(r?i.min[e]:0)+t.value/100*i.halfExtents[e]*2}return 0}var Mi=["blur","brightness","drop-shadow","contrast","grayscale","sepia","saturate","hue-rotate","invert"];function wi(t){return""+t}var Ni=function(t){return $r("number"==typeof t?t:/^\s*[-+]?(\d*\.)?\d+\s*$/.test(t)?Number(t):0)},Pi=Pr(Ni);function Si(t,e){return[t,e,wi]}function Ai(t,e){return function(n,r){return[n,r,function(n){return wi(Ut(n,t,e))}]}}function Ci(t,e){if(t.length===e.length)return[t,e,function(t){return t}]}function Ri(t){var e;return 0===t.parsedStyle.d.totalLength&&(t.parsedStyle.d.totalLength=Se(t.parsedStyle.d.absolutePath,void 0,qt(qt({},e),{bbox:!1,length:!0})).length),t.parsedStyle.d.totalLength}function Oi(t,e){return t[0]===e[0]&&t[1]===e[1]}function Li(t,e){var n=t.prePoint,r=t.currentPoint,i=t.nextPoint,a=Math.pow(r[0]-n[0],2)+Math.pow(r[1]-n[1],2),o=Math.pow(r[0]-i[0],2)+Math.pow(r[1]-i[1],2),s=Math.acos((a+o-(Math.pow(n[0]-i[0],2)+Math.pow(n[1]-i[1],2)))/(2*Math.sqrt(a)*Math.sqrt(o)));if(!s||0===Math.sin(s)||jt(s,0))return{xExtra:0,yExtra:0};var u=Math.abs(Math.atan2(i[1]-r[1],i[0]-r[0])),l=Math.abs(Math.atan2(i[0]-r[0],i[1]-r[1]));return l=l>Math.PI/2?Math.PI-l:l,{xExtra:Math.cos(s/2-(u=u>Math.PI/2?Math.PI-u:u))*(e/2*(1/Math.sin(s/2)))-e/2||0,yExtra:Math.cos(l-s/2)*(e/2*(1/Math.sin(s/2)))-e/2||0}}function Ii(t,e){return[e[0]+(e[0]-t[0]),e[1]+(e[1]-t[1])]}Pr((function(t){return Yt(t)?t.split(" ").map(Pi):t.map(Pi)}));var _i=function(t,e){return(0>t.x*e.y-t.y*e.x?-1:1)*Math.acos((t.x*e.x+t.y*e.y)/Math.sqrt((Math.pow(t.x,2)+Math.pow(t.y,2))*(Math.pow(e.x,2)+Math.pow(e.y,2))))},Di=function(t,e,n,r,i,a,o,s){e=Math.abs(e),n=Math.abs(n);var u=Mn(r=Xt(r,360));if(t.x===o.x&&t.y===o.y)return{x:t.x,y:t.y,ellipticalArcAngle:0};if(0===e||0===n)return{x:0,y:0,ellipticalArcAngle:0};var l=(t.x-o.x)/2,c=(t.y-o.y)/2,h={x:Math.cos(u)*l+Math.sin(u)*c,y:-Math.sin(u)*l+Math.cos(u)*c},f=Math.pow(h.x,2)/Math.pow(e,2)+Math.pow(h.y,2)/Math.pow(n,2);f>1&&(e*=Math.sqrt(f),n*=Math.sqrt(f));var d=(Math.pow(e,2)*Math.pow(n,2)-Math.pow(e,2)*Math.pow(h.y,2)-Math.pow(n,2)*Math.pow(h.x,2))/(Math.pow(e,2)*Math.pow(h.y,2)+Math.pow(n,2)*Math.pow(h.x,2)),v=(i!==a?1:-1)*Math.sqrt(d=0>d?0:d),p=v*(e*h.y/n),m=v*(-n*h.x/e),y={x:Math.cos(u)*p-Math.sin(u)*m+(t.x+o.x)/2,y:Math.sin(u)*p+Math.cos(u)*m+(t.y+o.y)/2},g={x:(h.x-p)/e,y:(h.y-m)/n},k=_i({x:1,y:0},g),E=_i(g,{x:(-h.x-p)/e,y:(-h.y-m)/n});!a&&E>0?E-=2*Math.PI:a&&0>E&&(E+=2*Math.PI);var x=k+(E%=2*Math.PI)*s,b=e*Math.cos(x),T=n*Math.sin(x);return{x:Math.cos(u)*b-Math.sin(u)*T+y.x,y:Math.sin(u)*b+Math.cos(u)*T+y.y,ellipticalArcStartAngle:k,ellipticalArcEndAngle:k+E,ellipticalArcAngle:x,ellipticalArcCenter:y,resultantRx:e,resultantRy:n}};function Fi(t,e){var n=2>=arguments.length||void 0===arguments[2]||arguments[2],r=t.arcParams,i=r.rx,a=void 0===i?0:i,o=r.ry,s=void 0===o?0:o,u=r.xRotation,l=r.arcFlag,c=r.sweepFlag,h=Di({x:t.prePoint[0],y:t.prePoint[1]},a,s,u,!!l,!!c,{x:t.currentPoint[0],y:t.currentPoint[1]},e),f=Di({x:t.prePoint[0],y:t.prePoint[1]},a,s,u,!!l,!!c,{x:t.currentPoint[0],y:t.currentPoint[1]},n?e+.005:e-.005),d=f.x-h.x,v=f.y-h.y,p=Math.sqrt(d*d+v*v);return{x:-d/p,y:-v/p}}function Gi(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}function Bi(t,e){return Gi(t)*Gi(e)?(t[0]*e[0]+t[1]*e[1])/(Gi(t)*Gi(e)):1}function Vi(t,e){return(t[1]*e[0]>t[0]*e[1]?-1:1)*Math.acos(Bi(t,e))}function Yi(t,e){var n=e[1],r=e[2],i=Xt(Mn(e[3]),2*Math.PI),a=e[4],o=e[5],s=t[0],u=t[1],l=e[6],c=e[7],h=Math.cos(i)*(s-l)/2+Math.sin(i)*(u-c)/2,f=-1*Math.sin(i)*(s-l)/2+Math.cos(i)*(u-c)/2,d=h*h/(n*n)+f*f/(r*r);d>1&&(n*=Math.sqrt(d),r*=Math.sqrt(d));var v=n*n*(f*f)+r*r*(h*h),p=v?Math.sqrt((n*n*(r*r)-v)/v):1;a===o&&(p*=-1),isNaN(p)&&(p=0);var m=r?p*n*f/r:0,y=n?p*-r*h/n:0,g=(s+l)/2+Math.cos(i)*m-Math.sin(i)*y,k=(u+c)/2+Math.sin(i)*m+Math.cos(i)*y,E=[(h-m)/n,(f-y)/r],x=[(-1*h-m)/n,(-1*f-y)/r],b=Vi([1,0],E),T=Vi(E,x);return Bi(E,x)>-1||(T=Math.PI),1>Bi(E,x)||(T=0),0===o&&T>0&&(T-=2*Math.PI),1===o&&0>T&&(T+=2*Math.PI),{cx:g,cy:k,rx:Oi(t,[l,c])?0:n,ry:Oi(t,[l,c])?0:r,startAngle:b,endAngle:b+T,xRotation:i,arcFlag:a,sweepFlag:o}}function Ui(t,e,n,r){var i=.5522847498307936,a=t*i,o=e*i,s=n-t,u=n+t,l=r-e,c=r+e;return[["M",s,r],["C",s,r-o,n-a,l,n,l],["C",n+a,l,u,r-o,u,r],["C",u,r+o,n+a,c,n,c],["C",n-a,c,s,r+o,s,r],["Z"]]}var zi=function(t){if(""===t||Array.isArray(t)&&0===t.length)return{absolutePath:[],hasArc:!1,segments:[],polygons:[],polylines:[],curve:null,totalLength:0,rect:{x:0,y:0,width:0,height:0}};var e;try{e=fe(t)}catch(n){e=fe(""),console.error("[g]: Invalid SVG Path definition: ".concat(t))}!function(t){for(var e=0;t.length>e;e++){var n=t[e-1],r=t[e];if("M"===r[0]&&n){var i=n[0],a=void 0;"L"===i||"M"===i?a=[n[1],n[2]]:"C"!==i&&"A"!==i&&"Q"!==i||(a=[n[n.length-2],n[n.length-1]]),a&&Oi([r[1],r[2]],a)&&(t.splice(e,1),e--)}}}(e);var n=function(t){for(var e=!1,n=t.length,r=0;n>r;r++){var i=t[r][0];if("C"===i||"A"===i||"Q"===i){e=!0;break}}return e}(e),r=function(t){for(var e=[],n=[],r=[],i=0;t.length>i;i++){var a=t[i],o=a[0];"M"===o?(r.length&&(n.push(r),r=[]),r.push([a[1],a[2]])):"Z"===o?r.length&&(e.push(r),r=[]):r.push([a[1],a[2]])}return r.length>0&&n.push(r),{polygons:e,polylines:n}}(e),i=r.polygons,a=r.polylines,o=function(t){for(var e=[],n=null,r=null,i=null,a=0,o=t.length,s=0;o>s;s++){var u=t[s];r=t[s+1];var l=u[0],c={command:l,prePoint:n,params:u,startTangent:null,endTangent:null,currentPoint:null,nextPoint:null,arcParams:null,box:null,cubicParams:null};switch(l){case"M":i=[u[1],u[2]],a=s;break;case"A":var h=Yi(n,u);c.arcParams=h}if("Z"===l)n=i,r=t[a+1];else{var f=u.length;n=[u[f-2],u[f-1]]}r&&"Z"===r[0]&&(r=t[a],e[a]&&(e[a].prePoint=n)),c.currentPoint=n,e[a]&&Oi(n,e[a].currentPoint)&&(e[a].prePoint=c.prePoint),c.nextPoint=r?[r[r.length-2],r[r.length-1]]:null;var d=c.prePoint;if(["L","H","V"].includes(l))c.startTangent=[d[0]-n[0],d[1]-n[1]],c.endTangent=[n[0]-d[0],n[1]-d[1]];else if("Q"===l){var v=[u[1],u[2]];c.startTangent=[d[0]-v[0],d[1]-v[1]],c.endTangent=[n[0]-v[0],n[1]-v[1]]}else if("T"===l){var p=e[s-1],m=Ii(p.currentPoint,d);"Q"===p.command?(c.command="Q",c.startTangent=[d[0]-m[0],d[1]-m[1]],c.endTangent=[n[0]-m[0],n[1]-m[1]]):(c.command="TL",c.startTangent=[d[0]-n[0],d[1]-n[1]],c.endTangent=[n[0]-d[0],n[1]-d[1]])}else if("C"===l){var y=[u[1],u[2]],g=[u[3],u[4]];c.startTangent=[d[0]-y[0],d[1]-y[1]],c.endTangent=[n[0]-g[0],n[1]-g[1]],0===c.startTangent[0]&&0===c.startTangent[1]&&(c.startTangent=[y[0]-g[0],y[1]-g[1]]),0===c.endTangent[0]&&0===c.endTangent[1]&&(c.endTangent=[g[0]-y[0],g[1]-y[1]])}else if("S"===l){var k=e[s-1],E=Ii(k.currentPoint,d),x=[u[1],u[2]];"C"===k.command?(c.command="C",c.startTangent=[d[0]-E[0],d[1]-E[1]],c.endTangent=[n[0]-x[0],n[1]-x[1]]):(c.command="SQ",c.startTangent=[d[0]-x[0],d[1]-x[1]],c.endTangent=[n[0]-x[0],n[1]-x[1]])}else if("A"===l){var b=Fi(c,0),T=b.x,M=b.y,w=Fi(c,1,!1),N=w.x,P=w.y;c.startTangent=[T,M],c.endTangent=[N,P]}e.push(c)}return e}(e),s=function(t,e){for(var n=[],r=[],i=[],a=0;t.length>a;a++){var o=t[a],s=o.currentPoint,u=o.params,l=o.prePoint,c=void 0;switch(o.command){case"Q":c=We(l[0],l[1],u[1],u[2],u[3],u[4]);break;case"C":c=ze(l[0],l[1],u[1],u[2],u[3],u[4],u[5],u[6]);break;case"A":var h=o.arcParams;c=Ge(h.cx,h.cy,h.rx,h.ry,h.xRotation,h.startAngle,h.endAngle);break;default:n.push(s[0]),r.push(s[1])}c&&(o.box=c,n.push(c.x,c.x+c.width),r.push(c.y,c.y+c.height)),e&&("L"===o.command||"M"===o.command)&&o.prePoint&&o.nextPoint&&i.push(o)}n=n.filter((function(t){return!Number.isNaN(t)&&t!==1/0&&t!==-1/0})),r=r.filter((function(t){return!Number.isNaN(t)&&t!==1/0&&t!==-1/0}));var f=Vt(n),d=Vt(r),v=Bt(n),p=Bt(r);if(0===i.length)return{x:f,y:d,width:v-f,height:p-d};for(var m=0;i.length>m;m++){var y=i[m],g=y.currentPoint;g[0]===f?f-=Li(y,e).xExtra:g[0]===v&&(v+=Li(y,e).xExtra),g[1]===d?d-=Li(y,e).yExtra:g[1]===p&&(p+=Li(y,e).yExtra)}return{x:f,y:d,width:v-f,height:p-d}}(o,0),u=s.x,l=s.y,c=s.width,h=s.height;return{absolutePath:e,hasArc:n,segments:o,polygons:i,polylines:a,totalLength:0,rect:{x:Number.isFinite(u)?u:0,y:Number.isFinite(l)?l:0,width:Number.isFinite(c)?c:0,height:Number.isFinite(h)?h:0}}},ji=Pr(zi);function Xi(t){return Yt(t)?ji(t):zi(t)}function Hi(t,e,n){var r=t.curve,i=e.curve;r&&0!==r.length||(r=ge(t.absolutePath,!1),t.curve=r),i&&0!==i.length||(i=ge(e.absolutePath,!1),e.curve=i);var a=[r,i];r.length!==i.length&&(a=Re(r,i));var o,s,u,l,c,h,f,d,v=Ae(a[0])!==Ae(a[1])?(s=(o=a[0]).slice(1).map((function(t,e,n){return e?n[e-1].slice(-2).concat(t.slice(1)):o[0].slice(1).concat(t.slice(1))})).map((function(t){return t.map((function(e,n){return t[t.length-n-2*(1-n%2)]}))})).reverse(),[["M"].concat(s[0].slice(0,2))].concat(s.map((function(t){return["C"].concat(t.slice(2))})))):a[0].map((function(t){return Array.isArray(t)?[].concat(t):t}));return[v,(u=a[1],l=v,c=u.length-1,h=[],f=0,d=function(t){var e=t.length,n=e-1;return t.map((function(r,i){return t.map((function(r,a){var o=i+a;return 0===a||t[o]&&"M"===t[o][0]?["M"].concat(t[o].slice(-2)):(e>o||(o-=n),t[o])}))}))}(u),d.forEach((function(t,e){u.slice(1).forEach((function(t,n){f+=ke(u[(e+n)%c].slice(-2),l[n%c].slice(-2))})),h[e]=f,f=0})),d[h.indexOf(Math.min.apply(null,h))]),function(t){return t}]}function Wi(t,e){return[t.points,e.points,function(t){return t}]}var qi=null,Zi=/\s*(\w+)\(([^)]*)\)/g;function Ki(t){return function(e){var n=0;return t.map((function(t){return t===qi?e[n++]:t}))}}function Qi(t){return t}var $i={matrix:["NNNNNN",[qi,qi,0,0,qi,qi,0,0,0,0,1,0,qi,qi,0,1],Qi],matrix3d:["NNNNNNNNNNNNNNNN",Qi],rotate:["A"],rotateX:["A"],rotateY:["A"],rotateZ:["A"],rotate3d:["NNNA"],perspective:["L"],scale:["Nn",Ki([qi,qi,new zr(1)]),Qi],scaleX:["N",Ki([qi,new zr(1),new zr(1)]),Ki([qi,new zr(1)])],scaleY:["N",Ki([new zr(1),qi,new zr(1)]),Ki([new zr(1),qi])],scaleZ:["N",Ki([new zr(1),new zr(1),qi])],scale3d:["NNN",Qi],skew:["Aa",null,Qi],skewX:["A",null,Ki([qi,Xr])],skewY:["A",null,Ki([Xr,qi])],translate:["Tt",Ki([qi,qi,jr]),Qi],translateX:["T",Ki([qi,jr,jr]),Ki([qi,jr])],translateY:["T",Ki([jr,qi,jr]),Ki([jr,qi])],translateZ:["L",Ki([jr,jr,qi])],translate3d:["TTL",Qi]};function Ji(t){for(var e=[],n=t.length,r=0;n>r;r++){var i=t[r],a=i[0],o=i.slice(1);if("translate"===a||"skew"===a?1===o.length&&o.push(0):"scale"===a&&1===o.length&&o.push(o[0]),!$i[a])return[];var s=o.map((function(t){return $r(t)}));e.push({t:a,d:s})}return e}function ta(t){if(Array.isArray(t))return Ji(t);if("none"===(t=(t||"none").trim()))return[];var e,n=[],r=0;for(Zi.lastIndex=0;e=Zi.exec(t);){if(e.index!==r)return[];r=e.index+e[0].length;var i=e[1],a=$i[i];if(!a)return[];var o=e[2].split(","),s=a[0];if(o.length>s.length)return[];for(var u=[],l=0;s.length>l;l++){var c=o[l],h=s[l],f=void 0;if(void 0===(f=c?{A:function(t){return"0"===t.trim()?Xr:ki(t)},N:Pi,T:yi,L:pi}[h.toUpperCase()](c):{a:Xr,n:u[0],t:jr}[h]))return[];u.push(f)}if(n.push({t:i,d:u}),Zi.lastIndex===t.length)return n}return[]}function ea(t){if(Array.isArray(t))return Ji(t);if("none"===(t=(t||"none").trim()))return[];var e,n=[],r=0;for(Zi.lastIndex=0;e=Zi.exec(t);){if(e.index!==r)return[];r=e.index+e[0].length;var i=e[1],a=$i[i];if(!a)return[];var o=e[2].split(","),s=a[0];if(o.length>s.length)return[];for(var u=[],l=0;s.length>l;l++){var c=o[l],h=s[l],f=void 0;if(void 0===(f=c?{A:function(t){return"0"===t.trim()?Xr:gi(t)},N:Ni,T:mi,L:vi}[h.toUpperCase()](c):{a:Xr,n:u[0],t:jr}[h]))return[];u.push(f)}if(n.push({t:i,d:u}),Zi.lastIndex===t.length)return n}return[]}function na(t){var e,n,r,i;switch(t.t){case"rotateX":return i=Mn(xi(t.d[0])),[1,0,0,0,0,Math.cos(i),Math.sin(i),0,0,-Math.sin(i),Math.cos(i),0,0,0,0,1];case"rotateY":return i=Mn(xi(t.d[0])),[Math.cos(i),0,-Math.sin(i),0,0,1,0,0,Math.sin(i),0,Math.cos(i),0,0,0,0,1];case"rotate":case"rotateZ":return i=Mn(xi(t.d[0])),[Math.cos(i),Math.sin(i),0,0,-Math.sin(i),Math.cos(i),0,0,0,0,1,0,0,0,0,1];case"rotate3d":e=t.d[0].value,n=t.d[1].value,r=t.d[2].value,i=Mn(xi(t.d[3]));var a=e*e+n*n+r*r;if(0===a)e=1,n=0,r=0;else if(1!==a){var o=Math.sqrt(a);e/=o,n/=o,r/=o}var s=Math.sin(i/2),u=s*Math.cos(i/2),l=s*s;return[1-2*(n*n+r*r)*l,2*(e*n*l+r*u),2*(e*r*l-n*u),0,2*(e*n*l-r*u),1-2*(e*e+r*r)*l,2*(n*r*l+e*u),0,2*(e*r*l+n*u),2*(n*r*l-e*u),1-2*(e*e+n*n)*l,0,0,0,0,1];case"scale":return[t.d[0].value,0,0,0,0,t.d[1].value,0,0,0,0,1,0,0,0,0,1];case"scaleX":return[t.d[0].value,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1];case"scaleY":return[1,0,0,0,0,t.d[0].value,0,0,0,0,1,0,0,0,0,1];case"scaleZ":return[1,0,0,0,0,1,0,0,0,0,t.d[0].value,0,0,0,0,1];case"scale3d":return[t.d[0].value,0,0,0,0,t.d[1].value,0,0,0,0,t.d[2].value,0,0,0,0,1];case"skew":var c=Mn(xi(t.d[0])),h=Mn(xi(t.d[1]));return[1,Math.tan(h),0,0,Math.tan(c),1,0,0,0,0,1,0,0,0,0,1];case"skewX":return i=Mn(xi(t.d[0])),[1,0,0,0,Math.tan(i),1,0,0,0,0,1,0,0,0,0,1];case"skewY":return i=Mn(xi(t.d[0])),[1,Math.tan(i),0,0,0,1,0,0,0,0,1,0,0,0,0,1];case"translate":return[1,0,0,0,0,1,0,0,0,0,1,0,e=Ti(t.d[0],0,null)||0,n=Ti(t.d[1],0,null)||0,0,1];case"translateX":return[1,0,0,0,0,1,0,0,0,0,1,0,e=Ti(t.d[0],0,null)||0,0,0,1];case"translateY":return[1,0,0,0,0,1,0,0,0,0,1,0,0,n=Ti(t.d[0],0,null)||0,0,1];case"translateZ":return[1,0,0,0,0,1,0,0,0,0,1,0,0,0,r=Ti(t.d[0],0,null)||0,1];case"translate3d":return[1,0,0,0,0,1,0,0,0,0,1,0,e=Ti(t.d[0],0,null)||0,n=Ti(t.d[1],0,null)||0,r=Ti(t.d[2],0,null)||0,1];case"perspective":var f=Ti(t.d[0],0,null)||0;return[1,0,0,0,0,1,0,0,0,0,1,f?-1/f:0,0,0,0,1];case"matrix":return[t.d[0].value,t.d[1].value,0,0,t.d[2].value,t.d[3].value,0,0,0,0,1,0,t.d[4].value,t.d[5].value,0,1];case"matrix3d":return t.d.map((function(t){return t.value}))}}function ra(t,e){return[t[0]*e[0]+t[4]*e[1]+t[8]*e[2]+t[12]*e[3],t[1]*e[0]+t[5]*e[1]+t[9]*e[2]+t[13]*e[3],t[2]*e[0]+t[6]*e[1]+t[10]*e[2]+t[14]*e[3],t[3]*e[0]+t[7]*e[1]+t[11]*e[2]+t[15]*e[3],t[0]*e[4]+t[4]*e[5]+t[8]*e[6]+t[12]*e[7],t[1]*e[4]+t[5]*e[5]+t[9]*e[6]+t[13]*e[7],t[2]*e[4]+t[6]*e[5]+t[10]*e[6]+t[14]*e[7],t[3]*e[4]+t[7]*e[5]+t[11]*e[6]+t[15]*e[7],t[0]*e[8]+t[4]*e[9]+t[8]*e[10]+t[12]*e[11],t[1]*e[8]+t[5]*e[9]+t[9]*e[10]+t[13]*e[11],t[2]*e[8]+t[6]*e[9]+t[10]*e[10]+t[14]*e[11],t[3]*e[8]+t[7]*e[9]+t[11]*e[10]+t[15]*e[11],t[0]*e[12]+t[4]*e[13]+t[8]*e[14]+t[12]*e[15],t[1]*e[12]+t[5]*e[13]+t[9]*e[14]+t[13]*e[15],t[2]*e[12]+t[6]*e[13]+t[10]*e[14]+t[14]*e[15],t[3]*e[12]+t[7]*e[13]+t[11]*e[14]+t[15]*e[15]]}function ia(t){var e=[0,0,0],n=[1,1,1],r=[0,0,0],i=[0,0,0,1],a=[0,0,0,1];return function(t,e,n,r,i,a){if(!function(t,e){var n=e[15];if(0===n)return!1;for(var r=1/n,i=0;16>i;i++)t[i]=e[i]*r;return!0}(On,t))return!1;if(N(Ln,On),Ln[3]=0,Ln[7]=0,Ln[11]=0,Ln[15]=1,1e-8>Math.abs(R(Ln)))return!1;var o,s,u=On[3],l=On[7],c=On[11],h=On[12],f=On[13],d=On[14],v=On[15];if(0!==u||0!==l||0!==c){if(In[0]=u,In[1]=l,In[2]=c,In[3]=v,!C(Ln,Ln))return!1;A(Ln,Ln),bt(i,In,Ln)}else i[0]=i[1]=i[2]=0,i[3]=1;if(e[0]=h,e[1]=f,e[2]=d,(o=_n)[0][0]=(s=On)[0],o[0][1]=s[1],o[0][2]=s[2],o[1][0]=s[4],o[1][1]=s[5],o[1][2]=s[6],o[2][0]=s[8],o[2][1]=s[9],o[2][2]=s[10],n[0]=it(_n[0]),ht(_n[0],_n[0]),r[0]=ft(_n[0],_n[1]),Fn(_n[1],_n[1],_n[0],1,-r[0]),n[1]=it(_n[1]),ht(_n[1],_n[1]),r[0]/=n[1],r[1]=ft(_n[0],_n[2]),Fn(_n[2],_n[2],_n[0],1,-r[1]),r[2]=ft(_n[1],_n[2]),Fn(_n[2],_n[2],_n[1],1,-r[2]),n[2]=it(_n[2]),ht(_n[2],_n[2]),r[1]/=n[2],r[2]/=n[2],dt(Dn,_n[1],_n[2]),0>ft(_n[0],Dn))for(var p=0;3>p;p++)n[p]*=-1,_n[p][0]*=-1,_n[p][1]*=-1,_n[p][2]*=-1;a[0]=.5*Math.sqrt(Math.max(1+_n[0][0]-_n[1][1]-_n[2][2],0)),a[1]=.5*Math.sqrt(Math.max(1-_n[0][0]+_n[1][1]-_n[2][2],0)),a[2]=.5*Math.sqrt(Math.max(1-_n[0][0]-_n[1][1]+_n[2][2],0)),a[3]=.5*Math.sqrt(Math.max(1+_n[0][0]+_n[1][1]+_n[2][2],0)),_n[2][1]>_n[1][2]&&(a[0]=-a[0]),_n[0][2]>_n[2][0]&&(a[1]=-a[1]),_n[1][0]>_n[0][1]&&(a[2]=-a[2])}(function(t){return 0===t.length?[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1]:t.map(na).reduce(ra)}(t),e,n,r,i,a),[[e,n,r,a,i]]}var aa=function(){function t(t,e){for(var n=[[0,0,0,0],[0,0,0,0],[0,0,0,0],[0,0,0,0]],r=0;4>r;r++)for(var i=0;4>i;i++)for(var a=0;4>a;a++)n[r][i]+=e[r][a]*t[a][i];return n}return function(e,n,r,i,a){for(var o=[[1,0,0,0],[0,1,0,0],[0,0,1,0],[0,0,0,1]],s=0;4>s;s++)o[s][3]=a[s];for(var u=0;3>u;u++)for(var l=0;3>l;l++)o[3][u]+=e[l]*o[l][u];var c=i[0],h=i[1],f=i[2],d=i[3],v=[[1,0,0,0],[0,1,0,0],[0,0,1,0],[0,0,0,1]];v[0][0]=1-2*(h*h+f*f),v[0][1]=2*(c*h-f*d),v[0][2]=2*(c*f+h*d),v[1][0]=2*(c*h+f*d),v[1][1]=1-2*(c*c+f*f),v[1][2]=2*(h*f-c*d),v[2][0]=2*(c*f-h*d),v[2][1]=2*(h*f+c*d),v[2][2]=1-2*(c*c+h*h),o=t(o,v);var p,m=[[1,0,0,0],[0,1,0,0],[0,0,1,0],[0,0,0,1]];r[2]&&(m[2][1]=r[2],o=t(o,m)),r[1]&&(m[2][1]=0,m[2][0]=r[0],o=t(o,m)),r[0]&&(m[2][0]=0,m[1][0]=r[0],o=t(o,m));for(var y=0;3>y;y++)for(var g=0;3>g;g++)o[y][g]*=n[y];return 0===(p=o)[0][2]&&0===p[0][3]&&0===p[1][2]&&0===p[1][3]&&0===p[2][0]&&0===p[2][1]&&1===p[2][2]&&0===p[2][3]&&0===p[3][2]&&1===p[3][3]?[o[0][0],o[0][1],o[1][0],o[1][1],o[3][0],o[3][1]]:o[0].concat(o[1],o[2],o[3])}}();function oa(t){return t.toFixed(6).replace(".000000","")}function sa(t,e){var n,r;return t.decompositionPair!==e&&(t.decompositionPair=e,n=ia(t)),e.decompositionPair!==t&&(e.decompositionPair=t,r=ia(e)),null===n[0]||null===r[0]?[[!1],[!0],function(n){return n?e[0].d:t[0].d}]:(n[0].push(0),r[0].push(1),[n,r,function(t){var e=function(t,e,n){var r=function(t,e){for(var n=0,r=0;t.length>r;r++)n+=t[r]*e[r];return n}(t,e);r=Ut(r,-1,1);var i=[];if(1===r)i=t;else for(var a=Math.acos(r),o=1*Math.sin(n*a)/Math.sqrt(1-r*r),s=0;4>s;s++)i.push(t[s]*(Math.cos(n*a)-r*o)+e[s]*o);return i}(n[0][3],r[0][3],t[5]);return aa(t[0],t[1],t[2],e,t[4]).map(oa).join(",")}])}function ua(t){return t.replace(/[XY]/,"")}function la(t){return t.replace(/(X|Y|Z|3d)?$/,"3d")}function ca(t,e,n){var r=!1;if(!t.length||!e.length){t.length||(r=!0,t=e,e=[]);for(var i=function(){var n=t[a],r=n.t,i=n.d,o="scale"===r.substring(0,5)?1:0;e.push({t:r,d:i.map((function(t){return"number"==typeof t?$r(o):$r(o,t.unit)}))})},a=0;t.length>a;a++)i()}var o,s,u=[],l=[],c=[];if(t.length!==e.length){var h=sa(t,e);u=[h[0]],l=[h[1]],c=[["matrix",[h[2]]]]}else for(var f=0;t.length>f;f++){var d=t[f].t,v=e[f].t,p=t[f].d,m=e[f].d,y=$i[d],g=$i[v],k=void 0;if(s=v,"perspective"===(o=d)&&"perspective"===s||!("matrix"!==o&&"matrix3d"!==o||"matrix"!==s&&"matrix3d"!==s)){var E=sa([t[f]],[e[f]]);u.push(E[0]),l.push(E[1]),c.push(["matrix",[E[2]]])}else{if(d===v)k=d;else if(y[2]&&g[2]&&ua(d)===ua(v))k=ua(d),p=y[2](p),m=g[2](m);else{if(!y[1]||!g[1]||la(d)!==la(v)){var x=sa(t,e);u=[x[0]],l=[x[1]],c=[["matrix",[x[2]]]];break}k=la(d),p=y[1](p),m=g[1](m)}for(var b=[],T=[],M=[],w=0;p.length>w;w++){var N=Ei(p[w],m[w],n,!1,w);b[w]=N[0],T[w]=N[1],M.push(N[2])}u.push(b),l.push(T),c.push([k,M])}}if(r){var P=u;u=l,l=P}return[u,l,function(t){return t.map((function(t,e){var n=t.map((function(t,n){return c[e][1][n](t)})).join(",");return"matrix"===c[e][0]&&16===n.split(",").length&&(c[e][0]="matrix3d"),"matrix3d"===c[e][0]&&6===n.split(",").length&&(c[e][0]="matrix"),"".concat(c[e][0],"(").concat(n,")")})).join(" ")}]}var ha=Pr((function(t){if(Yt(t)){if("text-anchor"===t)return[$r(0,"px"),$r(0,"px")];var e=t.split(" ");return 1===e.length&&("top"===e[0]||"bottom"===e[0]?(e[1]=e[0],e[0]="center"):e[1]="center"),2!==e.length?null:[yi(fa(e[0])),yi(fa(e[1]))]}return[$r(t[0]||0,"px"),$r(t[1]||0,"px")]}));function fa(t){return"center"===t?"50%":"left"===t||"top"===t?"0%":"right"===t||"bottom"===t?"100%":t}var da=[{n:"display",k:["none"]},{n:"opacity",int:!0,inh:!0,d:"1",syntax:Wn.OPACITY_VALUE},{n:"fillOpacity",int:!0,inh:!0,d:"1",syntax:Wn.OPACITY_VALUE},{n:"strokeOpacity",int:!0,inh:!0,d:"1",syntax:Wn.OPACITY_VALUE},{n:"fill",int:!0,k:["none"],d:"none",syntax:Wn.PAINT},{n:"fillRule",k:["nonzero","evenodd"],d:"nonzero"},{n:"stroke",int:!0,k:["none"],d:"none",syntax:Wn.PAINT,l:!0},{n:"shadowType",k:["inner","outer","both"],d:"outer",l:!0},{n:"shadowColor",int:!0,syntax:Wn.COLOR},{n:"shadowOffsetX",int:!0,l:!0,d:"0",syntax:Wn.LENGTH_PERCENTAGE},{n:"shadowOffsetY",int:!0,l:!0,d:"0",syntax:Wn.LENGTH_PERCENTAGE},{n:"shadowBlur",int:!0,l:!0,d:"0",syntax:Wn.SHADOW_BLUR},{n:"lineWidth",int:!0,inh:!0,d:"1",l:!0,a:["strokeWidth"],syntax:Wn.LENGTH_PERCENTAGE},{n:"increasedLineWidthForHitTesting",inh:!0,d:"0",l:!0,syntax:Wn.LENGTH_PERCENTAGE},{n:"lineJoin",inh:!0,l:!0,a:["strokeLinejoin"],k:["miter","bevel","round"],d:"miter"},{n:"lineCap",inh:!0,l:!0,a:["strokeLinecap"],k:["butt","round","square"],d:"butt"},{n:"lineDash",int:!0,inh:!0,k:["none"],a:["strokeDasharray"],syntax:Wn.LENGTH_PERCENTAGE_12},{n:"lineDashOffset",int:!0,inh:!0,d:"0",a:["strokeDashoffset"],syntax:Wn.LENGTH_PERCENTAGE},{n:"offsetPath",syntax:Wn.DEFINED_PATH},{n:"offsetDistance",int:!0,syntax:Wn.OFFSET_DISTANCE},{n:"dx",int:!0,l:!0,d:"0",syntax:Wn.LENGTH_PERCENTAGE},{n:"dy",int:!0,l:!0,d:"0",syntax:Wn.LENGTH_PERCENTAGE},{n:"zIndex",ind:!0,int:!0,d:"0",k:["auto"],syntax:Wn.Z_INDEX},{n:"visibility",k:["visible","hidden"],ind:!0,inh:!0,int:!0,d:"visible"},{n:"pointerEvents",inh:!0,k:["none","auto","stroke","fill","painted","visible","visiblestroke","visiblefill","visiblepainted","all"],d:"auto"},{n:"filter",ind:!0,l:!0,k:["none"],d:"none",syntax:Wn.FILTER},{n:"clipPath",syntax:Wn.DEFINED_PATH},{n:"textPath",syntax:Wn.DEFINED_PATH},{n:"textPathSide",k:["left","right"],d:"left"},{n:"textPathStartOffset",l:!0,d:"0",syntax:Wn.LENGTH_PERCENTAGE},{n:"transform",p:100,int:!0,k:["none"],d:"none",syntax:Wn.TRANSFORM},{n:"transformOrigin",p:100,d:"0 0",l:!0,syntax:Wn.TRANSFORM_ORIGIN},{n:"cx",int:!0,l:!0,d:"0",syntax:Wn.COORDINATE},{n:"cy",int:!0,l:!0,d:"0",syntax:Wn.COORDINATE},{n:"cz",int:!0,l:!0,d:"0",syntax:Wn.COORDINATE},{n:"r",int:!0,l:!0,d:"0",syntax:Wn.LENGTH_PERCENTAGE},{n:"rx",int:!0,l:!0,d:"0",syntax:Wn.LENGTH_PERCENTAGE},{n:"ry",int:!0,l:!0,d:"0",syntax:Wn.LENGTH_PERCENTAGE},{n:"x",int:!0,l:!0,d:"0",syntax:Wn.COORDINATE},{n:"y",int:!0,l:!0,d:"0",syntax:Wn.COORDINATE},{n:"z",int:!0,l:!0,d:"0",syntax:Wn.COORDINATE},{n:"width",int:!0,l:!0,k:["auto","fit-content","min-content","max-content"],d:"0",syntax:Wn.LENGTH_PERCENTAGE},{n:"height",int:!0,l:!0,k:["auto","fit-content","min-content","max-content"],d:"0",syntax:Wn.LENGTH_PERCENTAGE},{n:"radius",int:!0,l:!0,d:"0",syntax:Wn.LENGTH_PERCENTAGE_14},{n:"x1",int:!0,l:!0,syntax:Wn.COORDINATE},{n:"y1",int:!0,l:!0,syntax:Wn.COORDINATE},{n:"z1",int:!0,l:!0,syntax:Wn.COORDINATE},{n:"x2",int:!0,l:!0,syntax:Wn.COORDINATE},{n:"y2",int:!0,l:!0,syntax:Wn.COORDINATE},{n:"z2",int:!0,l:!0,syntax:Wn.COORDINATE},{n:"d",int:!0,l:!0,d:"",syntax:Wn.PATH,p:50},{n:"points",int:!0,l:!0,syntax:Wn.LIST_OF_POINTS,p:50},{n:"text",l:!0,d:"",syntax:Wn.TEXT,p:50},{n:"textTransform",l:!0,inh:!0,k:["capitalize","uppercase","lowercase","none"],d:"none",syntax:Wn.TEXT_TRANSFORM,p:51},{n:"font",l:!0},{n:"fontSize",int:!0,inh:!0,d:"16px",l:!0,syntax:Wn.LENGTH_PERCENTAGE},{n:"fontFamily",l:!0,inh:!0,d:"sans-serif"},{n:"fontStyle",l:!0,inh:!0,k:["normal","italic","oblique"],d:"normal"},{n:"fontWeight",l:!0,inh:!0,k:["normal","bold","bolder","lighter"],d:"normal"},{n:"fontVariant",l:!0,inh:!0,k:["normal","small-caps"],d:"normal"},{n:"lineHeight",l:!0,syntax:Wn.LENGTH,int:!0,d:"0"},{n:"letterSpacing",l:!0,syntax:Wn.LENGTH,int:!0,d:"0"},{n:"miterLimit",l:!0,syntax:Wn.NUMBER,d:function(t){return t===en.PATH||t===en.POLYGON||t===en.POLYLINE?"4":"10"}},{n:"wordWrap",l:!0},{n:"wordWrapWidth",l:!0},{n:"maxLines",l:!0},{n:"textOverflow",l:!0,d:"clip"},{n:"leading",l:!0},{n:"textBaseline",l:!0,inh:!0,k:["top","hanging","middle","alphabetic","ideographic","bottom"],d:"alphabetic"},{n:"textAlign",l:!0,inh:!0,k:["start","center","middle","end","left","right"],d:"start"},{n:"markerStart",syntax:Wn.MARKER},{n:"markerEnd",syntax:Wn.MARKER},{n:"markerMid",syntax:Wn.MARKER},{n:"markerStartOffset",syntax:Wn.LENGTH,l:!0,int:!0,d:"0"},{n:"markerEndOffset",syntax:Wn.LENGTH,l:!0,int:!0,d:"0"}],va=new Set(da.filter((function(t){return!!t.l})).map((function(t){return t.n}))),pa={},ma=function(){return u((function t(e){var n=this;o(this,t),this.runtime=e,da.forEach((function(t){n.registerMetadata(t)}))}),[{key:"registerMetadata",value:function(t){[t.n].concat(h(t.a||[])).forEach((function(e){pa[e]=t}))}},{key:"getPropertySyntax",value:function(t){return this.runtime.CSSPropertySyntaxFactory[t]}},{key:"processProperties",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{skipUpdateAttribute:!1,skipParse:!1,forceUpdateGeometry:!1,usedAttributes:[],memoize:!0};Object.assign(t.attributes,e);var r=t.parsedStyle.clipPath,i=t.parsedStyle.offsetPath;!function(t,e){var n=ya(t);for(var r in e)n.has(r)&&(t.parsedStyle[r]=e[r])}(t,e);var o=!!n.forceUpdateGeometry;if(!o)for(var s in e)if(va.has(s)){o=!0;break}var u,l=ya(t);l.has("fill")&&e.fill&&(t.parsedStyle.fill=hi(e.fill)),l.has("stroke")&&e.stroke&&(t.parsedStyle.stroke=hi(e.stroke)),l.has("shadowColor")&&e.shadowColor&&(t.parsedStyle.shadowColor=hi(e.shadowColor)),l.has("filter")&&e.filter&&(t.parsedStyle.filter=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if("none"===(t=t.toLowerCase().trim()))return[];for(var e,n=/\s*([\w-]+)\(([^)]*)\)/g,r=[],i=0;e=n.exec(t);){if(e.index!==i)return[];if(i=e.index+e[0].length,Mi.indexOf(e[1])>-1&&r.push({name:e[1],params:e[2].split(" ").map((function(t){return di(/deg|rad|grad|turn|px|%/g,t)||hi(t)}))}),n.lastIndex===t.length)return r}return[]}(e.filter)),l.has("radius")&&!_t(e.radius)&&(t.parsedStyle.radius=bi(e.radius,4)),l.has("lineDash")&&!_t(e.lineDash)&&(t.parsedStyle.lineDash=bi(e.lineDash,"even")),l.has("points")&&e.points&&(t.parsedStyle.points={points:Yt(u=e.points)?u.split(" ").map((function(t){var e=g(t.split(","),2),n=e[1];return[Number(e[0]),Number(n)]})):u,totalLength:0,segments:[]}),l.has("d")&&""===e.d&&(t.parsedStyle.d=a({},Hn)),l.has("d")&&e.d&&(t.parsedStyle.d=Xi(e.d)),l.has("textTransform")&&e.textTransform&&this.runtime.CSSPropertySyntaxFactory[Wn.TEXT_TRANSFORM].calculator(null,null,{value:e.textTransform},t,null),l.has("clipPath")&&!Wt(e.clipPath)&&this.runtime.CSSPropertySyntaxFactory[Wn.DEFINED_PATH].calculator("clipPath",r,e.clipPath,t,this.runtime),l.has("offsetPath")&&e.offsetPath&&this.runtime.CSSPropertySyntaxFactory[Wn.DEFINED_PATH].calculator("offsetPath",i,e.offsetPath,t,this.runtime),l.has("transform")&&e.transform&&(t.parsedStyle.transform=ta(e.transform)),l.has("transformOrigin")&&e.transformOrigin&&(t.parsedStyle.transformOrigin=ha(e.transformOrigin)),l.has("markerStart")&&e.markerStart&&(t.parsedStyle.markerStart=this.runtime.CSSPropertySyntaxFactory[Wn.MARKER].calculator(null,e.markerStart,e.markerStart,null,null)),l.has("markerEnd")&&e.markerEnd&&(t.parsedStyle.markerEnd=this.runtime.CSSPropertySyntaxFactory[Wn.MARKER].calculator(null,e.markerEnd,e.markerEnd,null,null)),l.has("markerMid")&&e.markerMid&&(t.parsedStyle.markerMid=this.runtime.CSSPropertySyntaxFactory[Wn.MARKER].calculator("",e.markerMid,e.markerMid,null,null)),l.has("zIndex")&&!_t(e.zIndex)&&this.runtime.CSSPropertySyntaxFactory[Wn.Z_INDEX].postProcessor(t),l.has("offsetDistance")&&!_t(e.offsetDistance)&&this.runtime.CSSPropertySyntaxFactory[Wn.OFFSET_DISTANCE].postProcessor(t),l.has("transform")&&e.transform&&this.runtime.CSSPropertySyntaxFactory[Wn.TRANSFORM].postProcessor(t),l.has("transformOrigin")&&e.transformOrigin&&this.runtime.CSSPropertySyntaxFactory[Wn.TRANSFORM_ORIGIN].postProcessor(t),o&&(t.geometry.dirty=!0,t.renderable.boundsDirty=!0,t.renderable.renderBoundsDirty=!0,n.forceUpdateGeometry||this.runtime.sceneGraphService.dirtifyToRoot(t))}},{key:"updateGeometry",value:function(t){var e=t.nodeName,n=this.runtime.geometryUpdaterFactory[e];if(n){var r=t.geometry;r.contentBounds||(r.contentBounds=new fn),r.renderBounds||(r.renderBounds=new fn);var i=t.parsedStyle,a=n.update(i,t),o=a.cx,s=void 0===o?0:o,u=a.cy,l=void 0===u?0:u,c=a.cz,h=void 0===c?0:c,f=a.hwidth,d=void 0===f?0:f,v=a.hheight,p=void 0===v?0:v,m=a.hdepth,y=void 0===m?0:m,g=[Math.abs(d),Math.abs(p),y],k=i.stroke,E=i.lineWidth,x=void 0===E?1:E,b=i.increasedLineWidthForHitTesting,T=void 0===b?0:b,M=i.shadowType,w=void 0===M?"outer":M,N=i.shadowColor,P=i.filter,S=void 0===P?[]:P,A=i.transformOrigin,C=[s,l,h];if(r.contentBounds.update(C,g),k&&!k.isNone){var R=((x||0)+(T||0))*(e===en.POLYLINE||e===en.POLYGON||e===en.PATH?Math.SQRT2:.5);g[0]+=R,g[1]+=R}if(r.renderBounds.update(C,g),N&&w&&"inner"!==w){var O=r.renderBounds,L=O.min,I=O.max,_=i.shadowBlur||0,D=i.shadowOffsetX||0,F=i.shadowOffsetY||0,G=I[0]+_+D,B=L[1]-_+F,V=I[1]+_+F;L[0]=Math.min(L[0],L[0]-_+D),I[0]=Math.max(I[0],G),L[1]=Math.min(L[1],B),I[1]=Math.max(I[1],V),r.renderBounds.setMinMax(L,I)}S.forEach((function(t){var e=t.name,n=t.params;if("blur"===e){var i=n[0].value;r.renderBounds.update(r.renderBounds.center,ut(r.renderBounds.halfExtents,r.renderBounds.halfExtents,[i,i,0]))}else if("drop-shadow"===e){var a=n[0].value,o=n[1].value,s=n[2].value,u=r.renderBounds,l=u.min,c=u.max,h=c[0]+s+a,f=l[1]-s+o,d=c[1]+s+o;l[0]=Math.min(l[0],l[0]-s+a),c[0]=Math.max(c[0],h),l[1]=Math.min(l[1],f),c[1]=Math.max(c[1],d),r.renderBounds.setMinMax(l,c)}})),t.geometry.dirty=!1;var Y=0>p,U=(0>d?-1:1)*(A?Ti(A[0],0,t,!0):0),z=(Y?-1:1)*(A?Ti(A[1],1,t,!0):0);(U||z)&&t.setOrigin(U,z)}}},{key:"updateSizeAttenuation",value:function(t,e){t.style.isSizeAttenuation?(t.style.rawLineWidth||(t.style.rawLineWidth=t.style.lineWidth),t.style.lineWidth=(t.style.rawLineWidth||1)/e,t.nodeName===en.CIRCLE&&(t.style.rawR||(t.style.rawR=t.style.r),t.style.r=(t.style.rawR||1)/e)):(t.style.rawLineWidth&&(t.style.lineWidth=t.style.rawLineWidth,delete t.style.rawLineWidth),t.nodeName===en.CIRCLE&&t.style.rawR&&(t.style.r=t.style.rawR,delete t.style.rawR))}}])}();function ya(t){return t.constructor.PARSED_STYLE_LIST}var ga=function(){return u((function t(){o(this,t),this.mixer=Si}),[{key:"calculator",value:function(t,e,n,r){return xi(n)}}])}(),ka=function(){return u((function t(){o(this,t)}),[{key:"calculator",value:function(t,e,n,r,i){return n instanceof Yr&&(n=null),i.sceneGraphService.updateDisplayObjectDependency(t,e,n,r),"clipPath"===t&&r.forEach((function(t){0===t.childNodes.length&&i.sceneGraphService.dirtifyToRoot(t)})),n}}])}(),Ea=function(){return u((function t(){o(this,t),this.parser=hi,this.mixer=fi}),[{key:"calculator",value:function(t,e,n,r){return n instanceof Yr?"none"===n.value?Zr:Kr:n}}])}(),xa=function(){return u((function t(){o(this,t)}),[{key:"calculator",value:function(t,e,n){return n instanceof Yr?[]:n}}])}();function ba(t){var e=t.parsedStyle.fontSize;return _t(e)?null:e}var Ta=function(){return u((function t(){o(this,t),this.mixer=Si}),[{key:"calculator",value:function(t,e,n,r,i){if(zt(n))return n;if(!zr.isRelativeUnit(n.unit))return n.value;if(n.unit===Sr.kPercentage)return 0;if(n.unit===Sr.kEms){if(r.parentNode){var a=ba(r.parentNode);if(a)return a*=n.value}return 0}if(n.unit===Sr.kRems){var o;if(null!=r&&null!==(o=r.ownerDocument)&&void 0!==o&&o.documentElement){var s=ba(r.ownerDocument.documentElement);if(s)return s*=n.value}return 0}}}])}(),Ma=function(){return u((function t(){o(this,t),this.mixer=Ci}),[{key:"calculator",value:function(t,e,n){return n.map((function(t){return t.value}))}}])}(),wa=function(){return u((function t(){o(this,t),this.mixer=Ci}),[{key:"calculator",value:function(t,e,n){return n.map((function(t){return t.value}))}}])}(),Na=function(){return u((function t(){o(this,t)}),[{key:"calculator",value:function(t,e,n,r){var i;n instanceof Yr&&(n=null);var a=null===(i=n)||void 0===i?void 0:i.cloneNode(!0);return a&&(a.style.isMarker=!0),a}}])}(),Pa=function(){return u((function t(){o(this,t),this.mixer=Si}),[{key:"calculator",value:function(t,e,n){return n.value}}])}(),Sa=function(){return u((function t(){o(this,t),this.mixer=Ai(0,1)}),[{key:"calculator",value:function(t,e,n){return n.value}},{key:"postProcessor",value:function(t){var e=t.parsedStyle,n=e.offsetPath;if(n){var r=n.nodeName;if(r===en.LINE||r===en.PATH||r===en.POLYLINE){var i=n.getPoint(e.offsetDistance);i&&t.setLocalPosition(i.x,i.y)}}}}])}(),Aa=function(){return u((function t(){o(this,t),this.mixer=Ai(0,1)}),[{key:"calculator",value:function(t,e,n){return n.value}}])}(),Ca=function(){return u((function t(){o(this,t),this.parser=Xi,this.mixer=Hi}),[{key:"calculator",value:function(t,e,n){return n instanceof Yr&&"unset"===n.value?{absolutePath:[],hasArc:!1,segments:[],polygons:[],polylines:[],curve:null,totalLength:0,rect:new yn(0,0,0,0)}:n}}])}(),Ra=u((function t(){o(this,t),this.mixer=Wi})),Oa=function(t){function e(){var t;o(this,e);for(var n=arguments.length,r=Array(n),i=0;n>i;i++)r[i]=arguments[i];return(t=p(this,e,[].concat(r))).mixer=Ai(0,1/0),t}return y(e,t),u(e)}(Ta),La=function(){return u((function t(){o(this,t)}),[{key:"calculator",value:function(t,e,n,r){return n instanceof Yr?"unset"===n.value?"":n.value:"".concat(n)}},{key:"postProcessor",value:function(t){t.nodeValue="".concat(t.parsedStyle.text)||""}}])}(),Ia=function(){return u((function t(){o(this,t)}),[{key:"calculator",value:function(t,e,n,r){var i=r.getAttribute("text");if(i){var a=i;"capitalize"===n.value?a=i.charAt(0).toUpperCase()+i.slice(1):"lowercase"===n.value?a=i.toLowerCase():"uppercase"===n.value&&(a=i.toUpperCase()),r.parsedStyle.text=a}return n.value}}])}(),_a=new WeakMap;var Da="undefined"!=typeof window&&void 0!==window.document;function Fa(t){return!!t.getAttribute}function Ga(t,e){var n=Number(t.parsedStyle.zIndex||0),r=Number(e.parsedStyle.zIndex||0);if(n===r){var i=t.parentNode;if(i){var a=i.childNodes||[];return a.indexOf(t)-a.indexOf(e)}}return n-r}function Ba(t){var e=t;do{var n;if(null===(n=e.parsedStyle)||void 0===n?void 0:n.clipPath)return e;e=e.parentElement}while(null!==e);return null}function Va(t,e){if(Da)return document.defaultView.getComputedStyle(t,null).getPropertyValue(e)}var Ya={touchstart:"pointerdown",touchend:"pointerup",touchendoutside:"pointerupoutside",touchmove:"pointermove",touchcancel:"pointercancel"},Ua="object"==typeof performance&&performance.now?performance:Date;function za(t){return t.nodeName===en.FRAGMENT||t.getRootNode().nodeName===en.FRAGMENT}var ja=1,Xa="object"==typeof self&&self.self===self?self:"object"==typeof global&&global.global===global?global:{},Ha=Date.now(),Wa={},qa=Date.now(),Za=function(t){if("function"!=typeof t)throw new TypeError("".concat(t," is not a function"));var e=Date.now(),n=e-qa,r=n>16?0:16-n,i=ja++;return Wa[i]=t,Object.keys(Wa).length>1||setTimeout((function(){qa=e;var t=Wa;Wa={},Object.keys(t).forEach((function(e){return t[e](Xa.performance&&"function"==typeof Xa.performance.now?Xa.performance.now():Date.now()-Ha)}))}),r),i},Ka=function(t){delete Wa[t]},Qa=function(t){return"string"!=typeof t?Za:""===t?Xa.requestAnimationFrame:Xa["".concat(t,"RequestAnimationFrame")]},$a=function(t,e){for(var n=0;void 0!==t[n];){if(e(t[n]))return t[n];n+=1}}(["","webkit","moz","ms","o"],(function(t){return!!Qa(t)})),Ja=Qa($a),to=function(t){return"string"!=typeof t?Ka:""===t?Xa.cancelAnimationFrame:Xa["".concat(t,"CancelAnimationFrame")]||Xa["".concat(t,"CancelRequestAnimationFrame")]}($a);Xa.requestAnimationFrame=Ja,Xa.cancelAnimationFrame=to;var eo=function(){return u((function t(){o(this,t),this.callbacks=[]}),[{key:"getCallbacksNum",value:function(){return this.callbacks.length}},{key:"tapPromise",value:function(t,e){this.callbacks.push(e)}},{key:"promise",value:function(){for(var t=arguments.length,e=Array(t),n=0;t>n;n++)e[n]=arguments[n];return Promise.all(this.callbacks.map((function(t){return t.apply(void 0,e)})))}}])}(),no=function(){return u((function t(){o(this,t),this.callbacks=[]}),[{key:"tapPromise",value:function(t,e){this.callbacks.push(e)}},{key:"promise",value:(t=Ke(qe().mark((function t(){var e,n,r,i,a=arguments;return qe().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!this.callbacks.length){t.next=14;break}return t.next=3,(e=this.callbacks)[0].apply(e,a);case 3:n=t.sent,r=0;case 5:if(r>=this.callbacks.length-1){t.next=13;break}return i=this.callbacks[r],t.next=9,i(n);case 9:n=t.sent;case 10:r++,t.next=5;break;case 13:return t.abrupt("return",n);case 14:return t.abrupt("return",null);case 15:case"end":return t.stop()}}),t,this)}))),function(){return t.apply(this,arguments)})}]);var t}(),ro=function(){return u((function t(){o(this,t),this.callbacks=[]}),[{key:"tap",value:function(t,e){this.callbacks.push(e)}},{key:"call",value:function(){for(var t=arguments.length,e=Array(t),n=0;t>n;n++)e[n]=arguments[n];var r=arguments;this.callbacks.forEach((function(t){t.apply(void 0,r)}))}}])}(),io=function(){return u((function t(){o(this,t),this.callbacks=[]}),[{key:"tap",value:function(t,e){this.callbacks.push(e)}},{key:"call",value:function(){for(var t=arguments.length,e=Array(t),n=0;t>n;n++)e[n]=arguments[n];if(this.callbacks.length){for(var r=this.callbacks[0].apply(void 0,arguments),i=0;this.callbacks.length-1>i;i++){r=(0,this.callbacks[i])(r)}return r}return null}}])}(),ao=["serif","sans-serif","monospace","cursive","fantasy","system-ui"],oo=/([\"\'])[^\'\"]+\1/;function so(t,e,n){return S(t),t[4]=Math.tan(e),t[1]=Math.tan(n),t}var uo=M(),lo=M(),co={scale:function(t){F(uo,[t[0].value,t[1].value,1])},scaleX:function(t){F(uo,[t[0].value,1,1])},scaleY:function(t){F(uo,[1,t[0].value,1])},scaleZ:function(t){F(uo,[1,1,t[0].value])},scale3d:function(t){F(uo,[t[0].value,t[1].value,t[2].value])},translate:function(t){D(uo,[t[0].value,t[1].value,0])},translateX:function(t){D(uo,[t[0].value,0,0])},translateY:function(t){D(uo,[0,t[0].value,0])},translateZ:function(t){D(uo,[0,0,t[0].value])},translate3d:function(t){D(uo,[t[0].value,t[1].value,t[2].value])},rotate:function(t){Y(uo,Mn(xi(t[0])))},rotateX:function(t){B(uo,Mn(xi(t[0])))},rotateY:function(t){V(uo,Mn(xi(t[0])))},rotateZ:function(t){Y(uo,Mn(xi(t[0])))},rotate3d:function(t){G(uo,Mn(xi(t[3])),[t[0].value,t[1].value,t[2].value])},skew:function(t){so(uo,Mn(t[0].value),Mn(t[1].value))},skewX:function(t){so(uo,Mn(t[0].value),0)},skewY:function(t){so(uo,0,Mn(t[0].value))},matrix:function(t){P(uo,t[0].value,t[1].value,0,0,t[2].value,t[3].value,0,0,0,0,1,0,t[4].value,t[5].value,0,1)},matrix3d:function(t){P.apply(et,[uo].concat(h(t.map((function(t){return t.value})))))}},ho=at(1,1,1),fo=nt(),vo={translate:function(t,e){ks.sceneGraphService.setLocalScale(t,ho,!1),ks.sceneGraphService.setLocalEulerAngles(t,fo,void 0,void 0,!1),ks.sceneGraphService.setLocalPosition(t,[e[0].value,e[1].value,0],!1),ks.sceneGraphService.dirtifyLocal(t,t.transformable)}};function po(t,e){if(t.length){if(1===t.length&&vo[t[0].t])return void vo[t[0].t](e,t[0].d);for(var n=S(lo),r=0;t.length>r;r++){var i=t[r],a=co[i.t];a&&(a(i.d),tt(n,n,uo))}e.setLocalTransform(n)}else e.resetLocalTransform();return e.getLocalTransform()}var mo=function(){return u((function t(){o(this,t),this.parser=ea,this.mixer=ca}),[{key:"calculator",value:function(t,e,n,r){return n instanceof Yr?[]:n}},{key:"postProcessor",value:function(t){po(t.parsedStyle.transform,t)}}])}(),yo=function(){return u((function t(){o(this,t)}),[{key:"postProcessor",value:function(t){var e=t.parsedStyle.transformOrigin;e[0].unit===Sr.kPixels&&e[1].unit===Sr.kPixels?t.setOrigin(e[0].value,e[1].value):t.getGeometryBounds()}}])}(),go=function(){return u((function t(){o(this,t)}),[{key:"calculator",value:function(t,e,n,r){return n.value}},{key:"postProcessor",value:function(t){if(t.parentNode){var e=t.parentNode,n=e.renderable,r=e.sortable;n&&(n.dirty=!0),r&&(r.dirty=!0,r.dirtyReason=Xn.Z_INDEX_CHANGED)}}}])}(),ko=function(){return u((function t(){o(this,t)}),[{key:"update",value:function(t,e){var n=t.cx,r=t.cy,i=t.r,a=void 0===i?0:i;return{cx:void 0===n?0:n,cy:void 0===r?0:r,hwidth:a,hheight:a}}}])}(),Eo=function(){return u((function t(){o(this,t)}),[{key:"update",value:function(t,e){var n=t.cx,r=t.cy,i=t.rx,a=t.ry;return{cx:void 0===n?0:n,cy:void 0===r?0:r,hwidth:void 0===i?0:i,hheight:void 0===a?0:a}}}])}(),xo=function(){return u((function t(){o(this,t)}),[{key:"update",value:function(t){var e=t.x1,n=t.y1,r=t.x2,i=t.y2,a=Math.min(e,r),o=Math.max(e,r),s=Math.min(n,i),u=(o-a)/2,l=(Math.max(n,i)-s)/2;return{cx:a+u,cy:s+l,hwidth:u,hheight:l}}}])}(),bo=function(){return u((function t(){o(this,t)}),[{key:"update",value:function(t){var e=t.d.rect,n=e.width/2,r=e.height/2;return{cx:e.x+n,cy:e.y+r,hwidth:n,hheight:r}}}])}(),To=function(){return u((function t(){o(this,t)}),[{key:"update",value:function(t){if(t.points&&Dt(t.points.points)){var e=t.points.points,n=Math.min.apply(Math,h(e.map((function(t){return t[0]})))),r=Math.max.apply(Math,h(e.map((function(t){return t[0]})))),i=Math.min.apply(Math,h(e.map((function(t){return t[1]})))),a=(r-n)/2,o=(Math.max.apply(Math,h(e.map((function(t){return t[1]}))))-i)/2;return{cx:n+a,cy:i+o,hwidth:a,hheight:o}}return{cx:0,cy:0,hwidth:0,hheight:0}}}])}(),Mo=function(){return u((function t(){o(this,t)}),[{key:"update",value:function(t,e){var n=t.x,r=void 0===n?0:n,i=t.y,a=void 0===i?0:i,o=t.src,s=t.width,u=t.height,l=void 0===s?0:s,c=void 0===u?0:u;return o&&!Yt(o)&&(l||(t.width=l=o.width),c||(t.height=c=o.height)),{cx:r+l/2,cy:a+c/2,hwidth:l/2,hheight:c/2}}}])}(),wo=function(){return u((function t(e){o(this,t),this.globalRuntime=e}),[{key:"isReadyToMeasure",value:function(t,e){return t.text}},{key:"update",value:function(t,e){var n,r=t.text,i=t.textAlign,a=void 0===i?"start":i,o=t.lineWidth,s=void 0===o?1:o,u=t.textBaseline,l=void 0===u?"alphabetic":u,c=t.dx,h=void 0===c?0:c,f=t.dy,d=void 0===f?0:f,v=t.x,p=void 0===v?0:v,m=t.y,y=void 0===m?0:m;if(!this.isReadyToMeasure(t,e))return t.metrics={font:"",width:0,height:0,lines:[],lineWidths:[],lineHeight:0,maxLineWidth:0,fontProperties:{ascent:0,descent:0,fontSize:0},lineMetrics:[]},{hwidth:0,hheight:0,cx:0,cy:0};var g=(null==e||null===(n=e.ownerDocument)||void 0===n||null===(n=n.defaultView)||void 0===n?void 0:n.getConfig())||{},k=this.globalRuntime.textService.measureText(r,t,g.offscreenCanvas);t.metrics=k;var E=k.width/2,x=k.height/2,b=p+E;"center"===a||"middle"===a?b+=s/2-E:"right"!==a&&"end"!==a||(b+=s-2*E);var T=y-x;return"middle"===l?T+=x:"top"===l||"hanging"===l?T+=2*x:"alphabetic"===l||"bottom"!==l&&"ideographic"!==l||(T+=0),h&&(b+=h),d&&(T+=d),{cx:b,cy:T,hwidth:E,hheight:x}}}])}(),No=function(){return u((function t(){o(this,t)}),[{key:"update",value:function(t,e){return{cx:0,cy:0,hwidth:0,hheight:0}}}])}(),Po=function(){return u((function t(){o(this,t)}),[{key:"update",value:function(t,e){var n=t.x,r=t.y,i=t.width,a=void 0===i?0:i,o=t.height,s=void 0===o?0:o;return{cx:(void 0===n?0:n)+a/2,cy:(void 0===r?0:r)+s/2,hwidth:a/2,hheight:s/2}}}])}();var So=function(){return u((function t(e){o(this,t),this.eventPhase=t.prototype.NONE,this.bubbles=!0,this.cancelBubble=!0,this.cancelable=!1,this.defaultPrevented=!1,this.propagationStopped=!1,this.propagationImmediatelyStopped=!1,this.layer=new mn,this.page=new mn,this.canvas=new mn,this.viewport=new mn,this.composed=!1,this.NONE=0,this.CAPTURING_PHASE=1,this.AT_TARGET=2,this.BUBBLING_PHASE=3,this.manager=e}),[{key:"name",get:function(){return this.type}},{key:"layerX",get:function(){return this.layer.x}},{key:"layerY",get:function(){return this.layer.y}},{key:"pageX",get:function(){return this.page.x}},{key:"pageY",get:function(){return this.page.y}},{key:"x",get:function(){return this.canvas.x}},{key:"y",get:function(){return this.canvas.y}},{key:"canvasX",get:function(){return this.canvas.x}},{key:"canvasY",get:function(){return this.canvas.y}},{key:"viewportX",get:function(){return this.viewport.x}},{key:"viewportY",get:function(){return this.viewport.y}},{key:"composedPath",value:function(){return!this.manager||this.path&&this.path[0]===this.target||(this.path=this.target?this.manager.propagationPath(this.target):[]),this.path}},{key:"propagationPath",get:function(){return this.composedPath()}},{key:"preventDefault",value:function(){this.nativeEvent instanceof Event&&this.nativeEvent.cancelable&&this.nativeEvent.preventDefault(),this.defaultPrevented=!0}},{key:"stopImmediatePropagation",value:function(){this.propagationImmediatelyStopped=!0}},{key:"stopPropagation",value:function(){this.propagationStopped=!0}},{key:"initEvent",value:function(){}},{key:"initUIEvent",value:function(){}},{key:"clone",value:function(){throw Error(gn)}}])}(),Ao=function(t){function e(){var t;o(this,e);for(var n=arguments.length,r=Array(n),i=0;n>i;i++)r[i]=arguments[i];return(t=p(this,e,[].concat(r))).client=new mn,t.movement=new mn,t.offset=new mn,t.global=new mn,t.screen=new mn,t}return y(e,t),u(e,[{key:"clientX",get:function(){return this.client.x}},{key:"clientY",get:function(){return this.client.y}},{key:"movementX",get:function(){return this.movement.x}},{key:"movementY",get:function(){return this.movement.y}},{key:"offsetX",get:function(){return this.offset.x}},{key:"offsetY",get:function(){return this.offset.y}},{key:"globalX",get:function(){return this.global.x}},{key:"globalY",get:function(){return this.global.y}},{key:"screenX",get:function(){return this.screen.x}},{key:"screenY",get:function(){return this.screen.y}},{key:"getModifierState",value:function(t){return"getModifierState"in this.nativeEvent&&this.nativeEvent.getModifierState(t)}},{key:"initMouseEvent",value:function(){throw Error(gn)}}])}(So),Co=function(t){function e(){var t;o(this,e);for(var n=arguments.length,r=Array(n),i=0;n>i;i++)r[i]=arguments[i];return(t=p(this,e,[].concat(r))).width=0,t.height=0,t.isPrimary=!1,t}return y(e,t),u(e,[{key:"getCoalescedEvents",value:function(){return"pointermove"===this.type||"mousemove"===this.type||"touchmove"===this.type?[this]:[]}},{key:"getPredictedEvents",value:function(){throw Error("getPredictedEvents is not supported!")}},{key:"clone",value:function(){return this.manager.clonePointerEvent(this)}}])}(Ao),Ro=function(t){function e(){return o(this,e),p(this,e,arguments)}return y(e,t),u(e,[{key:"clone",value:function(){return this.manager.cloneWheelEvent(this)}}])}(Ao),Oo=function(t){function e(t,n){var r;return o(this,e),(r=p(this,e,[null])).type=t,r.detail=n,Object.assign(r,n),r}return y(e,t),u(e)}(So),Lo=new WeakMap,Io=function(){return u((function t(){o(this,t),this.emitter=new E}),[{key:"on",value:function(t,e,n){return this.addEventListener(t,e,n),this}},{key:"addEventListener",value:function(t,e,n){var r=!1,i=!1;if(Ht(n))r=n;else if(n){var a=n.capture;r=void 0!==a&&a;var o=n.once;i=void 0!==o&&o}r&&(t+="capture"),e=It(e)?e:e.handleEvent;var s=It(e)?void 0:e;return i?this.emitter.once(t,e,s):this.emitter.on(t,e,s),this}},{key:"off",value:function(t,e,n){return t?this.removeEventListener(t,e,n):this.removeAllEventListeners(),this}},{key:"removeAllEventListeners",value:function(){var t;null===(t=this.emitter)||void 0===t||t.removeAllListeners()}},{key:"removeEventListener",value:function(t,e,n){var r;if(!this.emitter)return this;(Ht(n)?n:null==n?void 0:n.capture)&&(t+="capture"),e=It(e)?e:null===(r=e)||void 0===r?void 0:r.handleEvent;var i=It(e)?void 0:e;return this.emitter.off(t,e,i),this}},{key:"emit",value:function(t,e){this.dispatchEvent(new Oo(t,e))}},{key:"dispatchEvent",value:function(t){var e,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=Lo.get(this);r||(r=this.document?this:this.defaultView?this.defaultView:null===(e=this.ownerDocument)||void 0===e?void 0:e.defaultView)&&Lo.set(this,r);if(r){if(t.manager=r.getEventService(),!t.manager)return!1;t.defaultPrevented=!1,t.path?t.path.length=0:t.page=[],n||(t.target=this),t.manager.dispatchEvent(t,t.type,n)}else this.emitter.emit(t.type,t);return!t.defaultPrevented}}])}(),_o=function(t){function e(){var t;o(this,e);for(var n=arguments.length,r=Array(n),i=0;n>i;i++)r[i]=arguments[i];return(t=p(this,e,[].concat(r))).shadow=!1,t.ownerDocument=null,t.isConnected=!1,t.baseURI="",t.childNodes=[],t.nodeType=0,t.nodeName="",t.nodeValue=null,t.parentNode=null,t}return y(e,t),u(e,[{key:"textContent",get:function(){var t="";this.nodeName===en.TEXT&&(t+=this.style.text);var e,n=Qe(this.childNodes);try{for(n.s();!(e=n.n()).done;){var r=e.value;t+=r.nodeName===en.TEXT?r.nodeValue:r.textContent}}catch(t){n.e(t)}finally{n.f()}return t},set:function(t){var e=this;this.childNodes.slice().forEach((function(t){e.removeChild(t)})),this.nodeName===en.TEXT&&(this.style.text="".concat(t))}},{key:"getRootNode",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.parentNode?this.parentNode.getRootNode(t):t.composed&&this.host?this.host.getRootNode(t):this}},{key:"hasChildNodes",value:function(){return this.childNodes.length>0}},{key:"isDefaultNamespace",value:function(t){throw Error(gn)}},{key:"lookupNamespaceURI",value:function(t){throw Error(gn)}},{key:"lookupPrefix",value:function(t){throw Error(gn)}},{key:"normalize",value:function(){throw Error(gn)}},{key:"isEqualNode",value:function(t){return this===t}},{key:"isSameNode",value:function(t){return this.isEqualNode(t)}},{key:"parent",get:function(){return this.parentNode}},{key:"parentElement",get:function(){return null}},{key:"nextSibling",get:function(){return null}},{key:"previousSibling",get:function(){return null}},{key:"firstChild",get:function(){return this.childNodes.length>0?this.childNodes[0]:null}},{key:"lastChild",get:function(){return this.childNodes.length>0?this.childNodes[this.childNodes.length-1]:null}},{key:"compareDocumentPosition",value:function(t){if(t===this)return 0;for(var n=t,r=this,i=[n],a=[r];null!==(o=n.parentNode)&&void 0!==o?o:r.parentNode;){var o;n=n.parentNode?(i.push(n.parentNode),n.parentNode):n,r=r.parentNode?(a.push(r.parentNode),r.parentNode):r}if(n!==r)return e.DOCUMENT_POSITION_DISCONNECTED|e.DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC|e.DOCUMENT_POSITION_PRECEDING;var s=i.length>a.length?i:a,u=s===i?a:i;if(s[s.length-u.length]===u[0])return s===i?e.DOCUMENT_POSITION_CONTAINED_BY|e.DOCUMENT_POSITION_FOLLOWING:e.DOCUMENT_POSITION_CONTAINS|e.DOCUMENT_POSITION_PRECEDING;for(var l=s.length-u.length,c=u.length-1;c>=0;c--){var h=u[c],f=s[l+c];if(f!==h){var d=h.parentNode.childNodes;return d.indexOf(h)<d.indexOf(f)?u===i?e.DOCUMENT_POSITION_PRECEDING:e.DOCUMENT_POSITION_FOLLOWING:s===i?e.DOCUMENT_POSITION_PRECEDING:e.DOCUMENT_POSITION_FOLLOWING}}return e.DOCUMENT_POSITION_FOLLOWING}},{key:"contain",value:function(t){return this.contains(t)}},{key:"contains",value:function(t){for(var e=t;e&&this!==e;)e=e.parentNode;return!!e}},{key:"getAncestor",value:function(t){for(var e=this;t>0&&e;)e=e.parentNode,t--;return e}},{key:"forEach",value:function(t){for(var e=[this];e.length>0;){var n=e.pop();if(!1===t(n))break;for(var r=n.childNodes.length-1;r>=0;r--)e.push(n.childNodes[r])}}}],[{key:"isNode",value:function(t){return!!t.childNodes}}])}(Io);_o.DOCUMENT_POSITION_DISCONNECTED=1,_o.DOCUMENT_POSITION_PRECEDING=2,_o.DOCUMENT_POSITION_FOLLOWING=4,_o.DOCUMENT_POSITION_CONTAINS=8,_o.DOCUMENT_POSITION_CONTAINED_BY=16,_o.DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC=32;var Do=function(){return u((function t(e,n){var r=this;o(this,t),this.nativeHTMLMap=new WeakMap,this.cursor="default",this.mappingTable={},this.mappingState={trackingData:{}},this.eventPool=new Map,this.tmpMatrix=M(),this.tmpVec3=nt(),this.onPointerDown=function(t){var e=r.createPointerEvent(t);if(r.dispatchEvent(e,"pointerdown"),"touch"===e.pointerType)r.dispatchEvent(e,"touchstart");else if("mouse"===e.pointerType||"pen"===e.pointerType){r.dispatchEvent(e,2===e.button?"rightdown":"mousedown")}r.trackingData(t.pointerId).pressTargetsByButton[t.button]=e.composedPath(),r.freeEvent(e)},this.onPointerUp=function(t){var e=Ua.now(),n=r.createPointerEvent(t,void 0,void 0,r.context.config.alwaysTriggerPointerEventOnCanvas?r.rootTarget:void 0);if(r.dispatchEvent(n,"pointerup"),"touch"===n.pointerType)r.dispatchEvent(n,"touchend");else if("mouse"===n.pointerType||"pen"===n.pointerType){r.dispatchEvent(n,2===n.button?"rightup":"mouseup")}var i=r.trackingData(t.pointerId),a=r.findMountedTarget(i.pressTargetsByButton[t.button]),o=a;if(a&&!n.composedPath().includes(a)){for(var s=a;s&&!n.composedPath().includes(s);){if(n.currentTarget=s,r.notifyTarget(n,"pointerupoutside"),"touch"===n.pointerType)r.notifyTarget(n,"touchendoutside");else if("mouse"===n.pointerType||"pen"===n.pointerType){r.notifyTarget(n,2===n.button?"rightupoutside":"mouseupoutside")}_o.isNode(s)&&(s=s.parentNode)}delete i.pressTargetsByButton[t.button],o=s}if(o){var u,l=r.clonePointerEvent(n,"click");l.target=o,l.path=[],i.clicksByButton[t.button]||(i.clicksByButton[t.button]={clickCount:0,target:l.target,timeStamp:e});var c=i.clicksByButton[t.button];c.target===l.target&&e-c.timeStamp<r.context.renderingContext.root.ownerDocument.defaultView.getConfig().dblClickSpeed?++c.clickCount:c.clickCount=1,c.target=l.target,c.timeStamp=e,l.detail=c.clickCount,null!==(u=n.detail)&&void 0!==u&&u.preventClick||(r.context.config.useNativeClickEvent||"mouse"!==l.pointerType&&"touch"!==l.pointerType||r.dispatchEvent(l,"click"),r.dispatchEvent(l,"pointertap")),r.freeEvent(l)}r.freeEvent(n)},this.onPointerMove=function(t){var e=r.createPointerEvent(t,void 0,void 0,r.context.config.alwaysTriggerPointerEventOnCanvas?r.rootTarget:void 0),n="mouse"===e.pointerType||"pen"===e.pointerType,i=r.trackingData(t.pointerId),a=r.findMountedTarget(i.overTargets);if(i.overTargets&&a!==e.target){var o=r.createPointerEvent(t,"mousemove"===t.type?"mouseout":"pointerout",a||void 0);if(r.dispatchEvent(o,"pointerout"),n&&r.dispatchEvent(o,"mouseout"),!e.composedPath().includes(a)){var s=r.createPointerEvent(t,"pointerleave",a||void 0);for(s.eventPhase=s.AT_TARGET;s.target&&!e.composedPath().includes(s.target);)s.currentTarget=s.target,r.notifyTarget(s),n&&r.notifyTarget(s,"mouseleave"),_o.isNode(s.target)&&(s.target=s.target.parentNode);r.freeEvent(s)}r.freeEvent(o)}if(a!==e.target){var u=r.clonePointerEvent(e,"mousemove"===t.type?"mouseover":"pointerover");r.dispatchEvent(u,"pointerover"),n&&r.dispatchEvent(u,"mouseover");for(var l=a&&_o.isNode(a)&&a.parentNode;l&&l!==(_o.isNode(r.rootTarget)&&r.rootTarget.parentNode)&&l!==e.target;)l=l.parentNode;if(!l||l===(_o.isNode(r.rootTarget)&&r.rootTarget.parentNode)){var c=r.clonePointerEvent(e,"pointerenter");for(c.eventPhase=c.AT_TARGET;c.target&&c.target!==a&&c.target!==(_o.isNode(r.rootTarget)&&r.rootTarget.parentNode);)c.currentTarget=c.target,r.notifyTarget(c),n&&r.notifyTarget(c,"mouseenter"),_o.isNode(c.target)&&(c.target=c.target.parentNode);r.freeEvent(c)}r.freeEvent(u)}r.dispatchEvent(e,"pointermove"),"touch"===e.pointerType&&r.dispatchEvent(e,"touchmove"),n&&(r.dispatchEvent(e,"mousemove"),r.cursor=r.getCursor(e.target)),i.overTargets=e.composedPath(),r.freeEvent(e)},this.onPointerOut=function(t){var e=r.trackingData(t.pointerId);if(e.overTargets){var n="mouse"===t.pointerType||"pen"===t.pointerType,i=r.findMountedTarget(e.overTargets),a=r.createPointerEvent(t,"pointerout",i||void 0);r.dispatchEvent(a),n&&r.dispatchEvent(a,"mouseout");var o=r.createPointerEvent(t,"pointerleave",i||void 0);for(o.eventPhase=o.AT_TARGET;o.target&&o.target!==(_o.isNode(r.rootTarget)&&r.rootTarget.parentNode);)o.currentTarget=o.target,r.notifyTarget(o),n&&r.notifyTarget(o,"mouseleave"),_o.isNode(o.target)&&(o.target=o.target.parentNode);e.overTargets=null,r.freeEvent(a),r.freeEvent(o)}r.cursor=null},this.onPointerOver=function(t){var e=r.trackingData(t.pointerId),n=r.createPointerEvent(t),i="mouse"===n.pointerType||"pen"===n.pointerType;r.dispatchEvent(n,"pointerover"),i&&r.dispatchEvent(n,"mouseover"),"mouse"===n.pointerType&&(r.cursor=r.getCursor(n.target));var a=r.clonePointerEvent(n,"pointerenter");for(a.eventPhase=a.AT_TARGET;a.target&&a.target!==(_o.isNode(r.rootTarget)&&r.rootTarget.parentNode);)a.currentTarget=a.target,r.notifyTarget(a),i&&r.notifyTarget(a,"mouseenter"),_o.isNode(a.target)&&(a.target=a.target.parentNode);e.overTargets=n.composedPath(),r.freeEvent(n),r.freeEvent(a)},this.onPointerUpOutside=function(t){var e=r.trackingData(t.pointerId),n=r.findMountedTarget(e.pressTargetsByButton[t.button]),i=r.createPointerEvent(t);if(n){for(var a=n;a;)i.currentTarget=a,r.notifyTarget(i,"pointerupoutside"),"touch"===i.pointerType||"mouse"!==i.pointerType&&"pen"!==i.pointerType||r.notifyTarget(i,2===i.button?"rightupoutside":"mouseupoutside"),_o.isNode(a)&&(a=a.parentNode);delete e.pressTargetsByButton[t.button]}r.freeEvent(i)},this.onWheel=function(t){var e=r.createWheelEvent(t);r.dispatchEvent(e),r.freeEvent(e)},this.onClick=function(t){if(r.context.config.useNativeClickEvent){var e=r.createPointerEvent(t);r.dispatchEvent(e),r.freeEvent(e)}},this.onPointerCancel=function(t){var e=r.createPointerEvent(t,void 0,void 0,r.context.config.alwaysTriggerPointerEventOnCanvas?r.rootTarget:void 0);r.dispatchEvent(e),r.freeEvent(e)},this.globalRuntime=e,this.context=n}),[{key:"init",value:function(){this.rootTarget=this.context.renderingContext.root.parentNode,this.addEventMapping("pointerdown",this.onPointerDown),this.addEventMapping("pointerup",this.onPointerUp),this.addEventMapping("pointermove",this.onPointerMove),this.addEventMapping("pointerout",this.onPointerOut),this.addEventMapping("pointerleave",this.onPointerOut),this.addEventMapping("pointercancel",this.onPointerCancel),this.addEventMapping("pointerover",this.onPointerOver),this.addEventMapping("pointerupoutside",this.onPointerUpOutside),this.addEventMapping("wheel",this.onWheel),this.addEventMapping("click",this.onClick)}},{key:"destroy",value:function(){this.mappingTable={},this.mappingState={},this.eventPool.clear()}},{key:"getScale",value:function(){var t=this.context.contextService.getBoundingClientRect(),e=1,n=1,r=this.context.contextService.getDomElement();if(r&&t){var i=r.offsetWidth,a=r.offsetHeight;i&&a&&(e=t.width/i,n=t.height/a)}return{scaleX:e,scaleY:n,bbox:t}}},{key:"client2Viewport",value:function(t){var e=this.getScale(),n=e.bbox;return new mn((t.x-((null==n?void 0:n.left)||0))/e.scaleX,(t.y-((null==n?void 0:n.top)||0))/e.scaleY)}},{key:"viewport2Client",value:function(t){var e=this.getScale(),n=e.bbox;return new mn((t.x+((null==n?void 0:n.left)||0))*e.scaleX,(t.y+((null==n?void 0:n.top)||0))*e.scaleY)}},{key:"viewport2Canvas",value:function(t){var e=t.x,n=t.y,r=this.rootTarget.defaultView.getCamera(),i=this.context.config,a=i.width,o=i.height,s=r.getPerspectiveInverse(),u=r.getWorldTransform(),l=O(this.tmpMatrix,u,s),c=st(this.tmpVec3,e/a*2-1,2*(1-n/o)-1,0);return pt(c,c,l),new mn(c[0],c[1])}},{key:"canvas2Viewport",value:function(t){var e=this.rootTarget.defaultView.getCamera(),n=e.getPerspective(),r=e.getViewTransform(),i=O(this.tmpMatrix,n,r),a=st(this.tmpVec3,t.x,t.y,0);pt(this.tmpVec3,this.tmpVec3,i);var o=this.context.config;return new mn((a[0]+1)/2*o.width,(1-(a[1]+1)/2)*o.height)}},{key:"setPickHandler",value:function(t){this.pickHandler=t}},{key:"addEventMapping",value:function(t,e){this.mappingTable[t]||(this.mappingTable[t]=[]),this.mappingTable[t].push({fn:e,priority:0}),this.mappingTable[t].sort((function(t,e){return t.priority-e.priority}))}},{key:"mapEvent",value:function(t){if(this.rootTarget){var e=this.mappingTable[t.type];if(e)for(var n=0,r=e.length;r>n;n++)e[n].fn(t);else console.warn("[EventService]: Event mapping not defined for ".concat(t.type))}}},{key:"dispatchEvent",value:function(t,e,n){n?(t.eventPhase=t.AT_TARGET,t.currentTarget=this.rootTarget.defaultView||null,this.notifyListeners(t,e)):(t.propagationStopped=!1,t.propagationImmediatelyStopped=!1,this.propagate(t,e))}},{key:"propagate",value:function(t,e){if(t.target){var n=t.composedPath();t.eventPhase=t.CAPTURING_PHASE;for(var r=n.length-1;r>=1;r--)if(t.currentTarget=n[r],this.notifyTarget(t,e),t.propagationStopped||t.propagationImmediatelyStopped)return;if(t.eventPhase=t.AT_TARGET,t.currentTarget=t.target,this.notifyTarget(t,e),!t.propagationStopped&&!t.propagationImmediatelyStopped){var i=n.indexOf(t.currentTarget);t.eventPhase=t.BUBBLING_PHASE;for(var a=i+1;n.length>a;a++)if(t.currentTarget=n[a],this.notifyTarget(t,e),t.propagationStopped||t.propagationImmediatelyStopped)return}}}},{key:"propagationPath",value:function(t){var e=[t],n=this.rootTarget.defaultView||null;if(n&&n===t)return e.unshift(n.document),e;for(var r=0;2048>r&&t!==this.rootTarget;r++)_o.isNode(t)&&t.parentNode&&(e.push(t.parentNode),t=t.parentNode);return n&&e.push(n),e}},{key:"hitTest",value:function(t){var e=t.viewportX,n=t.viewportY,r=this.context.config;return 0>e||0>n||e>r.width||n>r.height?null:!r.disableHitTesting&&this.pickHandler(t)||this.rootTarget||null}},{key:"isNativeEventFromCanvas",value:function(t,e){var n,r=null==e?void 0:e.target;if(null!==(n=r)&&void 0!==n&&n.shadowRoot&&(r=e.composedPath()[0]),r){if(r===t)return!0;if(t&&t.contains)return t.contains(r)}return!(null==e||!e.composedPath)&&e.composedPath().indexOf(t)>-1}},{key:"getExistedHTML",value:function(t){if(t.nativeEvent.composedPath)for(var e=0,n=t.nativeEvent.composedPath();n.length>e;e++){var r=this.nativeHTMLMap.get(n[e]);if(r)return r}return null}},{key:"pickTarget",value:function(t){return this.hitTest({clientX:t.clientX,clientY:t.clientY,viewportX:t.viewportX,viewportY:t.viewportY,x:t.canvasX,y:t.canvasY})}},{key:"createPointerEvent",value:function(t,e,n,r){var i=this.allocateEvent(Co);this.copyPointerData(t,i),this.copyMouseData(t,i),this.copyData(t,i),i.nativeEvent=t.nativeEvent,i.originalEvent=t;var a=this.getExistedHTML(i),o=this.context.contextService.getDomElement();return i.target=null!=n?n:a||this.isNativeEventFromCanvas(o,i.nativeEvent)&&this.pickTarget(i)||r,"string"==typeof e&&(i.type=e),i}},{key:"createWheelEvent",value:function(t){var e=this.allocateEvent(Ro);this.copyWheelData(t,e),this.copyMouseData(t,e),this.copyData(t,e),e.nativeEvent=t.nativeEvent,e.originalEvent=t;var n=this.getExistedHTML(e),r=this.context.contextService.getDomElement();return e.target=n||this.isNativeEventFromCanvas(r,e.nativeEvent)&&this.pickTarget(e),e}},{key:"trackingData",value:function(t){return this.mappingState.trackingData[t]||(this.mappingState.trackingData[t]={pressTargetsByButton:{},clicksByButton:{},overTarget:null}),this.mappingState.trackingData[t]}},{key:"cloneWheelEvent",value:function(t){var e=this.allocateEvent(Ro);return e.nativeEvent=t.nativeEvent,e.originalEvent=t.originalEvent,this.copyWheelData(t,e),this.copyMouseData(t,e),this.copyData(t,e),e.target=t.target,e.path=t.composedPath().slice(),e.type=t.type,e}},{key:"clonePointerEvent",value:function(t,e){var n=this.allocateEvent(Co);return n.nativeEvent=t.nativeEvent,n.originalEvent=t.originalEvent,this.copyPointerData(t,n),this.copyMouseData(t,n),this.copyData(t,n),n.target=t.target,n.path=t.composedPath().slice(),n.type=null!=e?e:n.type,n}},{key:"copyPointerData",value:function(t,e){e.pointerId=t.pointerId,e.width=t.width,e.height=t.height,e.isPrimary=t.isPrimary,e.pointerType=t.pointerType,e.pressure=t.pressure,e.tangentialPressure=t.tangentialPressure,e.tiltX=t.tiltX,e.tiltY=t.tiltY,e.twist=t.twist}},{key:"copyMouseData",value:function(t,e){e.altKey=t.altKey,e.button=t.button,e.buttons=t.buttons,e.ctrlKey=t.ctrlKey,e.metaKey=t.metaKey,e.shiftKey=t.shiftKey,e.client.copyFrom(t.client),e.movement.copyFrom(t.movement),e.canvas.copyFrom(t.canvas),e.screen.copyFrom(t.screen),e.global.copyFrom(t.global),e.offset.copyFrom(t.offset)}},{key:"copyWheelData",value:function(t,e){e.deltaMode=t.deltaMode,e.deltaX=t.deltaX,e.deltaY=t.deltaY,e.deltaZ=t.deltaZ}},{key:"copyData",value:function(t,e){e.isTrusted=t.isTrusted,e.timeStamp=Ua.now(),e.type=t.type,e.detail=t.detail,e.view=t.view,e.page.copyFrom(t.page),e.viewport.copyFrom(t.viewport)}},{key:"allocateEvent",value:function(t){this.eventPool.has(t)||this.eventPool.set(t,[]);var e=this.eventPool.get(t).pop()||new t(this);return e.eventPhase=e.NONE,e.currentTarget=null,e.path=[],e.target=null,e}},{key:"freeEvent",value:function(t){if(t.manager!==this)throw Error("It is illegal to free an event not managed by this EventBoundary!");var e=t.constructor;this.eventPool.has(e)||this.eventPool.set(e,[]),this.eventPool.get(e).push(t)}},{key:"notifyTarget",value:function(t,e){e=null!=e?e:t.type,this.notifyListeners(t,t.eventPhase===t.CAPTURING_PHASE||t.eventPhase===t.AT_TARGET?"".concat(e,"capture"):e),t.eventPhase===t.AT_TARGET&&this.notifyListeners(t,e)}},{key:"notifyListeners",value:function(t,e){var n=t.currentTarget.emitter,r=n._events[e];if(r)if("fn"in r)r.once&&n.removeListener(e,r.fn,void 0,!0),r.fn.call(t.currentTarget||r.context,t);else for(var i=0;r.length>i&&!t.propagationImmediatelyStopped;i++)r[i].once&&n.removeListener(e,r[i].fn,void 0,!0),r[i].fn.call(t.currentTarget||r[i].context,t)}},{key:"findMountedTarget",value:function(t){if(!t)return null;for(var e=t[t.length-1],n=t.length-2;n>=0;n--){var r=t[n];if(!(r===this.rootTarget||_o.isNode(r)&&r.parentNode===e))break;e=t[n]}return e}},{key:"getCursor",value:function(t){for(var e=t;e;){var n=Fa(e)&&e.getAttribute("cursor");if(n)return n;e=_o.isNode(e)&&e.parentNode}}}])}(),Fo=function(){return u((function t(){o(this,t)}),[{key:"getOrCreateCanvas",value:function(t,e){if(this.canvas)return this.canvas;if(t||ks.offscreenCanvas)this.canvas=t||ks.offscreenCanvas,this.context=this.canvas.getContext("2d",a({willReadFrequently:!0},e));else try{this.canvas=new window.OffscreenCanvas(0,0),this.context=this.canvas.getContext("2d",a({willReadFrequently:!0},e)),this.context&&this.context.measureText||(this.canvas=document.createElement("canvas"),this.context=this.canvas.getContext("2d"))}catch(t){this.canvas=document.createElement("canvas"),this.context=this.canvas.getContext("2d",a({willReadFrequently:!0},e))}return this.canvas.width=10,this.canvas.height=10,this.canvas}},{key:"getOrCreateContext",value:function(t,e){return this.context||this.getOrCreateCanvas(t,e),this.context}}],[{key:"createCanvas",value:function(){try{return new window.OffscreenCanvas(0,0)}catch(t){}try{return document.createElement("canvas")}catch(t){}return null}}])}(),Go=function(t){return t[t.CAMERA_CHANGED=0]="CAMERA_CHANGED",t[t.DISPLAY_OBJECT_CHANGED=1]="DISPLAY_OBJECT_CHANGED",t[t.NONE=2]="NONE",t}({}),Bo=function(){return u((function t(e,n){o(this,t),this.inited=!1,this.stats={total:0,rendered:0},this.zIndexCounter=0,this.hooks={init:new ro,initAsync:new eo,dirtycheck:new io,cull:new io,beginFrame:new ro,beforeRender:new ro,render:new ro,afterRender:new ro,endFrame:new ro,destroy:new ro,pick:new no,pickSync:new io,pointerDown:new ro,pointerUp:new ro,pointerMove:new ro,pointerOut:new ro,pointerOver:new ro,pointerWheel:new ro,pointerCancel:new ro,click:new ro},this.globalRuntime=e,this.context=n}),[{key:"init",value:function(t){var e=this,n=a(a({},this.globalRuntime),this.context);this.context.renderingPlugins.forEach((function(t){t.apply(n,e.globalRuntime)})),this.hooks.init.call(),0===this.hooks.initAsync.getCallbacksNum()?(this.inited=!0,t()):this.hooks.initAsync.promise().then((function(){e.inited=!0,t()})).catch((function(t){}))}},{key:"getStats",value:function(){return this.stats}},{key:"disableDirtyRectangleRendering",value:function(){return!this.context.config.renderer.getConfig().enableDirtyRectangleRendering||this.context.renderingContext.renderReasons.has(Go.CAMERA_CHANGED)}},{key:"render",value:function(t,e,n){var r=this;this.stats.total=0,this.stats.rendered=0,this.zIndexCounter=0;var i=this.context.renderingContext;if(this.globalRuntime.sceneGraphService.syncHierarchy(i.root),this.globalRuntime.sceneGraphService.triggerPendingEvents(),i.renderReasons.size&&this.inited){i.dirtyRectangleRenderingDisabled=this.disableDirtyRectangleRendering();var a=1===i.renderReasons.size&&i.renderReasons.has(Go.CAMERA_CHANGED),o=!t.disableRenderHooks||!(t.disableRenderHooks&&a);o&&this.renderDisplayObject(i.root,t,i),this.hooks.beginFrame.call(e),o&&i.renderListCurrentFrame.forEach((function(t){r.hooks.beforeRender.call(t),r.hooks.render.call(t),r.hooks.afterRender.call(t)})),this.hooks.endFrame.call(e),i.renderListCurrentFrame=[],i.renderReasons.clear(),n()}}},{key:"renderDisplayObject",value:function(t,e,n){var r=this,i=e.renderer.getConfig(),a=i.enableDirtyCheck,o=i.enableCulling;function s(t){var e=t.renderable,i=t.sortable,s=a?e.dirty||n.dirtyRectangleRenderingDisabled?t:null:t;if(s){var u=o?r.hooks.cull.call(s,r.context.camera):s;u&&(r.stats.rendered+=1,n.renderListCurrentFrame.push(u))}e.dirty=!1,i.renderOrder=r.zIndexCounter,r.zIndexCounter+=1,r.stats.total+=1,i.dirty&&(r.sort(t,i),i.dirty=!1,i.dirtyChildren=[],i.dirtyReason=void 0)}for(var u=[t];u.length>0;){var l,c=u.pop();s(c);for(var h=(null===(l=c.sortable)||void 0===l||null===(l=l.sorted)||void 0===l?void 0:l.length)>0?c.sortable.sorted:c.childNodes,f=h.length-1;f>=0;f--)u.push(h[f])}}},{key:"sort",value:function(t,e){var n,r;(null==e||null===(n=e.sorted)||void 0===n?void 0:n.length)>0&&e.dirtyReason!==Xn.Z_INDEX_CHANGED?e.dirtyChildren.forEach((function(n){var r=e.sorted.indexOf(n);if(r>-1&&e.sorted.splice(r,1),t.childNodes.indexOf(n)>-1)if(0===e.sorted.length)e.sorted.push(n);else{var i=function(t,e){for(var n=0,r=t.length;r>n;){var i=n+r>>>1;0>Ga(t[i],e)?n=i+1:r=i}return n}(e.sorted,n);e.sorted.splice(i,0,n)}})):e.sorted=t.childNodes.slice().sort(Ga),(null===(r=e.sorted)||void 0===r?void 0:r.length)>0&&0===t.childNodes.filter((function(t){return t.parsedStyle.zIndex})).length&&(e.sorted=[])}},{key:"destroy",value:function(){this.inited=!1,this.hooks.destroy.call(),this.globalRuntime.sceneGraphService.clearPendingEvents()}},{key:"dirtify",value:function(){this.context.renderingContext.renderReasons.add(Go.DISPLAY_OBJECT_CHANGED)}}])}(),Vo=/\[\s*(.*)=(.*)\s*\]/,Yo=function(){return u((function t(){o(this,t)}),[{key:"selectOne",value:function(t,e){var n=this;if(t.startsWith("."))return e.find((function(e){return((null==e?void 0:e.classList)||[]).indexOf(n.getIdOrClassname(t))>-1}));if(t.startsWith("#"))return e.find((function(e){return e.id===n.getIdOrClassname(t)}));if(t.startsWith("[")){var r=this.getAttribute(t),i=r.name,a=r.value;return i?e.find((function(t){return e!==t&&("name"===i?t.name===a:n.attributeToString(t,i)===a)})):null}return e.find((function(n){return e!==n&&n.nodeName===t}))}},{key:"selectAll",value:function(t,e){var n=this;if(t.startsWith("."))return e.findAll((function(r){return e!==r&&((null==r?void 0:r.classList)||[]).indexOf(n.getIdOrClassname(t))>-1}));if(t.startsWith("#"))return e.findAll((function(r){return e!==r&&r.id===n.getIdOrClassname(t)}));if(t.startsWith("[")){var r=this.getAttribute(t),i=r.name,a=r.value;return i?e.findAll((function(t){return e!==t&&("name"===i?t.name===a:n.attributeToString(t,i)===a)})):[]}return e.findAll((function(n){return e!==n&&n.nodeName===t}))}},{key:"is",value:function(t,e){if(t.startsWith("."))return e.className===this.getIdOrClassname(t);if(t.startsWith("#"))return e.id===this.getIdOrClassname(t);if(t.startsWith("[")){var n=this.getAttribute(t),r=n.name,i=n.value;return"name"===r?e.name===i:this.attributeToString(e,r)===i}return e.nodeName===t}},{key:"getIdOrClassname",value:function(t){return t.substring(1)}},{key:"getAttribute",value:function(t){var e=t.match(Vo),n="",r="";return e&&e.length>2&&(n=e[1].replace(/"/g,""),r=e[2].replace(/"/g,"")),{name:n,value:r}}},{key:"attributeToString",value:function(t,e){if(!t.getAttribute)return"";var n=t.getAttribute(e);return _t(n)?"":n.toString?""+n:""}}])}(),Uo=function(t){return t.ATTR_MODIFIED="DOMAttrModified",t.INSERTED="DOMNodeInserted",t.MOUNTED="DOMNodeInsertedIntoDocument",t.REMOVED="removed",t.UNMOUNTED="DOMNodeRemovedFromDocument",t.REPARENT="reparent",t.DESTROY="destroy",t.BOUNDS_CHANGED="bounds-changed",t.CULLED="culled",t}({}),zo=function(t){function e(t,n,r,i,a,s,u,l){var c;return o(this,e),(c=p(this,e,[null])).relatedNode=n,c.prevValue=r,c.newValue=i,c.attrName=a,c.attrChange=s,c.prevParsedValue=u,c.newParsedValue=l,c.type=t,c}return y(e,t),u(e)}(So);function jo(t){var e=t.renderable;e&&(e.renderBoundsDirty=!0,e.boundsDirty=!0)}zo.ADDITION=2,zo.MODIFICATION=1,zo.REMOVAL=3;var Xo,Ho,Wo,qo=new zo(Uo.REPARENT,null,"","","",0,"",""),Zo=Lt(),Ko=nt(),Qo=at(1,1,1),$o=M(),Jo=Lt(),ts=nt(),es=M(),ns=Tt(),rs=nt(),is=Tt(),as=nt(),os=nt(),ss=nt(),us=M(),ls=Tt(),cs=Tt(),hs=Tt(),fs={affectChildren:!0},ds=function(){return u((function t(e){o(this,t),this.pendingEvents=new Map,this.boundsChangedEvent=new Oo(Uo.BOUNDS_CHANGED),this.displayObjectDependencyMap=new WeakMap,this.runtime=e}),[{key:"matches",value:function(t,e){return this.runtime.sceneGraphSelector.is(t,e)}},{key:"querySelector",value:function(t,e){return this.runtime.sceneGraphSelector.selectOne(t,e)}},{key:"querySelectorAll",value:function(t,e){return this.runtime.sceneGraphSelector.selectAll(t,e)}},{key:"attach",value:function(t,e,n){var r,i=!1;t.parentNode&&(i=t.parentNode!==e,this.detach(t));var a=t.nodeName===en.FRAGMENT,o=za(e);t.parentNode=e;var s=a?t.childNodes:[t];zt(n)?s.forEach((function(t){e.childNodes.splice(n,0,t),t.parentNode=e})):s.forEach((function(t){e.childNodes.push(t),t.parentNode=e}));var u=e.sortable;if((null!=u&&null!==(r=u.sorted)&&void 0!==r&&r.length||u.dirty||t.parsedStyle.zIndex)&&(-1===u.dirtyChildren.indexOf(t)&&u.dirtyChildren.push(t),u.dirty=!0,u.dirtyReason=Xn.ADDED),!o){if(a)this.dirtifyFragment(t);else{var l=t.transformable;l&&this.dirtifyWorld(t,l)}i&&t.dispatchEvent(qo)}}},{key:"detach",value:function(t){var e,n;if(t.parentNode){var r=t.transformable,i=t.parentNode.sortable;(null!=i&&null!==(e=i.sorted)&&void 0!==e&&e.length||null!==(n=t.style)&&void 0!==n&&n.zIndex)&&(-1===i.dirtyChildren.indexOf(t)&&i.dirtyChildren.push(t),i.dirty=!0,i.dirtyReason=Xn.REMOVED);var a=t.parentNode.childNodes.indexOf(t);a>-1&&t.parentNode.childNodes.splice(a,1),r&&this.dirtifyWorld(t,r),t.parentNode=null}}},{key:"getOrigin",value:function(t){return t.getGeometryBounds(),t.transformable.origin}},{key:"setOrigin",value:function(t,e){"number"==typeof e&&(e=[e,arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,arguments.length>3&&void 0!==arguments[3]?arguments[3]:0]);var n=t.transformable;if(e[0]!==n.origin[0]||e[1]!==n.origin[1]||e[2]!==n.origin[2]){var r=n.origin;r[0]=e[0],r[1]=e[1],r[2]=e[2]||0,this.dirtifyLocal(t,n)}}},{key:"rotate",value:function(t,e){"number"==typeof e&&(e=at(e,arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,arguments.length>3&&void 0!==arguments[3]?arguments[3]:0));var n=t.transformable;if(null!==t.parentNode&&t.parentNode.transformable){var r=ns;Pt(r,e[0],e[1],e[2]);var i=this.getRotation(t),a=this.getRotation(t.parentNode);At(hs,a),Nt(hs,hs),wt(r,hs,r),wt(n.localRotation,r,i),Ot(n.localRotation,n.localRotation),this.dirtifyLocal(t,n)}else this.rotateLocal(t,e)}},{key:"rotateLocal",value:function(t,e){"number"==typeof e&&(e=at(e,arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,arguments.length>3&&void 0!==arguments[3]?arguments[3]:0));var n=t.transformable;Pt(cs,e[0],e[1],e[2]),Rt(n.localRotation,n.localRotation,cs),this.dirtifyLocal(t,n)}},{key:"setEulerAngles",value:function(t,e){"number"==typeof e&&(e=at(e,arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,arguments.length>3&&void 0!==arguments[3]?arguments[3]:0));var n=t.transformable;if(null!==t.parentNode&&t.parentNode.transformable){Pt(n.localRotation,e[0],e[1],e[2]);var r=this.getRotation(t.parentNode);At(ls,Nt(ns,r)),Rt(n.localRotation,n.localRotation,ls),this.dirtifyLocal(t,n)}else this.setLocalEulerAngles(t,e)}},{key:"setLocalEulerAngles",value:function(t,e){var n=4>=arguments.length||void 0===arguments[4]||arguments[4];"number"==typeof e&&(e=at(e,arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,arguments.length>3&&void 0!==arguments[3]?arguments[3]:0));var r=t.transformable;Pt(r.localRotation,e[0],e[1],e[2]),n&&this.dirtifyLocal(t,r)}},{key:"translateLocal",value:function(t,e){"number"==typeof e&&(e=at(e,arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,arguments.length>3&&void 0!==arguments[3]?arguments[3]:0));var n=t.transformable;mt(e,Ko)||(!function(t,e,n){var r=n[0],i=n[1],a=n[2],o=e[0],s=e[1],u=e[2],l=i*u-a*s,c=a*o-r*u,h=r*s-i*o,f=i*h-a*c,d=a*l-r*h,v=r*c-i*l,p=2*n[3];c*=p,h*=p,d*=2,v*=2,t[0]=o+(l*=p)+(f*=2),t[1]=s+c+d,t[2]=u+h+v}(e,e,n.localRotation),ut(n.localPosition,n.localPosition,e),this.dirtifyLocal(t,n))}},{key:"setPosition",value:function(t,e){var n,r=t.transformable;if(ss[0]=e[0],ss[1]=e[1],ss[2]=null!==(n=e[2])&&void 0!==n?n:0,!mt(this.getPosition(t),ss)){if(ot(r.position,ss),null!==t.parentNode&&t.parentNode.transformable)N(us,t.parentNode.transformable.worldTransform),C(us,us),pt(r.localPosition,ss,us);else ot(r.localPosition,ss);this.dirtifyLocal(t,r)}}},{key:"setLocalPosition",value:function(t,e){var n,r=2>=arguments.length||void 0===arguments[2]||arguments[2],i=t.transformable;os[0]=e[0],os[1]=e[1],os[2]=null!==(n=e[2])&&void 0!==n?n:0,mt(i.localPosition,os)||(ot(i.localPosition,os),r&&this.dirtifyLocal(t,i))}},{key:"scaleLocal",value:function(t,e){var n,r,i,a,o=t.transformable;r=o.localScale,i=o.localScale,a=st(ts,e[0],e[1],null!==(n=e[2])&&void 0!==n?n:1),r[0]=i[0]*a[0],r[1]=i[1]*a[1],r[2]=i[2]*a[2],this.dirtifyLocal(t,o)}},{key:"setLocalScale",value:function(t,e){var n,r=2>=arguments.length||void 0===arguments[2]||arguments[2],i=t.transformable;st(ts,e[0],e[1],null!==(n=e[2])&&void 0!==n?n:i.localScale[2]),mt(ts,i.localScale)||(ot(i.localScale,ts),r&&this.dirtifyLocal(t,i))}},{key:"translate",value:function(t,e){"number"==typeof e&&(e=st(ts,e,arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,arguments.length>3&&void 0!==arguments[3]?arguments[3]:0)),mt(e,Ko)||(ut(ts,this.getPosition(t),e),this.setPosition(t,ts))}},{key:"setRotation",value:function(t,e,n,r,i){var a=t.transformable;if("number"==typeof e&&(e=St(e,n,r,i)),null!==t.parentNode&&t.parentNode.transformable){var o=this.getRotation(t.parentNode);At(ns,o),Nt(ns,ns),wt(a.localRotation,ns,e),Ot(a.localRotation,a.localRotation),this.dirtifyLocal(t,a)}else this.setLocalRotation(t,e)}},{key:"setLocalRotation",value:function(t,e,n,r,i){var a=5>=arguments.length||void 0===arguments[5]||arguments[5];"number"==typeof e&&(e=Ct(ns,e,n,r,i));var o=t.transformable;At(o.localRotation,e),a&&this.dirtifyLocal(t,o)}},{key:"setLocalSkew",value:function(t,e,n){var r=3>=arguments.length||void 0===arguments[3]||arguments[3];"number"==typeof e&&(e=function(t,e,n){return t[0]=e,t[1]=n,t}(Jo,e,n));var i,a,o=t.transformable;(i=o.localSkew)[0]=(a=e)[0],i[1]=a[1],r&&this.dirtifyLocal(t,o)}},{key:"dirtifyLocal",value:function(t,e){za(t)||e.localDirtyFlag||(e.localDirtyFlag=!0,e.dirtyFlag||this.dirtifyWorld(t,e))}},{key:"dirtifyWorld",value:function(t,e){e.dirtyFlag||this.unfreezeParentToRoot(t),this.dirtifyWorldInternal(t,e),this.dirtifyToRoot(t,!0)}},{key:"dirtifyFragment",value:function(t){var e=t.transformable;e&&(e.frozen=!1,e.dirtyFlag=!0,e.localDirtyFlag=!0);var n=t.renderable;n&&(n.renderBoundsDirty=!0,n.boundsDirty=!0,n.dirty=!0);for(var r=t.childNodes.length,i=0;r>i;i++)this.dirtifyFragment(t.childNodes[i]);t.nodeName===en.FRAGMENT&&this.pendingEvents.set(t,!1)}},{key:"triggerPendingEvents",value:function(){var t=this,e=new Set,n=function(n,r){n.isConnected&&!e.has(n)&&n.nodeName!==en.FRAGMENT&&(t.boundsChangedEvent.detail=r,t.boundsChangedEvent.target=n,n.isMutationObserved?n.dispatchEvent(t.boundsChangedEvent):n.ownerDocument.defaultView.dispatchEvent(t.boundsChangedEvent,!0),e.add(n))};this.pendingEvents.forEach((function(t,e){e.nodeName!==en.FRAGMENT&&(fs.affectChildren=t,t?e.forEach((function(t){n(t,fs)})):n(e,fs))})),e.clear(),this.clearPendingEvents()}},{key:"clearPendingEvents",value:function(){this.pendingEvents.clear()}},{key:"dirtifyToRoot",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=t;for(n.renderable&&(n.renderable.dirty=!0);n;)jo(n),n=n.parentNode;e&&t.forEach((function(t){jo(t)})),this.informDependentDisplayObjects(t),this.pendingEvents.set(t,e)}},{key:"updateDisplayObjectDependency",value:function(t,e,n,r){if(e&&e!==n){var i=this.displayObjectDependencyMap.get(e);if(i&&i[t]){var a=i[t].indexOf(r);i[t].splice(a,1)}}if(n){var o=this.displayObjectDependencyMap.get(n);o||(this.displayObjectDependencyMap.set(n,{}),o=this.displayObjectDependencyMap.get(n)),o[t]||(o[t]=[]),o[t].push(r)}}},{key:"informDependentDisplayObjects",value:function(t){var e=this,n=this.displayObjectDependencyMap.get(t);n&&Object.keys(n).forEach((function(t){n[t].forEach((function(n){e.dirtifyToRoot(n,!0),n.dispatchEvent(new zo(Uo.ATTR_MODIFIED,n,e,e,t,zo.MODIFICATION,e,e)),n.isCustomElement&&n.isConnected&&n.attributeChangedCallback&&n.attributeChangedCallback(t,e,e)}))}))}},{key:"getPosition",value:function(t){var e=t.transformable;return z(e.position,this.getWorldTransform(t,e))}},{key:"getRotation",value:function(t){var e=t.transformable;return X(e.rotation,this.getWorldTransform(t,e))}},{key:"getScale",value:function(t){var e=t.transformable;return j(e.scaling,this.getWorldTransform(t,e))}},{key:"getWorldTransform",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t.transformable;return e.localDirtyFlag||e.dirtyFlag?(t.parentNode&&t.parentNode.transformable&&this.getWorldTransform(t.parentNode),this.sync(t,e),e.worldTransform):e.worldTransform}},{key:"getLocalPosition",value:function(t){return t.transformable.localPosition}},{key:"getLocalRotation",value:function(t){return t.transformable.localRotation}},{key:"getLocalScale",value:function(t){return t.transformable.localScale}},{key:"getLocalSkew",value:function(t){return t.transformable.localSkew}},{key:"calcLocalTransform",value:function(t){if(0!==t.localSkew[0]||0!==t.localSkew[1]){H(t.localTransform,t.localRotation,t.localPosition,at(1,1,1),t.origin),0===t.localSkew[0]&&0===t.localSkew[1]||(S(es),es[4]=Math.tan(t.localSkew[0]),es[1]=Math.tan(t.localSkew[1]),O(t.localTransform,t.localTransform,es));var e=H(es,Ct(ns,0,0,0,1),st(ts,1,1,1),t.localScale,t.origin);O(t.localTransform,t.localTransform,e)}else{var n=t.localTransform,r=t.localPosition,i=t.localRotation,a=t.localScale,o=t.origin,s=0!==r[0]||0!==r[1]||0!==r[2];1!==i[3]||0!==i[0]||0!==i[1]||0!==i[2]||(1!==a[0]||1!==a[1]||1!==a[2])||(0!==o[0]||0!==o[1]||0!==o[2])?H(n,i,r,a,o):s?D(n,r):S(n)}}},{key:"getLocalTransform",value:function(t){var e=t.transformable;return e.localDirtyFlag&&(this.calcLocalTransform(e),e.localDirtyFlag=!1),e.localTransform}},{key:"setLocalTransform",value:function(t,e){var n=z(rs,e),r=X(is,e),i=j(as,e);this.setLocalScale(t,i,!1),this.setLocalPosition(t,n,!1),this.setLocalRotation(t,r,void 0,void 0,void 0,!1),this.dirtifyLocal(t,t.transformable)}},{key:"resetLocalTransform",value:function(t){this.setLocalScale(t,Qo,!1),this.setLocalPosition(t,Ko,!1),this.setLocalEulerAngles(t,Ko,void 0,void 0,!1),this.setLocalSkew(t,Zo,void 0,!1),this.dirtifyLocal(t,t.transformable)}},{key:"getTransformedGeometryBounds",value:function(t){var e=arguments.length>2?arguments[2]:void 0,n=this.getGeometryBounds(t,arguments.length>1&&void 0!==arguments[1]&&arguments[1]);if(!fn.isEmpty(n)){var r=e||new fn;return r.setFromTransformedAABB(n,this.getWorldTransform(t)),r}return null}},{key:"getGeometryBounds",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=t.geometry;return n.dirty&&ks.styleValueRegistry.updateGeometry(t),(e?n.renderBounds:n.contentBounds||null)||new fn}},{key:"getBounds",value:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=t.renderable;if(!r.boundsDirty&&!n&&r.bounds)return r.bounds;if(!r.renderBoundsDirty&&n&&r.renderBounds)return r.renderBounds;var i=n?r.renderBounds:r.bounds,a=this.getTransformedGeometryBounds(t,n,i);if(t.childNodes.forEach((function(t){var r=e.getBounds(t,n);r&&(a?a.add(r):(a=i||new fn).update(r.center,r.halfExtents))})),a||(a=new fn),n){var o=Ba(t);if(o){var s=o.parsedStyle.clipPath.getBounds(n);a?s&&(a=s.intersection(a)):a.update(s.center,s.halfExtents)}}return n?(r.renderBounds=a,r.renderBoundsDirty=!1):(r.bounds=a,r.boundsDirty=!1),a}},{key:"getLocalBounds",value:function(t){if(t.parentNode){var e=$o;t.parentNode.transformable&&(e=C(es,this.getWorldTransform(t.parentNode)));var n=this.getBounds(t);if(!fn.isEmpty(n)){var r=new fn;return r.setFromTransformedAABB(n,e),r}}return this.getBounds(t)}},{key:"getBoundingClientRect",value:function(t){var e,n,r=this.getGeometryBounds(t);fn.isEmpty(r)||(n=new fn).setFromTransformedAABB(r,this.getWorldTransform(t));var i=null===(e=t.ownerDocument)||void 0===e||null===(e=e.defaultView)||void 0===e?void 0:e.getContextService().getBoundingClientRect();if(n){var a=g(n.getMin(),2),o=a[0],s=a[1],u=g(n.getMax(),2);return new yn(o+((null==i?void 0:i.left)||0),s+((null==i?void 0:i.top)||0),u[0]-o,u[1]-s)}return new yn((null==i?void 0:i.left)||0,(null==i?void 0:i.top)||0,0,0)}},{key:"dirtifyWorldInternal",value:function(t,e){var n=this;if(!e.dirtyFlag){e.dirtyFlag=!0,e.frozen=!1,t.childNodes.forEach((function(t){var e=t.transformable;e.dirtyFlag||n.dirtifyWorldInternal(t,e)}));var r=t.renderable;r&&(r.renderBoundsDirty=!0,r.boundsDirty=!0,r.dirty=!0)}}},{key:"syncHierarchy",value:function(t){var e=t.transformable;if(!e.frozen){e.frozen=!0,(e.localDirtyFlag||e.dirtyFlag)&&this.sync(t,e);for(var n=t.childNodes,r=0;n.length>r;r++)this.syncHierarchy(n[r])}}},{key:"sync",value:function(t,e){if(e.localDirtyFlag&&(this.calcLocalTransform(e),e.localDirtyFlag=!1),e.dirtyFlag){var n=t.parentNode,r=n&&n.transformable;null!==n&&r?O(e.worldTransform,r.worldTransform,e.localTransform):N(e.worldTransform,e.localTransform),e.dirtyFlag=!1}}},{key:"unfreezeParentToRoot",value:function(t){for(var e=t.parentNode;e;){var n=e.transformable;n&&(n.frozen=!1),e=e.parentNode}}}])}(),vs={MetricsString:"|ÉqÅ",BaselineSymbol:"M",BaselineMultiplier:1.4,HeightMultiplier:2,Newlines:[10,13],BreakingSpaces:[9,32,8192,8193,8194,8195,8196,8197,8198,8200,8201,8202,8287,12288]},ps=/[a-zA-Z0-9\u00C0-\u00D6\u00D8-\u00f6\u00f8-\u00ff!"#$%&'()*+,-./:;]/,ms=RegExp("".concat(/[!%),.:;?\]}¢°·'""†‡›℃∶、。〃〆〕〗〞﹚﹜！＂％＇），．：；？！］｝～]/.source,"|").concat(/[!),.:;?\]}¢·–—'"•"、。〆〞〕〉》」︰︱︲︳﹐﹑﹒﹓﹔﹕﹖﹘﹚﹜！），．：；？︶︸︺︼︾﹀﹂﹗］｜｝､]/.source,"|").concat(/[)\]｝〕〉》」』】〙〗〟'"｠»ヽヾーァィゥェォッャュョヮヵヶぁぃぅぇぉっゃゅょゎゕゖㇰㇱㇲㇳㇴㇵㇶㇷㇸㇹㇺㇻㇼㇽㇾㇿ々〻‐゠–〜?!‼⁇⁈⁉・、:;,。.]/.source,"|").concat(/[!%),.:;?\]}¢°'"†‡℃〆〈《「『〕！％），．：；？］｝]/.source)),ys=RegExp("".concat(/[$(£¥·'"〈《「『【〔〖〝﹙﹛＄（．［｛￡￥]/.source,"|").concat(/[([{£¥'"‵〈《「『〔〝︴﹙﹛（｛︵︷︹︻︽︿﹁﹃﹏]/.source,"|").concat(/[([｛〔〈《「『【〘〖〝'"｟«—...‥〳〴〵]/.source,"|").concat(/[$([{£¥'"々〇〉》」〔＄（［｛｠￥￦#]/.source)),gs=function(){return u((function t(e){var n=this;o(this,t),this.fontMetricsCache={},this.shouldBreakByKinsokuShorui=function(t,e){return!n.isBreakingSpace(e)&&!(!t||!ys.exec(e)&&!ms.exec(t))},this.trimByKinsokuShorui=function(t){var e=h(t),n=e[e.length-2];if(!n)return t;var r=n[n.length-1];return e[e.length-2]=n.slice(0,-1),e[e.length-1]=r+e[e.length-1],e},this.runtime=e}),[{key:"measureFont",value:function(t,e){if(this.fontMetricsCache[t])return this.fontMetricsCache[t];var n={ascent:0,descent:0,fontSize:0},r=this.runtime.offscreenCanvasCreator.getOrCreateCanvas(e),i=this.runtime.offscreenCanvasCreator.getOrCreateContext(e,{willReadFrequently:!0});i.font=t;var a=vs.MetricsString+vs.BaselineSymbol,o=Math.ceil(i.measureText(a).width),s=Math.ceil(i.measureText(vs.BaselineSymbol).width),u=vs.HeightMultiplier*s;s=s*vs.BaselineMultiplier|0,r.width=o,r.height=u,i.fillStyle="#f00",i.fillRect(0,0,o,u),i.font=t,i.textBaseline="alphabetic",i.fillStyle="#000",i.fillText(a,0,s);var l=i.getImageData(0,0,o||1,u||1).data,c=l.length,h=4*o,f=0,d=0,v=!1;for(f=0;s>f;++f){for(var p=0;h>p;p+=4)if(255!==l[d+p]){v=!0;break}if(v)break;d+=h}for(n.ascent=s-f,d=c-h,v=!1,f=u;f>s;--f){for(var m=0;h>m;m+=4)if(255!==l[d+m]){v=!0;break}if(v)break;d-=h}return n.descent=f-s,n.fontSize=n.ascent+n.descent,this.fontMetricsCache[t]=n,n}},{key:"measureText",value:function(t,e,n){var r=e.fontSize,i=void 0===r?16:r,a=e.wordWrap,o=void 0!==a&&a,s=e.lineHeight,u=e.lineWidth,l=void 0===u?1:u,c=e.textBaseline,h=void 0===c?"alphabetic":c,f=e.textAlign,d=void 0===f?"start":f,v=e.letterSpacing,p=void 0===v?0:v,m=e.textPath,y=e.leading,g=void 0===y?0:y,k=function(t){for(var e=t.fontSize,n=void 0===e?16:e,r=t.fontFamily,i=void 0===r?"sans-serif":r,a=t.fontStyle,o=void 0===a?"normal":a,s=t.fontVariant,u=void 0===s?"normal":s,l=t.fontWeight,c=void 0===l?"normal":l,h=zt(n)&&"".concat(n,"px")||"16px",f=i.split(","),d=f.length-1;d>=0;d--){var v=f[d].trim();!oo.test(v)&&0>ao.indexOf(v)&&(v='"'.concat(v,'"')),f[d]=v}return"".concat(o," ").concat(u," ").concat(c," ").concat(h," ").concat(f.join(","))}(e),E=this.measureFont(k,n);0===E.fontSize&&(E.fontSize=i,E.ascent=i);var x=this.runtime.offscreenCanvasCreator.getOrCreateContext(n);x.font=k,e.isOverflowing=!1;var b=(o?this.wordWrap(t,e,n):t).split(/(?:\r\n|\r|\n)/),T=Array(b.length),M=0;if(!m){for(var w=0;b.length>w;w++){var N=x.measureText(b[w]).width+(b[w].length-1)*p;T[w]=N,M=Math.max(M,N)}var P=M+l,S=s||E.fontSize+l,A=Math.max(S,E.fontSize+l)+(b.length-1)*(S+g),C=0;return"middle"===h?C=-A/2:"bottom"===h||"alphabetic"===h||"ideographic"===h?C=-A:"top"!==h&&"hanging"!==h||(C=0),{font:k,width:P,height:A,lines:b,lineWidths:T,lineHeight:S+=g,maxLineWidth:M,fontProperties:E,lineMetrics:T.map((function(t,e){var n=0;return"center"===d||"middle"===d?n-=t/2:"right"!==d&&"end"!==d||(n-=t),new yn(n-l/2,C+e*S,t+l,S)}))}}m.getTotalLength();for(var R=0;b.length>R;R++)x.measureText(b[R])}},{key:"wordWrap",value:function(t,e,n){var r=this,i=this,a=e.wordWrapWidth,o=void 0===a?0:a,s=e.letterSpacing,u=void 0===s?0:s,l=e.maxLines,c=void 0===l?1/0:l,h=e.textOverflow,f=this.runtime.offscreenCanvasCreator.getOrCreateContext(n),d=o+u,v="";"ellipsis"===h?v="...":h&&"clip"!==h&&(v=h);var p=Array.from(t),m=[],y=0,g=0,k=-1,E={},x=function(t){return r.getFromCache(t,u,E,f)},b=x(v);function T(t,e,n,r){for(;x(t)<r&&p.length-1>e&&!i.isNewline(p[e+1]);)t+=p[e+=1];for(;x(t)>r&&e>=n;)e-=1,t=t.slice(0,-1);return{lineTxt:t,txtLastCharIndex:e}}function M(t,e){if(b>0&&d>=b)if(m[t]){var n=T(m[t],e,k+1,d-b);m[t]=n.lineTxt+v}else m[t]=v}for(var w=0;p.length>w;w++){var N=p[w],P=p[w-1],S=p[w+1],A=x(N);if(this.isNewline(N)){if(y+1>=c){e.isOverflowing=!0,p.length-1>w&&M(y,w-1);break}k=w-1,g=0,m[y+=1]=""}else{if(g>0&&g+A>d){var C=T(m[y],w-1,k+1,d);if(C.txtLastCharIndex!==w-1){if(m[y]=C.lineTxt,C.txtLastCharIndex===p.length-1)break;P=p[(w=C.txtLastCharIndex+1)-1],S=p[w+1],A=x(N=p[w])}if(y+1>=c){e.isOverflowing=!0,M(y,w-1);break}if(k=w-1,g=0,m[y+=1]="",this.isBreakingSpace(N))continue;this.canBreakInLastChar(N)||(m=this.trimToBreakable(m),g=this.sumTextWidthByCache(m[y]||"",x)),this.shouldBreakByKinsokuShorui(N,S)&&(m=this.trimByKinsokuShorui(m),g+=x(P||""))}g+=A,m[y]=(m[y]||"")+N}}return m.join("\n")}},{key:"isBreakingSpace",value:function(t){return"string"==typeof t&&vs.BreakingSpaces.indexOf(t.charCodeAt(0))>=0}},{key:"isNewline",value:function(t){return"string"==typeof t&&vs.Newlines.indexOf(t.charCodeAt(0))>=0}},{key:"trimToBreakable",value:function(t){var e=h(t),n=e[e.length-2],r=this.findBreakableIndex(n);if(-1===r||!n)return e;var i=n.slice(r,r+1),a=r+(this.isBreakingSpace(i)?0:1);return e[e.length-1]+=n.slice(r+1,n.length),e[e.length-2]=n.slice(0,a),e}},{key:"canBreakInLastChar",value:function(t){return!t||!ps.test(t)}},{key:"sumTextWidthByCache",value:function(t,e){return t.split("").reduce((function(t,n){return t+e(n)}),0)}},{key:"findBreakableIndex",value:function(t){for(var e=t.length-1;e>=0;e--)if(!ps.test(t[e]))return e;return-1}},{key:"getFromCache",value:function(t,e,n,r){var i=n[t];if("number"!=typeof i){var a=t.length*e,o=r.measureText(t);n[t]=i=o.width+a}return i}}])}(),ks={},Es=(Ho=new Mo,Wo=new To,r(r(r(r(r(r(r(r(r(r(Xo={},en.FRAGMENT,null),en.CIRCLE,new ko),en.ELLIPSE,new Eo),en.RECT,Ho),en.IMAGE,Ho),en.GROUP,new No),en.LINE,new xo),en.TEXT,new wo(ks)),en.POLYLINE,Wo),en.POLYGON,Wo),r(r(r(Xo,en.PATH,new bo),en.HTML,new Po),en.MESH,null)),xs=function(t){var e=new Ea,n=new Ta;return r(r(r(r(r(r(r(r(r(r(t={},Wn.PERCENTAGE,null),Wn.NUMBER,new Pa),Wn.ANGLE,new ga),Wn.DEFINED_PATH,new ka),Wn.PAINT,e),Wn.COLOR,e),Wn.FILTER,new xa),Wn.LENGTH,n),Wn.LENGTH_PERCENTAGE,n),Wn.LENGTH_PERCENTAGE_12,new Ma),r(r(r(r(r(r(r(r(r(r(t,Wn.LENGTH_PERCENTAGE_14,new wa),Wn.COORDINATE,new Ta),Wn.OFFSET_DISTANCE,new Sa),Wn.OPACITY_VALUE,new Aa),Wn.PATH,new Ca),Wn.LIST_OF_POINTS,new Ra),Wn.SHADOW_BLUR,new Oa),Wn.TEXT,new La),Wn.TEXT_TRANSFORM,new Ia),Wn.TRANSFORM,new mo),r(r(r(t,Wn.TRANSFORM_ORIGIN,new yo),Wn.Z_INDEX,new go),Wn.MARKER,new Na)}();ks.CameraContribution=zn,ks.AnimationTimeline=null,ks.EasingFunction=null,ks.offscreenCanvasCreator=new Fo,ks.sceneGraphSelector=new Yo,ks.sceneGraphService=new ds(ks),ks.textService=new gs(ks),ks.geometryUpdaterFactory=Es,ks.CSSPropertySyntaxFactory=xs,ks.styleValueRegistry=new ma(ks),ks.layoutRegistry=null,ks.globalThis="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{},ks.enableStyleSyntax=!0,ks.enableSizeAttenuation=!1;var bs=0;var Ts=new zo(Uo.INSERTED,null,"","","",0,"",""),Ms=new zo(Uo.REMOVED,null,"","","",0,"",""),ws=new Oo(Uo.DESTROY),Ns=function(t){function e(){var t;o(this,e);for(var n=arguments.length,r=Array(n),i=0;n>i;i++)r[i]=arguments[i];return(t=p(this,e,[].concat(r))).entity=bs++,t.renderable={bounds:void 0,boundsDirty:!0,renderBounds:void 0,renderBoundsDirty:!0,dirtyRenderBounds:void 0,dirty:!1},t.cullable={strategy:jn.Standard,visibilityPlaneMask:-1,visible:!0,enable:!0},t.transformable={dirtyFlag:!1,localDirtyFlag:!1,frozen:!1,localPosition:[0,0,0],localRotation:[0,0,0,1],localScale:[1,1,1],localTransform:[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1],localSkew:[0,0],position:[0,0,0],rotation:[0,0,0,1],scaling:[1,1,1],worldTransform:[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1],origin:[0,0,0]},t.sortable={dirty:!1,sorted:void 0,renderOrder:0,dirtyChildren:[],dirtyReason:void 0},t.geometry={contentBounds:void 0,renderBounds:void 0,dirty:!0},t.rBushNode={aabb:void 0},t.namespaceURI="g",t.scrollLeft=0,t.scrollTop=0,t.clientTop=0,t.clientLeft=0,t.destroyed=!1,t.style={},t.computedStyle={},t.parsedStyle={},t.attributes={},t}return y(e,t),u(e,[{key:"className",get:function(){return this.getAttribute("class")||""},set:function(t){this.setAttribute("class",t)}},{key:"classList",get:function(){return this.className.split(" ").filter((function(t){return""!==t}))}},{key:"tagName",get:function(){return this.nodeName}},{key:"children",get:function(){return this.childNodes}},{key:"childElementCount",get:function(){return this.childNodes.length}},{key:"firstElementChild",get:function(){return this.firstChild}},{key:"lastElementChild",get:function(){return this.lastChild}},{key:"parentElement",get:function(){return this.parentNode}},{key:"nextSibling",get:function(){if(this.parentNode){var t=this.parentNode.childNodes.indexOf(this);return this.parentNode.childNodes[t+1]||null}return null}},{key:"previousSibling",get:function(){if(this.parentNode){var t=this.parentNode.childNodes.indexOf(this);return this.parentNode.childNodes[t-1]||null}return null}},{key:"cloneNode",value:function(t){throw Error(gn)}},{key:"appendChild",value:function(t,e){var n;if(t.destroyed)throw Error("Cannot append a destroyed element.");return ks.sceneGraphService.attach(t,this,e),null!==(n=this.ownerDocument)&&void 0!==n&&n.defaultView&&(za(this)||t.nodeName!==en.FRAGMENT?this.ownerDocument.defaultView.mountChildren(t):this.ownerDocument.defaultView.mountFragment(t)),this.isMutationObserved&&(Ts.relatedNode=this,t.dispatchEvent(Ts)),t}},{key:"insertBefore",value:function(t,e){if(e){t.parentElement&&t.parentElement.removeChild(t);var n=this.childNodes.indexOf(e);-1===n?this.appendChild(t):this.appendChild(t,n)}else this.appendChild(t);return t}},{key:"replaceChild",value:function(t,e){var n=this.childNodes.indexOf(e);return this.removeChild(e),this.appendChild(t,n),e}},{key:"removeChild",value:function(t){var e;return Ms.relatedNode=this,t.dispatchEvent(Ms),null!==(e=t.ownerDocument)&&void 0!==e&&e.defaultView&&t.ownerDocument.defaultView.unmountChildren(t),ks.sceneGraphService.detach(t),t}},{key:"removeChildren",value:function(){for(var t=this.childNodes.length-1;t>=0;t--){this.removeChild(this.childNodes[t])}}},{key:"destroyChildren",value:function(){for(var t=this.childNodes.length-1;t>=0;t--){var e=this.childNodes[t];e.childNodes.length>0&&e.destroyChildren(),e.destroy()}}},{key:"matches",value:function(t){return ks.sceneGraphService.matches(t,this)}},{key:"getElementById",value:function(t){return ks.sceneGraphService.querySelector("#".concat(t),this)}},{key:"getElementsByName",value:function(t){return ks.sceneGraphService.querySelectorAll('[name="'.concat(t,'"]'),this)}},{key:"getElementsByClassName",value:function(t){return ks.sceneGraphService.querySelectorAll(".".concat(t),this)}},{key:"getElementsByTagName",value:function(t){return ks.sceneGraphService.querySelectorAll(t,this)}},{key:"querySelector",value:function(t){return ks.sceneGraphService.querySelector(t,this)}},{key:"querySelectorAll",value:function(t){return ks.sceneGraphService.querySelectorAll(t,this)}},{key:"closest",value:function(t){var e=this;do{if(ks.sceneGraphService.matches(t,e))return e;e=e.parentElement}while(null!==e);return null}},{key:"find",value:function(t){var e=this,n=null;return this.forEach((function(r){return r===e||!t(r)||(n=r,!1)})),n}},{key:"findAll",value:function(t){var e=this,n=[];return this.forEach((function(r){r!==e&&t(r)&&n.push(r)})),n}},{key:"after",value:function(){var t=this;if(this.parentNode){for(var e=this.parentNode.childNodes.indexOf(this),n=arguments.length,r=Array(n),i=0;n>i;i++)r[i]=arguments[i];r.forEach((function(n,r){var i;return null===(i=t.parentNode)||void 0===i?void 0:i.appendChild(n,e+r+1)}))}}},{key:"before",value:function(){if(this.parentNode){for(var t,e=this.parentNode.childNodes.indexOf(this),n=arguments.length,r=Array(n),i=0;n>i;i++)r[i]=arguments[i];var a=r[0],o=r.slice(1);this.parentNode.appendChild(a,e),(t=a).after.apply(t,h(o))}}},{key:"replaceWith",value:function(){this.after.apply(this,arguments),this.remove()}},{key:"append",value:function(){for(var t=this,e=arguments.length,n=Array(e),r=0;e>r;r++)n[r]=arguments[r];n.forEach((function(e){return t.appendChild(e)}))}},{key:"prepend",value:function(){for(var t=this,e=arguments.length,n=Array(e),r=0;e>r;r++)n[r]=arguments[r];n.forEach((function(e,n){return t.appendChild(e,n)}))}},{key:"replaceChildren",value:function(){for(;this.childNodes.length&&this.firstChild;)this.removeChild(this.firstChild);this.append.apply(this,arguments)}},{key:"remove",value:function(){return this.parentNode?this.parentNode.removeChild(this):this}},{key:"destroy",value:function(){this.destroyChildren(),this.dispatchEvent(ws),this.remove(),this.emitter.removeAllListeners(),this.destroyed=!0}},{key:"getGeometryBounds",value:function(){return ks.sceneGraphService.getGeometryBounds(this)}},{key:"getRenderBounds",value:function(){return ks.sceneGraphService.getBounds(this,!0)}},{key:"getBounds",value:function(){return ks.sceneGraphService.getBounds(this)}},{key:"getLocalBounds",value:function(){return ks.sceneGraphService.getLocalBounds(this)}},{key:"getBoundingClientRect",value:function(){return ks.sceneGraphService.getBoundingClientRect(this)}},{key:"getClientRects",value:function(){return[this.getBoundingClientRect()]}},{key:"computedStyleMap",value:function(){return new Map(Object.entries(this.computedStyle))}},{key:"getAttributeNames",value:function(){return Object.keys(this.attributes)}},{key:"getAttribute",value:function(t){if("symbol"!=typeof t){var e=this.attributes[t];return e}}},{key:"hasAttribute",value:function(t){return this.getAttributeNames().includes(t)}},{key:"hasAttributes",value:function(){return!!this.getAttributeNames().length}},{key:"removeAttribute",value:function(t){this.setAttribute(t,null),delete this.attributes[t]}},{key:"setAttribute",value:function(t,e,n,r){this.attributes[t]=e}},{key:"getAttributeNS",value:function(t,e){throw Error(gn)}},{key:"getAttributeNode",value:function(t){throw Error(gn)}},{key:"getAttributeNodeNS",value:function(t,e){throw Error(gn)}},{key:"hasAttributeNS",value:function(t,e){throw Error(gn)}},{key:"removeAttributeNS",value:function(t,e){throw Error(gn)}},{key:"removeAttributeNode",value:function(t){throw Error(gn)}},{key:"setAttributeNS",value:function(t,e,n){throw Error(gn)}},{key:"setAttributeNode",value:function(t){throw Error(gn)}},{key:"setAttributeNodeNS",value:function(t){throw Error(gn)}},{key:"toggleAttribute",value:function(t,e){throw Error(gn)}}])}(_o);function Ps(t){return!(null==t||!t.nodeName)}var Ss=ks.globalThis.Proxy?ks.globalThis.Proxy:function(){},As=new zo(Uo.ATTR_MODIFIED,null,null,null,null,zo.MODIFICATION,null,null),Cs=nt(),Rs=Tt(),Os=function(t){function e(t){var n;return o(this,e),(n=p(this,e)).isCustomElement=!1,n.isMutationObserved=!1,n.activeAnimations=[],n.config=t,n.id=t.id||"",n.name=t.name||"",(t.className||t.class)&&(n.className=t.className||t.class),n.nodeName=t.type||en.GROUP,t.initialParsedStyle&&Object.assign(n.parsedStyle,t.initialParsedStyle),n.initAttributes(t.style),ks.enableStyleSyntax&&(n.style=new Ss({setProperty:function(t,e){n.setAttribute(t,e)},getPropertyValue:function(t){return n.getAttribute(t)},removeProperty:function(t){n.removeAttribute(t)},item:function(){return""}},{get:function(t,e){return void 0!==t[e]?t[e]:n.getAttribute(e)},set:function(t,e,r){return n.setAttribute(e,r),!0}})),n}return y(e,t),u(e,[{key:"destroy",value:function(){Le(e,"destroy",this,3)([]),this.getAnimations().forEach((function(t){t.cancel()}))}},{key:"cloneNode",value:function(t,e){var n=a({},this.attributes);for(var r in n){var i=n[r];Ps(i)&&"clipPath"!==r&&"offsetPath"!==r&&"textPath"!==r&&(n[r]=i.cloneNode(t)),e&&(n[r]=e(r,i))}var o=new this.constructor(a(a({},this.config),{},{style:n}));return o.setLocalTransform(this.getLocalTransform()),t&&this.children.forEach((function(e){if(!e.style.isMarker){var n=e.cloneNode(t);o.appendChild(n)}})),o}},{key:"initAttributes",value:function(){ks.styleValueRegistry.processProperties(this,arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{forceUpdateGeometry:!0}),this.renderable.dirty=!0}},{key:"setAttribute",value:function(t,n){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=3>=arguments.length||void 0===arguments[3]||arguments[3];Wt(n)||(r||n!==this.attributes[t])&&(this.internalSetAttribute(t,n,{memoize:i}),Le(e,"setAttribute",this,3)([t,n]))}},{key:"internalSetAttribute",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=this.renderable,a=this.attributes[t],o=this.parsedStyle[t];ks.styleValueRegistry.processProperties(this,r({},t,e),n),i.dirty=!0;var s,u=this.parsedStyle[t];(this.isConnected&&(As.relatedNode=this,As.prevValue=a,As.newValue=e,As.attrName=t,As.prevParsedValue=o,As.newParsedValue=u,this.isMutationObserved?this.dispatchEvent(As):(As.target=this,this.ownerDocument.defaultView.dispatchEvent(As,!0))),this.isCustomElement&&this.isConnected||!this.isCustomElement)&&(null===(s=this.attributeChangedCallback)||void 0===s||s.call(this,t,a,e,o,u))}},{key:"getBBox",value:function(){var t=this.getBounds(),e=g(t.getMin(),2),n=e[0],r=e[1],i=g(t.getMax(),2);return new yn(n,r,i[0]-n,i[1]-r)}},{key:"setOrigin",value:function(t){return ks.sceneGraphService.setOrigin(this,bn(t,arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,!1)),this}},{key:"getOrigin",value:function(){return ks.sceneGraphService.getOrigin(this)}},{key:"setPosition",value:function(t){return ks.sceneGraphService.setPosition(this,bn(t,arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,!1)),this}},{key:"setLocalPosition",value:function(t){return ks.sceneGraphService.setLocalPosition(this,bn(t,arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,!1)),this}},{key:"translate",value:function(t){return ks.sceneGraphService.translate(this,bn(t,arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,!1)),this}},{key:"translateLocal",value:function(t){return ks.sceneGraphService.translateLocal(this,bn(t,arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,!1)),this}},{key:"getPosition",value:function(){return ks.sceneGraphService.getPosition(this)}},{key:"getLocalPosition",value:function(){return ks.sceneGraphService.getLocalPosition(this)}},{key:"scale",value:function(t,e,n){return this.scaleLocal(t,e,n)}},{key:"scaleLocal",value:function(t,e,n){return"number"==typeof t&&(t=bn(t,e=e||t,n=n||t,!1)),ks.sceneGraphService.scaleLocal(this,t),this}},{key:"setLocalScale",value:function(t,e,n){return"number"==typeof t&&(t=bn(t,e=e||t,n=n||t,!1)),ks.sceneGraphService.setLocalScale(this,t),this}},{key:"getLocalScale",value:function(){return ks.sceneGraphService.getLocalScale(this)}},{key:"getScale",value:function(){return ks.sceneGraphService.getScale(this)}},{key:"getEulerAngles",value:function(){return Nn(g(An(Cs,ks.sceneGraphService.getWorldTransform(this)),3)[2])}},{key:"getLocalEulerAngles",value:function(){return Nn(g(An(Cs,ks.sceneGraphService.getLocalRotation(this)),3)[2])}},{key:"setEulerAngles",value:function(t){return ks.sceneGraphService.setEulerAngles(this,0,0,t),this}},{key:"setLocalEulerAngles",value:function(t){return ks.sceneGraphService.setLocalEulerAngles(this,0,0,t),this}},{key:"rotateLocal",value:function(t,e,n){return _t(e)&&_t(n)?ks.sceneGraphService.rotateLocal(this,0,0,t):ks.sceneGraphService.rotateLocal(this,t,e,n),this}},{key:"rotate",value:function(t,e,n){return _t(e)&&_t(n)?ks.sceneGraphService.rotate(this,0,0,t):ks.sceneGraphService.rotate(this,t,e,n),this}},{key:"setRotation",value:function(t,e,n,r){return ks.sceneGraphService.setRotation(this,t,e,n,r),this}},{key:"setLocalRotation",value:function(t,e,n,r){return ks.sceneGraphService.setLocalRotation(this,t,e,n,r),this}},{key:"setLocalSkew",value:function(t,e){return ks.sceneGraphService.setLocalSkew(this,t,e),this}},{key:"getRotation",value:function(){return ks.sceneGraphService.getRotation(this)}},{key:"getLocalRotation",value:function(){return ks.sceneGraphService.getLocalRotation(this)}},{key:"getLocalSkew",value:function(){return ks.sceneGraphService.getLocalSkew(this)}},{key:"getLocalTransform",value:function(){return ks.sceneGraphService.getLocalTransform(this)}},{key:"getWorldTransform",value:function(){return ks.sceneGraphService.getWorldTransform(this)}},{key:"setLocalTransform",value:function(t){return ks.sceneGraphService.setLocalTransform(this,t),this}},{key:"resetLocalTransform",value:function(){ks.sceneGraphService.resetLocalTransform(this)}},{key:"getAnimations",value:function(){return this.activeAnimations}},{key:"animate",value:function(t,e){var n,r=null===(n=this.ownerDocument)||void 0===n?void 0:n.timeline;return r?r.play(this,t,e):null}},{key:"isVisible",value:function(){var t;return"hidden"!==(null===(t=this.parsedStyle)||void 0===t?void 0:t.visibility)}},{key:"interactive",get:function(){return this.isInteractive()},set:function(t){this.style.pointerEvents=t?"auto":"none"}},{key:"isInteractive",value:function(){var t;return"none"!==(null===(t=this.parsedStyle)||void 0===t?void 0:t.pointerEvents)}},{key:"isCulled",value:function(){return!(!this.cullable||!this.cullable.enable||this.cullable.visible)}},{key:"toFront",value:function(){return this.parentNode&&(this.style.zIndex=Math.max.apply(Math,h(this.parentNode.children.map((function(t){return Number(t.style.zIndex)}))))+1),this}},{key:"toBack",value:function(){return this.parentNode&&(this.style.zIndex=Math.min.apply(Math,h(this.parentNode.children.map((function(t){return Number(t.style.zIndex)}))))-1),this}},{key:"getConfig",value:function(){return this.config}},{key:"attr",value:function(){for(var t=this,e=arguments.length,n=Array(e),r=0;e>r;r++)n[r]=arguments[r];var i=n[0],a=n[1];return i?function(t){var e=typeof t;return null!==t&&"object"===e||"function"===e}(i)?(Object.keys(i).forEach((function(e){t.setAttribute(e,i[e])})),this):2===n.length?(this.setAttribute(i,a),this):this.attributes[i]:this.attributes}},{key:"getMatrix",value:function(t){var e=t||this.getWorldTransform(),n=g(z(Cs,e),2),r=n[0],i=n[1],a=g(j(Cs,e),2),o=a[0],s=a[1],u=X(Rs,e),l=g(An(Cs,u),3);return Cn(l[0]||l[2],r,i,o,s)}},{key:"getLocalMatrix",value:function(){return this.getMatrix(this.getLocalTransform())}},{key:"setMatrix",value:function(t){var e=g(Rn(t),5),n=e[0],r=e[1],i=e[2],a=e[3];this.setEulerAngles(e[4]).setPosition(n,r).setLocalScale(i,a)}},{key:"setLocalMatrix",value:function(t){var e=g(Rn(t),5),n=e[0],r=e[1],i=e[2],a=e[3];this.setLocalEulerAngles(e[4]).setLocalPosition(n,r).setLocalScale(i,a)}},{key:"show",value:function(){this.forEach((function(t){t.style.visibility="visible"}))}},{key:"hide",value:function(){this.forEach((function(t){t.style.visibility="hidden"}))}},{key:"getCount",value:function(){return this.childElementCount}},{key:"getParent",value:function(){return this.parentElement}},{key:"getChildren",value:function(){return this.children}},{key:"getFirst",value:function(){return this.firstElementChild}},{key:"getLast",value:function(){return this.lastElementChild}},{key:"getChildByIndex",value:function(t){return this.children[t]||null}},{key:"add",value:function(t,e){return this.appendChild(t,e)}},{key:"set",value:function(t,e){this.config[t]=e}},{key:"get",value:function(t){return this.config[t]}},{key:"moveTo",value:function(t){return this.setPosition(t,arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,arguments.length>2&&void 0!==arguments[2]?arguments[2]:0),this}},{key:"move",value:function(t){return this.setPosition(t,arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,arguments.length>2&&void 0!==arguments[2]?arguments[2]:0),this}},{key:"setZIndex",value:function(t){return this.style.zIndex=t,this}}])}(Ns);Os.PARSED_STYLE_LIST=new Set(["class","className","clipPath","cursor","display","draggable","droppable","fill","fillOpacity","fillRule","filter","increasedLineWidthForHitTesting","lineCap","lineDash","lineDashOffset","lineJoin","lineWidth","miterLimit","hitArea","offsetDistance","offsetPath","offsetX","offsetY","opacity","pointerEvents","shadowColor","shadowType","shadowBlur","shadowOffsetX","shadowOffsetY","stroke","strokeOpacity","strokeWidth","strokeLinecap","strokeLineJoin","strokeDasharray","strokeDashoffset","transform","transformOrigin","textTransform","visibility","zIndex"]);var Ls=function(t){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return o(this,e),p(this,e,[a({type:en.CIRCLE},t)])}return y(e,t),u(e)}(Os);Ls.PARSED_STYLE_LIST=new Set([].concat(h(Os.PARSED_STYLE_LIST),["cx","cy","cz","r","isBillboard","isSizeAttenuation"]));var Is=["style"],_s=function(t){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=n.style,i=$e(n,Is);return o(this,e),(t=p(this,e,[a({style:r},i)])).isCustomElement=!0,t}return y(e,t),u(e)}(Os);_s.PARSED_STYLE_LIST=new Set(["class","className","clipPath","cursor","draggable","droppable","opacity","pointerEvents","transform","transformOrigin","zIndex","visibility"]);var Ds=function(t){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return o(this,e),p(this,e,[a({type:en.ELLIPSE},t)])}return y(e,t),u(e)}(Os);Ds.PARSED_STYLE_LIST=new Set([].concat(h(Os.PARSED_STYLE_LIST),["cx","cy","cz","rx","ry","isBillboard","isSizeAttenuation"]));var Fs=function(t){function e(){return o(this,e),p(this,e,[{type:en.FRAGMENT}])}return y(e,t),u(e)}(Os);Fs.PARSED_STYLE_LIST=new Set(["class","className"]);var Gs=function(t){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return o(this,e),p(this,e,[a({type:en.GROUP},t)])}return y(e,t),u(e)}(Os);Gs.PARSED_STYLE_LIST=new Set(["class","className","clipPath","cursor","draggable","droppable","opacity","pointerEvents","transform","transformOrigin","zIndex","visibility"]);var Bs=["style"],Vs=function(t){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=n.style,i=$e(n,Bs);return o(this,e),(t=p(this,e,[a({type:en.HTML,style:r},i)])).cullable.enable=!1,t}return y(e,t),u(e,[{key:"getDomElement",value:function(){return this.parsedStyle.$el}},{key:"getClientRects",value:function(){return[this.getBoundingClientRect()]}},{key:"getLocalBounds",value:function(){if(this.parentNode){var t=C(M(),this.parentNode.getWorldTransform()),e=this.getBounds();if(!fn.isEmpty(e)){var n=new fn;return n.setFromTransformedAABB(e,t),n}}return this.getBounds()}}])}(Os);Vs.PARSED_STYLE_LIST=new Set([].concat(h(Os.PARSED_STYLE_LIST),["x","y","$el","innerHTML","width","height"]));var Ys=function(t){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return o(this,e),p(this,e,[a({type:en.IMAGE},t)])}return y(e,t),u(e)}(Os);Ys.PARSED_STYLE_LIST=new Set([].concat(h(Os.PARSED_STYLE_LIST),["x","y","z","src","width","height","isBillboard","billboardRotation","isSizeAttenuation","keepAspectRatio"]));var Us=["style"],zs=function(t){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=n.style,i=$e(n,Us);o(this,e),(t=p(this,e,[a({type:en.LINE,style:a({x1:0,y1:0,x2:0,y2:0,z1:0,z2:0},r)},i)])).markerStartAngle=0,t.markerEndAngle=0;var s=t.parsedStyle,u=s.markerStart,l=s.markerEnd;return u&&Ps(u)&&(t.markerStartAngle=u.getLocalEulerAngles(),t.appendChild(u)),l&&Ps(l)&&(t.markerEndAngle=l.getLocalEulerAngles(),t.appendChild(l)),t.transformMarker(!0),t.transformMarker(!1),t}return y(e,t),u(e,[{key:"attributeChangedCallback",value:function(t,e,n,r,i){"x1"===t||"y1"===t||"x2"===t||"y2"===t||"markerStartOffset"===t||"markerEndOffset"===t?(this.transformMarker(!0),this.transformMarker(!1)):"markerStart"===t?(r&&Ps(r)&&(this.markerStartAngle=0,r.remove()),i&&Ps(i)&&(this.markerStartAngle=i.getLocalEulerAngles(),this.appendChild(i),this.transformMarker(!0))):"markerEnd"===t&&(r&&Ps(r)&&(this.markerEndAngle=0,r.remove()),i&&Ps(i)&&(this.markerEndAngle=i.getLocalEulerAngles(),this.appendChild(i),this.transformMarker(!1)))}},{key:"transformMarker",value:function(t){var e=this.parsedStyle,n=e.markerStartOffset,r=e.markerEndOffset,i=e.x1,a=e.x2,o=e.y1,s=e.y2,u=t?e.markerStart:e.markerEnd;if(u&&Ps(u)){var l,c,h,f,d,v,p;t?(f=i,d=o,c=a-i,h=s-o,v=n||0,p=this.markerStartAngle):(f=a,d=s,c=i-a,h=o-s,v=r||0,p=this.markerEndAngle),u.setLocalEulerAngles(180*(l=Math.atan2(h,c))/Math.PI+p),u.setLocalPosition(f+Math.cos(l)*v,d+Math.sin(l)*v)}}},{key:"getPoint",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=this.parsedStyle,r=Ve(n.x1,n.y1,n.x2,n.y2,t),i=r.x,a=r.y,o=pt(nt(),at(i,a,0),e?this.getWorldTransform():this.getLocalTransform());return new mn(o[0],o[1])}},{key:"getPointAtLength",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return this.getPoint(t/this.getTotalLength(),e)}},{key:"getTotalLength",value:function(){var t=this.parsedStyle;return Be(t.x1,t.y1,t.x2,t.y2)}}])}(Os);zs.PARSED_STYLE_LIST=new Set([].concat(h(Os.PARSED_STYLE_LIST),["x1","y1","x2","y2","z1","z2","isBillboard","isSizeAttenuation","markerStart","markerEnd","markerStartOffset","markerEndOffset"]));var js=["style"],Xs=function(t){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=n.style,i=$e(n,js);o(this,e),(t=p(this,e,[a({type:en.PATH,style:r,initialParsedStyle:{miterLimit:4,d:a({},Hn)}},i)])).markerStartAngle=0,t.markerEndAngle=0,t.markerMidList=[];var s=t.parsedStyle,u=s.markerStart,l=s.markerEnd,c=s.markerMid;return u&&Ps(u)&&(t.markerStartAngle=u.getLocalEulerAngles(),t.appendChild(u)),c&&Ps(c)&&t.placeMarkerMid(c),l&&Ps(l)&&(t.markerEndAngle=l.getLocalEulerAngles(),t.appendChild(l)),t.transformMarker(!0),t.transformMarker(!1),t}return y(e,t),u(e,[{key:"attributeChangedCallback",value:function(t,e,n,r,i){"d"===t?(this.transformMarker(!0),this.transformMarker(!1),this.placeMarkerMid(this.parsedStyle.markerMid)):"markerStartOffset"===t||"markerEndOffset"===t?(this.transformMarker(!0),this.transformMarker(!1)):"markerStart"===t?(r&&Ps(r)&&(this.markerStartAngle=0,r.remove()),i&&Ps(i)&&(this.markerStartAngle=i.getLocalEulerAngles(),this.appendChild(i),this.transformMarker(!0))):"markerEnd"===t?(r&&Ps(r)&&(this.markerEndAngle=0,r.remove()),i&&Ps(i)&&(this.markerEndAngle=i.getLocalEulerAngles(),this.appendChild(i),this.transformMarker(!1))):"markerMid"===t&&this.placeMarkerMid(i)}},{key:"transformMarker",value:function(t){var e=this.parsedStyle,n=e.markerStartOffset,r=e.markerEndOffset,i=t?e.markerStart:e.markerEnd;if(i&&Ps(i)){var a,o,s,u,l,c,h;if(t){var f=g(this.getStartTangent(),2),d=f[0],v=f[1];u=v[0],l=v[1],o=d[0]-v[0],s=d[1]-v[1],c=n||0,h=this.markerStartAngle}else{var p=g(this.getEndTangent(),2),m=p[0],y=p[1];u=y[0],l=y[1],o=m[0]-y[0],s=m[1]-y[1],c=r||0,h=this.markerEndAngle}i.setLocalEulerAngles(180*(a=Math.atan2(s,o))/Math.PI+h),i.setLocalPosition(u+Math.cos(a)*c,l+Math.sin(a)*c)}}},{key:"placeMarkerMid",value:function(t){var e=this.parsedStyle.d.segments;if(this.markerMidList.forEach((function(t){t.remove()})),t&&Ps(t))for(var n=1;e.length-1>n;n++){var r=g(e[n].currentPoint,2),i=r[0],a=r[1],o=1===n?t:t.cloneNode(!0);this.markerMidList.push(o),this.appendChild(o),o.setLocalPosition(i,a)}}},{key:"getTotalLength",value:function(){return Ri(this)}},{key:"getPointAtLength",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=function(t,e,n){return Se(t,e,qt(qt({},n),{bbox:!1,length:!0})).point}(this.parsedStyle.d.absolutePath,t),r=n.x,i=n.y,a=pt(nt(),at(r,i,0),e?this.getWorldTransform():this.getLocalTransform());return new mn(a[0],a[1])}},{key:"getPoint",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return this.getPointAtLength(t*Ri(this),e)}},{key:"getStartTangent",value:function(){var t=this.parsedStyle.d.segments,e=[];if(t.length>1){var n=t[0].currentPoint,r=t[1].currentPoint,i=t[1].startTangent;e=[],i?(e.push([n[0]-i[0],n[1]-i[1]]),e.push([n[0],n[1]])):(e.push([r[0],r[1]]),e.push([n[0],n[1]]))}return e}},{key:"getEndTangent",value:function(){var t=this.parsedStyle.d.segments,e=t.length,n=[];if(e>1){var r=t[e-2].currentPoint,i=t[e-1].currentPoint,a=t[e-1].endTangent;n=[],a?(n.push([i[0]-a[0],i[1]-a[1]]),n.push([i[0],i[1]])):(n.push([r[0],r[1]]),n.push([i[0],i[1]]))}return n}}])}(Os);Xs.PARSED_STYLE_LIST=new Set([].concat(h(Os.PARSED_STYLE_LIST),["d","markerStart","markerMid","markerEnd","markerStartOffset","markerEndOffset","isBillboard","isSizeAttenuation"]));var Hs=["style"],Ws=function(t){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=n.style,i=$e(n,Hs);o(this,e),(t=p(this,e,[a({type:en.POLYGON,style:r,initialParsedStyle:{points:{points:[],totalLength:0,segments:[]},miterLimit:4,isClosed:!0}},i)])).markerStartAngle=0,t.markerEndAngle=0,t.markerMidList=[];var s=t.parsedStyle,u=s.markerStart,l=s.markerEnd,c=s.markerMid;return u&&Ps(u)&&(t.markerStartAngle=u.getLocalEulerAngles(),t.appendChild(u)),c&&Ps(c)&&t.placeMarkerMid(c),l&&Ps(l)&&(t.markerEndAngle=l.getLocalEulerAngles(),t.appendChild(l)),t.transformMarker(!0),t.transformMarker(!1),t}return y(e,t),u(e,[{key:"attributeChangedCallback",value:function(t,e,n,r,i){"points"===t?(this.transformMarker(!0),this.transformMarker(!1),this.placeMarkerMid(this.parsedStyle.markerMid)):"markerStartOffset"===t||"markerEndOffset"===t?(this.transformMarker(!0),this.transformMarker(!1)):"markerStart"===t?(r&&Ps(r)&&(this.markerStartAngle=0,r.remove()),i&&Ps(i)&&(this.markerStartAngle=i.getLocalEulerAngles(),this.appendChild(i),this.transformMarker(!0))):"markerEnd"===t?(r&&Ps(r)&&(this.markerEndAngle=0,r.remove()),i&&Ps(i)&&(this.markerEndAngle=i.getLocalEulerAngles(),this.appendChild(i),this.transformMarker(!1))):"markerMid"===t&&this.placeMarkerMid(i)}},{key:"transformMarker",value:function(t){var e=this.parsedStyle,n=e.markerStartOffset,r=e.markerEndOffset,i=(e.points||{}).points,a=t?e.markerStart:e.markerEnd;if(a&&Ps(a)&&i){var o,s,u,l,c,h,f;if(l=i[0][0],c=i[0][1],t)s=i[1][0]-i[0][0],u=i[1][1]-i[0][1],h=n||0,f=this.markerStartAngle;else{var d=i.length;this.parsedStyle.isClosed?(s=i[d-1][0]-i[0][0],u=i[d-1][1]-i[0][1]):(l=i[d-1][0],c=i[d-1][1],s=i[d-2][0]-i[d-1][0],u=i[d-2][1]-i[d-1][1]),h=r||0,f=this.markerEndAngle}a.setLocalEulerAngles(180*(o=Math.atan2(u,s))/Math.PI+f),a.setLocalPosition(l+Math.cos(o)*h,c+Math.sin(o)*h)}}},{key:"placeMarkerMid",value:function(t){var e=(this.parsedStyle.points||{}).points;if(this.markerMidList.forEach((function(t){t.remove()})),this.markerMidList=[],t&&Ps(t)&&e)for(var n=1;(this.parsedStyle.isClosed?e.length:e.length-1)>n;n++){var r=e[n][0],i=e[n][1],a=1===n?t:t.cloneNode(!0);this.markerMidList.push(a),this.appendChild(a),a.setLocalPosition(r,i)}}}])}(Os);Ws.PARSED_STYLE_LIST=new Set([].concat(h(Os.PARSED_STYLE_LIST),["points","markerStart","markerMid","markerEnd","markerStartOffset","markerEndOffset","isClosed","isBillboard","isSizeAttenuation"]));var qs=["style"],Zs=function(t){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.style,r=$e(t,qs);return o(this,e),p(this,e,[a({type:en.POLYLINE,style:n,initialParsedStyle:{points:{points:[],totalLength:0,segments:[]},miterLimit:4,isClosed:!1}},r)])}return y(e,t),u(e,[{key:"getTotalLength",value:function(){return 0===(t=this).parsedStyle.points.totalLength&&(t.parsedStyle.points.totalLength=je(t.parsedStyle.points.points)),t.parsedStyle.points.totalLength;var t}},{key:"getPointAtLength",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return this.getPoint(t/this.getTotalLength(),e)}},{key:"getPoint",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=this.parsedStyle.points.points;if(0===this.parsedStyle.points.segments.length){var r,i,a=[],o=0,s=this.getTotalLength();n.forEach((function(t,e){n[e+1]&&((r=[0,0])[0]=o/s,i=Be(t[0],t[1],n[e+1][0],n[e+1][1]),r[1]=(o+=i)/s,a.push(r))})),this.parsedStyle.points.segments=a}var u=0,l=0;this.parsedStyle.points.segments.forEach((function(e,n){e[0]>t||t>e[1]||(u=(t-e[0])/(e[1]-e[0]),l=n)}));var c=Ve(n[l][0],n[l][1],n[l+1][0],n[l+1][1],u),h=c.x,f=c.y,d=pt(nt(),at(h,f,0),e?this.getWorldTransform():this.getLocalTransform());return new mn(d[0],d[1])}},{key:"getStartTangent",value:function(){var t=this.parsedStyle.points.points,e=[];return e.push([t[1][0],t[1][1]]),e.push([t[0][0],t[0][1]]),e}},{key:"getEndTangent",value:function(){var t=this.parsedStyle.points.points,e=t.length-1,n=[];return n.push([t[e-1][0],t[e-1][1]]),n.push([t[e][0],t[e][1]]),n}}])}(Ws);Zs.PARSED_STYLE_LIST=new Set([].concat(h(Ws.PARSED_STYLE_LIST),["points","markerStart","markerMid","markerEnd","markerStartOffset","markerEndOffset","isBillboard"]));var Ks=function(t){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return o(this,e),p(this,e,[a({type:en.RECT},t)])}return y(e,t),u(e)}(Os);Ks.PARSED_STYLE_LIST=new Set([].concat(h(Os.PARSED_STYLE_LIST),["x","y","z","width","height","isBillboard","isSizeAttenuation","radius"]));var Qs=["style"],$s=function(t){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.style,r=$e(t,Qs);return o(this,e),p(this,e,[a({type:en.TEXT,style:a({fill:"black"},n)},r)])}return y(e,t),u(e,[{key:"getComputedTextLength",value:function(){var t;return this.getGeometryBounds(),(null===(t=this.parsedStyle.metrics)||void 0===t?void 0:t.maxLineWidth)||0}},{key:"getLineBoundingRects",value:function(){var t;return this.getGeometryBounds(),(null===(t=this.parsedStyle.metrics)||void 0===t?void 0:t.lineMetrics)||[]}},{key:"isOverflowing",value:function(){return this.getGeometryBounds(),!!this.parsedStyle.isOverflowing}}])}(Os);$s.PARSED_STYLE_LIST=new Set([].concat(h(Os.PARSED_STYLE_LIST),["x","y","z","isBillboard","billboardRotation","isSizeAttenuation","text","textAlign","textBaseline","fontStyle","fontSize","fontFamily","fontWeight","fontVariant","lineHeight","letterSpacing","leading","wordWrap","wordWrapWidth","maxLines","textOverflow","isOverflowing","textPath","textDecorationLine","textDecorationColor","textDecorationStyle","textPathSide","textPathStartOffset","metrics","dx","dy"]));var Js=function(){return u((function t(){o(this,t),this.registry={},this.define(en.CIRCLE,Ls),this.define(en.ELLIPSE,Ds),this.define(en.RECT,Ks),this.define(en.IMAGE,Ys),this.define(en.LINE,zs),this.define(en.GROUP,Gs),this.define(en.PATH,Xs),this.define(en.POLYGON,Ws),this.define(en.POLYLINE,Zs),this.define(en.TEXT,$s),this.define(en.HTML,Vs)}),[{key:"define",value:function(t,e){this.registry[t]=e}},{key:"get",value:function(t){return this.registry[t]}}])}(),tu={number:function(t){return new zr(t)},percent:function(t){return new zr(t,"%")},px:function(t){return new zr(t,"px")},em:function(t){return new zr(t,"em")},rem:function(t){return new zr(t,"rem")},deg:function(t){return new zr(t,"deg")},grad:function(t){return new zr(t,"grad")},rad:function(t){return new zr(t,"rad")},turn:function(t){return new zr(t,"turn")},s:function(t){return new zr(t,"s")},ms:function(t){return new zr(t,"ms")},registerProperty:function(t){ks.styleValueRegistry.registerMetadata({n:t.name,inh:t.inherits,int:t.interpolable,d:t.initialValue,syntax:t.syntax})},registerLayout:function(t,e){ks.layoutRegistry.registerLayout(t,e)}},eu=function(t){function e(){var t;o(this,e),(t=p(this,e)).defaultView=null,t.ownerDocument=null,t.nodeName="document";try{t.timeline=new ks.AnimationTimeline(t)}catch(t){}var n={};return da.forEach((function(t){var e=t.d;t.inh&&e&&(n[t.n]=It(e)?e(en.GROUP):e)})),t.documentElement=new Gs({id:"g-root",style:n}),t.documentElement.ownerDocument=t,t.documentElement.parentNode=t,t.childNodes=[t.documentElement],t}return y(e,t),u(e,[{key:"children",get:function(){return this.childNodes}},{key:"childElementCount",get:function(){return this.childNodes.length}},{key:"firstElementChild",get:function(){return this.firstChild}},{key:"lastElementChild",get:function(){return this.lastChild}},{key:"createElement",value:function(t,e){if("svg"===t)return this.documentElement;var n=this.defaultView.customElements.get(t);n||(console.warn("Unsupported tagName: ",t),n="tspan"===t?$s:Gs);var r=new n(e);return r.ownerDocument=this,r}},{key:"createElementNS",value:function(t,e,n){return this.createElement(e,n)}},{key:"cloneNode",value:function(t){throw Error(gn)}},{key:"destroy",value:function(){try{this.documentElement.destroyChildren(),this.timeline.destroy()}catch(t){}}},{key:"elementsFromBBox",value:function(t,e,n,r){var i=this.defaultView.context.rBushRoot.search({minX:t,minY:e,maxX:n,maxY:r}),a=[];return i.forEach((function(t){var e=t.displayObject,n=e.parsedStyle.pointerEvents,r=["auto","visiblepainted","visiblefill","visiblestroke","visible"].includes(void 0===n?"auto":n);(!r||r&&e.isVisible())&&!e.isCulled()&&e.isInteractive()&&a.push(e)})),a.sort((function(t,e){return e.sortable.renderOrder-t.sortable.renderOrder})),a}},{key:"elementFromPointSync",value:function(t,e){var n=this.defaultView.canvas2Viewport({x:t,y:e}),r=n.x,i=n.y,a=this.defaultView.getConfig();if(0>r||0>i||r>a.width||i>a.height)return null;var o=this.defaultView.viewport2Client({x:r,y:i}),s=o.x,u=o.y,l=this.defaultView.getRenderingService().hooks.pickSync.call({topmost:!0,position:{x:t,y:e,viewportX:r,viewportY:i,clientX:s,clientY:u},picked:[]}).picked;return l&&l[0]||this.documentElement}},{key:"elementFromPoint",value:(r=Ke(qe().mark((function t(e,n){var r,i,a,o,s,u,l,c,h,f;return qe().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=this.defaultView.canvas2Viewport({x:e,y:n}),i=r.x,a=r.y,o=this.defaultView.getConfig(),s=o.width,u=o.height,!(0>i||0>a||i>s||a>u)){t.next=4;break}return t.abrupt("return",null);case 4:return l=this.defaultView.viewport2Client({x:i,y:a}),c=l.x,h=l.y,t.next=7,this.defaultView.getRenderingService().hooks.pick.promise({topmost:!0,position:{x:e,y:n,viewportX:i,viewportY:a,clientX:c,clientY:h},picked:[]});case 7:return t.abrupt("return",(f=t.sent.picked)&&f[0]||this.documentElement);case 10:case"end":return t.stop()}}),t,this)}))),function(t,e){return r.apply(this,arguments)})},{key:"elementsFromPointSync",value:function(t,e){var n=this.defaultView.canvas2Viewport({x:t,y:e}),r=n.x,i=n.y,a=this.defaultView.getConfig();if(0>r||0>i||r>a.width||i>a.height)return[];var o=this.defaultView.viewport2Client({x:r,y:i}),s=o.x,u=o.y,l=this.defaultView.getRenderingService().hooks.pickSync.call({topmost:!1,position:{x:t,y:e,viewportX:r,viewportY:i,clientX:s,clientY:u},picked:[]}).picked;return l[l.length-1]!==this.documentElement&&l.push(this.documentElement),l}},{key:"elementsFromPoint",value:(n=Ke(qe().mark((function t(e,n){var r,i,a,o,s,u,l,c,h,f;return qe().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=this.defaultView.canvas2Viewport({x:e,y:n}),i=r.x,a=r.y,o=this.defaultView.getConfig(),s=o.width,u=o.height,!(0>i||0>a||i>s||a>u)){t.next=4;break}return t.abrupt("return",[]);case 4:return l=this.defaultView.viewport2Client({x:i,y:a}),c=l.x,h=l.y,t.next=7,this.defaultView.getRenderingService().hooks.pick.promise({topmost:!1,position:{x:e,y:n,viewportX:i,viewportY:a,clientX:c,clientY:h},picked:[]});case 7:return(f=t.sent.picked)[f.length-1]!==this.documentElement&&f.push(this.documentElement),t.abrupt("return",f);case 11:case"end":return t.stop()}}),t,this)}))),function(t,e){return n.apply(this,arguments)})},{key:"appendChild",value:function(t,e){throw Error(kn)}},{key:"insertBefore",value:function(t,e){throw Error(kn)}},{key:"removeChild",value:function(t,e){throw Error(kn)}},{key:"replaceChild",value:function(t,e,n){throw Error(kn)}},{key:"append",value:function(){throw Error(kn)}},{key:"prepend",value:function(){throw Error(kn)}},{key:"getElementById",value:function(t){return this.documentElement.getElementById(t)}},{key:"getElementsByName",value:function(t){return this.documentElement.getElementsByName(t)}},{key:"getElementsByTagName",value:function(t){return this.documentElement.getElementsByTagName(t)}},{key:"getElementsByClassName",value:function(t){return this.documentElement.getElementsByClassName(t)}},{key:"querySelector",value:function(t){return this.documentElement.querySelector(t)}},{key:"querySelectorAll",value:function(t){return this.documentElement.querySelectorAll(t)}},{key:"find",value:function(t){return this.documentElement.find(t)}},{key:"findAll",value:function(t){return this.documentElement.findAll(t)}}]);var n,r}(_o),nu=function(){function t(e){o(this,t),this.strategies=e}return u(t,[{key:"apply",value:function(e){var n=e.camera,r=e.renderingService,i=e.renderingContext,a=this.strategies;r.hooks.cull.tap(t.tag,(function(t){if(t){var e=t.cullable;return e.visible=0===a.length?i.unculledEntities.indexOf(t.entity)>-1:a.every((function(e){return e.isVisible(n,t)})),!t.isCulled()&&t.isVisible()?t:(t.dispatchEvent(new Oo(Uo.CULLED)),null)}return t})),r.hooks.afterRender.tap(t.tag,(function(t){t.cullable.visibilityPlaneMask=-1}))}}])}();nu.tag="Culling";var ru=function(){function t(){var e=this;o(this,t),this.autoPreventDefault=!1,this.rootPointerEvent=new Co(null),this.rootWheelEvent=new Ro(null),this.onPointerMove=function(t){var n,r=null===(n=e.context.renderingContext.root)||void 0===n||null===(n=n.ownerDocument)||void 0===n?void 0:n.defaultView;if(!r.supportsTouchEvents||"touch"!==t.pointerType){var i,a=Qe(e.normalizeToPointerEvent(t,r));try{for(a.s();!(i=a.n()).done;){var o=e.bootstrapEvent(e.rootPointerEvent,i.value,r,t);e.context.eventService.mapEvent(o)}}catch(t){a.e(t)}finally{a.f()}e.setCursor(e.context.eventService.cursor)}},this.onClick=function(t){var n,r,i=null===(n=e.context.renderingContext.root)||void 0===n||null===(n=n.ownerDocument)||void 0===n?void 0:n.defaultView,a=Qe(e.normalizeToPointerEvent(t,i));try{for(a.s();!(r=a.n()).done;){var o=e.bootstrapEvent(e.rootPointerEvent,r.value,i,t);e.context.eventService.mapEvent(o)}}catch(t){a.e(t)}finally{a.f()}e.setCursor(e.context.eventService.cursor)}}return u(t,[{key:"apply",value:function(e){var n=this;this.context=e;var r=e.renderingService,i=this.context.renderingContext.root.ownerDocument.defaultView;this.context.eventService.setPickHandler((function(t){return n.context.renderingService.hooks.pickSync.call({position:t,picked:[],topmost:!0}).picked[0]||null})),r.hooks.pointerWheel.tap(t.tag,(function(t){var e=n.normalizeWheelEvent(t);n.context.eventService.mapEvent(e)})),r.hooks.pointerDown.tap(t.tag,(function(t){if(!i.supportsTouchEvents||"touch"!==t.pointerType){var e=n.normalizeToPointerEvent(t,i);if(n.autoPreventDefault&&e[0].isNormalized)(t.cancelable||!("cancelable"in t))&&t.preventDefault();var r,a=Qe(e);try{for(a.s();!(r=a.n()).done;){var o=n.bootstrapEvent(n.rootPointerEvent,r.value,i,t);n.context.eventService.mapEvent(o)}}catch(t){a.e(t)}finally{a.f()}n.setCursor(n.context.eventService.cursor)}})),r.hooks.pointerUp.tap(t.tag,(function(t){if(!i.supportsTouchEvents||"touch"!==t.pointerType){var e,r=n.context.contextService.getDomElement(),a=n.context.eventService.isNativeEventFromCanvas(r,t)?"":"outside",o=Qe(n.normalizeToPointerEvent(t,i));try{for(o.s();!(e=o.n()).done;){var s=n.bootstrapEvent(n.rootPointerEvent,e.value,i,t);s.type+=a,n.context.eventService.mapEvent(s)}}catch(t){o.e(t)}finally{o.f()}n.setCursor(n.context.eventService.cursor)}})),r.hooks.pointerMove.tap(t.tag,this.onPointerMove),r.hooks.pointerOver.tap(t.tag,this.onPointerMove),r.hooks.pointerOut.tap(t.tag,this.onPointerMove),r.hooks.click.tap(t.tag,this.onClick),r.hooks.pointerCancel.tap(t.tag,(function(t){var e,r=Qe(n.normalizeToPointerEvent(t,i));try{for(r.s();!(e=r.n()).done;){var a=n.bootstrapEvent(n.rootPointerEvent,e.value,i,t);n.context.eventService.mapEvent(a)}}catch(t){r.e(t)}finally{r.f()}n.setCursor(n.context.eventService.cursor)}))}},{key:"bootstrapEvent",value:function(t,e,n,r){t.view=n,t.originalEvent=null,t.nativeEvent=r,t.pointerId=e.pointerId,t.width=e.width,t.height=e.height,t.isPrimary=e.isPrimary,t.pointerType=e.pointerType,t.pressure=e.pressure,t.tangentialPressure=e.tangentialPressure,t.tiltX=e.tiltX,t.tiltY=e.tiltY,t.twist=e.twist,this.transferMouseData(t,e);var i=this.context.eventService.client2Viewport({x:e.clientX,y:e.clientY}),a=i.y;t.viewport.x=i.x,t.viewport.y=a;var o=this.context.eventService.viewport2Canvas(t.viewport),s=o.y;return t.canvas.x=o.x,t.canvas.y=s,t.global.copyFrom(t.canvas),t.offset.copyFrom(t.canvas),t.isTrusted=r.isTrusted,"pointerleave"===t.type&&(t.type="pointerout"),t.type.startsWith("mouse")&&(t.type=t.type.replace("mouse","pointer")),t.type.startsWith("touch")&&(t.type=Ya[t.type]||t.type),t}},{key:"normalizeWheelEvent",value:function(t){var e=this.rootWheelEvent;this.transferMouseData(e,t),e.deltaMode=t.deltaMode,e.deltaX=t.deltaX,e.deltaY=t.deltaY,e.deltaZ=t.deltaZ;var n=this.context.eventService.client2Viewport({x:t.clientX,y:t.clientY}),r=n.y;e.viewport.x=n.x,e.viewport.y=r;var i=this.context.eventService.viewport2Canvas(e.viewport),a=i.y;return e.canvas.x=i.x,e.canvas.y=a,e.global.copyFrom(e.canvas),e.offset.copyFrom(e.canvas),e.nativeEvent=t,e.type=t.type,e}},{key:"transferMouseData",value:function(t,e){t.isTrusted=e.isTrusted,t.srcElement=e.srcElement,t.timeStamp=Ua.now(),t.type=e.type,t.altKey=e.altKey,t.metaKey=e.metaKey,t.shiftKey=e.shiftKey,t.ctrlKey=e.ctrlKey,t.button=e.button,t.buttons=e.buttons,t.client.x=e.clientX,t.client.y=e.clientY,t.movement.x=e.movementX,t.movement.y=e.movementY,t.page.x=e.pageX,t.page.y=e.pageY,t.screen.x=e.screenX,t.screen.y=e.screenY,t.relatedTarget=null}},{key:"setCursor",value:function(t){this.context.contextService.applyCursorStyle(t||this.context.config.cursor||"default")}},{key:"normalizeToPointerEvent",value:function(t,e){var n=[];if(e.isTouchEvent(t))for(var r=0;t.changedTouches.length>r;r++){var i=t.changedTouches[r];Wt(i.button)&&(i.button=0),Wt(i.buttons)&&(i.buttons=1),Wt(i.isPrimary)&&(i.isPrimary=1===t.touches.length&&"touchstart"===t.type),Wt(i.width)&&(i.width=i.radiusX||1),Wt(i.height)&&(i.height=i.radiusY||1),Wt(i.tiltX)&&(i.tiltX=0),Wt(i.tiltY)&&(i.tiltY=0),Wt(i.pointerType)&&(i.pointerType="touch"),Wt(i.pointerId)&&(i.pointerId=i.identifier||0),Wt(i.pressure)&&(i.pressure=i.force||.5),Wt(i.twist)&&(i.twist=0),Wt(i.tangentialPressure)&&(i.tangentialPressure=0),i.isNormalized=!0,i.type=t.type,n.push(i)}else if(e.isMouseEvent(t)){var a=t;Wt(a.isPrimary)&&(a.isPrimary=!0),Wt(a.width)&&(a.width=1),Wt(a.height)&&(a.height=1),Wt(a.tiltX)&&(a.tiltX=0),Wt(a.tiltY)&&(a.tiltY=0),Wt(a.pointerType)&&(a.pointerType="mouse"),Wt(a.pointerId)&&(a.pointerId=1),Wt(a.pressure)&&(a.pressure=.5),Wt(a.twist)&&(a.twist=0),Wt(a.tangentialPressure)&&(a.tangentialPressure=0),a.isNormalized=!0,n.push(a)}else n.push(t);return n}}])}();ru.tag="Event";var iu=[en.CIRCLE,en.ELLIPSE,en.IMAGE,en.RECT,en.LINE,en.POLYLINE,en.POLYGON,en.TEXT,en.PATH,en.HTML],au=function(){return u((function t(){o(this,t)}),[{key:"isVisible",value:function(t,e){var n,r=e.cullable;if(!r.enable)return!0;var i=e.getRenderBounds();if(fn.isEmpty(i))return!1;var a=t.getFrustum(),o=null===(n=e.parentNode)||void 0===n||null===(n=n.cullable)||void 0===n?void 0:n.visibilityPlaneMask;return r.visibilityPlaneMask=this.computeVisibilityWithPlaneMask(e,i,o||vn.INDETERMINATE,a.planes),r.visible=r.visibilityPlaneMask!==vn.OUTSIDE,r.visible}},{key:"computeVisibilityWithPlaneMask",value:function(t,e,n,r){if(n===vn.OUTSIDE||n===vn.INSIDE)return n;for(var i=vn.INSIDE,a=iu.indexOf(t.nodeName)>-1,o=0,s=r.length;s>o;++o){var u=1<<o;if(0!==(n&u)&&(!a||4!==o&&5!==o)){var l=r[o],c=l.normal,h=l.distance;if(0>ft(c,e.getPositiveFarPoint(r[o]))+h)return vn.OUTSIDE;0>ft(c,e.getNegativeFarPoint(r[o]))+h&&(i|=u)}}return i}}])}(),ou=function(){function t(){o(this,t),this.syncTasks=new Map,this.isFirstTimeRendering=!0,this.syncing=!1,this.isFirstTimeRenderingFinished=!1}return u(t,[{key:"apply",value:function(e){var n,r=this,i=e.renderingService,a=e.renderingContext.root.ownerDocument.defaultView;this.rBush=e.rBushRoot;var o=function(t){t.target.renderable.dirty=!0,i.dirtify()},s=function(t){r.syncTasks.set(t.target,t.detail.affectChildren),i.dirtify()},u=function(t){ks.enableSizeAttenuation&&ks.styleValueRegistry.updateSizeAttenuation(t.target,a.getCamera().getZoom())},l=function(t){var e=t.target,n=e.rBushNode;n.aabb&&r.rBush.remove(n.aabb),r.syncTasks.delete(e),ks.sceneGraphService.dirtifyToRoot(e),i.dirtify()};i.hooks.init.tap(t.tag,(function(){a.addEventListener(Uo.MOUNTED,u),a.addEventListener(Uo.UNMOUNTED,l),a.addEventListener(Uo.ATTR_MODIFIED,o),a.addEventListener(Uo.BOUNDS_CHANGED,s)})),i.hooks.destroy.tap(t.tag,(function(){a.removeEventListener(Uo.MOUNTED,u),a.removeEventListener(Uo.UNMOUNTED,l),a.removeEventListener(Uo.ATTR_MODIFIED,o),a.removeEventListener(Uo.BOUNDS_CHANGED,s),r.syncTasks.clear()}));var c=null!==(n=ks.globalThis.requestIdleCallback)&&void 0!==n?n:Ja.bind(ks.globalThis);i.hooks.endFrame.tap(t.tag,(function(){r.isFirstTimeRendering?(r.isFirstTimeRendering=!1,r.syncing=!0,c((function(){r.syncRTree(!0),r.isFirstTimeRenderingFinished=!0}))):r.syncRTree()}))}},{key:"syncNode",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(t.isConnected){var n=t.rBushNode;n.aabb&&this.rBush.remove(n.aabb);var r=t.getRenderBounds();if(r){var i=t.renderable;e&&(i.dirtyRenderBounds||(i.dirtyRenderBounds=new fn),i.dirtyRenderBounds.update(r.center,r.halfExtents));var a=g(r.getMin(),2),o=a[0],s=a[1],u=g(r.getMax(),2),l=u[0],c=u[1];n.aabb||(n.aabb={}),n.aabb.displayObject=t,n.aabb.minX=o,n.aabb.minY=s,n.aabb.maxX=l,n.aabb.maxY=c}return n.aabb&&!(isNaN(n.aabb.maxX)||isNaN(n.aabb.maxX)||isNaN(n.aabb.minX)||isNaN(n.aabb.minY))?n.aabb:void 0}}},{key:"syncRTree",value:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(e||!this.syncing&&0!==this.syncTasks.size){this.syncing=!0;var n=[],r=new Set,i=function(i){if(!r.has(i)&&i.renderable){var a=t.syncNode(i,e);a&&(n.push(a),r.add(i))}};this.syncTasks.forEach((function(t,e){t&&e.forEach(i);for(var n=e;n;)i(n),n=n.parentElement})),this.rBush.load(n),n.length=0,this.syncing=!1}}}])}();ou.tag="Prepare";var su=function(t){return t.READY="ready",t.BEFORE_RENDER="beforerender",t.RERENDER="rerender",t.AFTER_RENDER="afterrender",t.BEFORE_DESTROY="beforedestroy",t.AFTER_DESTROY="afterdestroy",t.RESIZE="resize",t.DIRTY_RECTANGLE="dirtyrectangle",t.RENDERER_CHANGED="rendererchanged",t}({}),uu=new Oo(Uo.MOUNTED),lu=new Oo(Uo.UNMOUNTED),cu=new Oo(su.BEFORE_RENDER),hu=new Oo(su.RERENDER),fu=new Oo(su.AFTER_RENDER),du=function(t){function e(t){var n;o(this,e),(n=p(this,e)).Element=Os,n.inited=!1,n.context={};var r=t.container,i=t.canvas,s=t.renderer,u=t.width,l=t.height,c=t.background,h=t.cursor,f=t.supportsMutipleCanvasesInOneContainer,d=t.cleanUpOnDestroy,v=void 0===d||d,m=t.offscreenCanvas,y=t.requestAnimationFrame,g=t.cancelAnimationFrame,k=t.createImage,E=t.supportsTouchEvents,x=t.supportsPointerEvents,b=t.isTouchEvent,T=t.isMouseEvent,M=t.dblClickSpeed,w=u,N=l,P=t.devicePixelRatio||Da&&window.devicePixelRatio||1;return P=1>P?1:Math.ceil(P),i&&(w=u||function(t){var e=Va(t,"width");return"auto"===e?t.offsetWidth:parseFloat(e)}(i)||i.width/P,N=l||function(t){var e=Va(t,"height");return"auto"===e?t.offsetHeight:parseFloat(e)}(i)||i.height/P),n.customElements=new Js,n.devicePixelRatio=P,n.requestAnimationFrame=null!=y?y:Ja.bind(ks.globalThis),n.cancelAnimationFrame=null!=g?g:to.bind(ks.globalThis),n.supportsTouchEvents=null!=E?E:"ontouchstart"in ks.globalThis,n.supportsPointerEvents=null!=x?x:!!ks.globalThis.PointerEvent,n.isTouchEvent=null!=b?b:function(t){return n.supportsTouchEvents&&t instanceof ks.globalThis.TouchEvent},n.isMouseEvent=null!=T?T:function(t){return!ks.globalThis.MouseEvent||t instanceof ks.globalThis.MouseEvent&&(!n.supportsPointerEvents||!(t instanceof ks.globalThis.PointerEvent))},m&&(ks.offscreenCanvas=m),n.document=new eu,n.document.defaultView=n,f||function(t,e,n){if(t){var r="string"==typeof t?document.getElementById(t):t;_a.has(r)&&_a.get(r).destroy(n),_a.set(r,e)}}(r,n,v),n.initRenderingContext(a(a({},t),{},{width:w,height:N,background:null!=c?c:"transparent",cursor:null!=h?h:"default",cleanUpOnDestroy:v,devicePixelRatio:P,requestAnimationFrame:n.requestAnimationFrame,cancelAnimationFrame:n.cancelAnimationFrame,supportsTouchEvents:n.supportsTouchEvents,supportsPointerEvents:n.supportsPointerEvents,isTouchEvent:n.isTouchEvent,isMouseEvent:n.isMouseEvent,dblClickSpeed:null!=M?M:200,createImage:null!=k?k:function(){return new window.Image}})),n.initDefaultCamera(w,N,s.clipSpaceNearZ),n.initRenderer(s,!0),n}return y(e,t),u(e,[{key:"initRenderingContext",value:function(t){this.context.config=t,this.context.renderingContext={root:this.document.documentElement,renderListCurrentFrame:[],unculledEntities:[],renderReasons:new Set,force:!1,dirty:!1}}},{key:"initDefaultCamera",value:function(t,e,n){var r=this,i=new ks.CameraContribution;i.clipSpaceNearZ=n,i.setType(Gn.EXPLORING,Bn.DEFAULT).setPosition(t/2,e/2,500).setFocalPoint(t/2,e/2,0).setOrthographic(t/-2,t/2,e/2,e/-2,.1,1e3),i.canvas=this,i.eventEmitter.on(Yn.UPDATED,(function(){r.context.renderingContext.renderReasons.add(Go.CAMERA_CHANGED),ks.enableSizeAttenuation&&r.getConfig().renderer.getConfig().enableSizeAttenuation&&r.updateSizeAttenuation()})),this.context.camera=i}},{key:"updateSizeAttenuation",value:function(){var t=this.getCamera().getZoom();this.document.documentElement.forEach((function(e){ks.styleValueRegistry.updateSizeAttenuation(e,t)}))}},{key:"getConfig",value:function(){return this.context.config}},{key:"getRoot",value:function(){return this.document.documentElement}},{key:"getCamera",value:function(){return this.context.camera}},{key:"getContextService",value:function(){return this.context.contextService}},{key:"getEventService",value:function(){return this.context.eventService}},{key:"getRenderingService",value:function(){return this.context.renderingService}},{key:"getRenderingContext",value:function(){return this.context.renderingContext}},{key:"getStats",value:function(){return this.getRenderingService().getStats()}},{key:"ready",get:function(){var t=this;return this.readyPromise||(this.readyPromise=new Promise((function(e){t.resolveReadyPromise=function(){e(t)}})),this.inited&&this.resolveReadyPromise()),this.readyPromise}},{key:"destroy",value:function(){var t=0>=arguments.length||void 0===arguments[0]||arguments[0],e=arguments.length>1?arguments[1]:void 0;Pr.clearCache(),e||this.dispatchEvent(new Oo(su.BEFORE_DESTROY)),this.frameId&&this.cancelAnimationFrame(this.frameId);var n=this.getRoot();t&&(this.unmountChildren(n),this.document.destroy(),this.getEventService().destroy()),this.getRenderingService().destroy(),this.getContextService().destroy(),this.context.rBushRoot&&this.context.rBushRoot.clear(),e||this.dispatchEvent(new Oo(su.AFTER_DESTROY));var r=function(t){t.currentTarget=null,t.manager=null,t.target=null,t.relatedNode=null};r(uu),r(lu),r(cu),r(hu),r(fu),r(As),r(Ts),r(Ms),r(ws)}},{key:"changeSize",value:function(t,e){this.resize(t,e)}},{key:"resize",value:function(t,e){var n=this.context.config;n.width=t,n.height=e,this.getContextService().resize(t,e);var r=this.context.camera,i=r.getProjectionMode();r.setPosition(t/2,e/2,500).setFocalPoint(t/2,e/2,0),i===Vn.ORTHOGRAPHIC?r.setOrthographic(t/-2,t/2,e/2,e/-2,r.getNear(),r.getFar()):r.setAspect(t/e),this.dispatchEvent(new Oo(su.RESIZE,{width:t,height:e}))}},{key:"appendChild",value:function(t,e){return this.document.documentElement.appendChild(t,e)}},{key:"insertBefore",value:function(t,e){return this.document.documentElement.insertBefore(t,e)}},{key:"removeChild",value:function(t){return this.document.documentElement.removeChild(t)}},{key:"removeChildren",value:function(){this.document.documentElement.removeChildren()}},{key:"destroyChildren",value:function(){this.document.documentElement.destroyChildren()}},{key:"render",value:function(t){var e=this;t&&(cu.detail=t,fu.detail=t),this.dispatchEvent(cu),this.getRenderingService().render(this.getConfig(),t,(function(){e.dispatchEvent(hu)})),this.dispatchEvent(fu)}},{key:"run",value:function(){var t=this,e=function(n,r){t.render(r),t.frameId=t.requestAnimationFrame(e)};e()}},{key:"initRenderer",value:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!t)throw Error("Renderer is required.");this.inited=!1,this.readyPromise=void 0,this.context.rBushRoot=new tn,this.context.renderingPlugins=[],this.context.renderingPlugins.push(new ru,new ou,new nu([new au])),this.loadRendererContainerModule(t),this.context.contextService=new this.context.ContextService(a(a({},ks),this.context)),this.context.renderingService=new Bo(ks,this.context),this.context.eventService=new Do(ks,this.context),this.context.eventService.init(),this.context.contextService.init?(this.context.contextService.init(),this.initRenderingService(t,n,!0)):this.context.contextService.initAsync().then((function(){e.initRenderingService(t,n)})).catch((function(t){console.error(t)}))}},{key:"initRenderingService",value:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.context.renderingService.init((function(){e.inited=!0,n?r?e.requestAnimationFrame((function(){e.dispatchEvent(new Oo(su.READY))})):e.dispatchEvent(new Oo(su.READY)):e.dispatchEvent(new Oo(su.RENDERER_CHANGED)),e.readyPromise&&e.resolveReadyPromise(),n||e.getRoot().forEach((function(t){var e=t.renderable;e&&(e.renderBoundsDirty=!0,e.boundsDirty=!0,e.dirty=!0)})),e.mountChildren(e.getRoot()),t.getConfig().enableAutoRendering&&e.run()}))}},{key:"loadRendererContainerModule",value:function(t){var e=this;t.getPlugins().forEach((function(t){t.context=e.context,t.init(ks)}))}},{key:"setRenderer",value:function(t){var e=this.getConfig();if(e.renderer!==t){var n=e.renderer;e.renderer=t,this.destroy(!1,!0),h((null==n?void 0:n.getPlugins())||[]).reverse().forEach((function(t){t.destroy(ks)})),this.initRenderer(t)}}},{key:"setCursor",value:function(t){this.getConfig().cursor=t,this.getContextService().applyCursorStyle(t)}},{key:"unmountChildren",value:function(t){var e=this;t.childNodes.forEach((function(t){e.unmountChildren(t)})),this.inited&&(t.isMutationObserved?t.dispatchEvent(lu):(lu.target=t,this.dispatchEvent(lu,!0)),t!==this.document.documentElement&&(t.ownerDocument=null),t.isConnected=!1),t.isCustomElement&&t.disconnectedCallback&&t.disconnectedCallback()}},{key:"mountChildren",value:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:za(t);this.inited?t.isConnected||(t.ownerDocument=this.document,t.isConnected=!0,n||(t.isMutationObserved?t.dispatchEvent(uu):(uu.target=t,this.dispatchEvent(uu,!0)))):console.warn("[g]: You are trying to call `canvas.appendChild` before canvas' initialization finished. You can either await `canvas.ready` or listen to `CanvasEvent.READY` manually.","appended child: ",t.nodeName),t.childNodes.forEach((function(t){e.mountChildren(t,n)})),t.isCustomElement&&t.connectedCallback&&t.connectedCallback()}},{key:"mountFragment",value:function(t){this.mountChildren(t,!1)}},{key:"client2Viewport",value:function(t){return this.getEventService().client2Viewport(t)}},{key:"viewport2Client",value:function(t){return this.getEventService().viewport2Client(t)}},{key:"viewport2Canvas",value:function(t){return this.getEventService().viewport2Canvas(t)}},{key:"canvas2Viewport",value:function(t){return this.getEventService().canvas2Viewport(t)}},{key:"getPointByClient",value:function(t,e){return this.client2Viewport({x:t,y:e})}},{key:"getClientByPoint",value:function(t,e){return this.viewport2Client({x:t,y:e})}}])}(Io),vu=function(t){function e(){var t;o(this,e);for(var n=arguments.length,r=Array(n),i=0;n>i;i++)r[i]=arguments[i];return(t=p(this,e,[].concat(r))).landmarks=[],t}return y(e,t),u(e,[{key:"rotate",value:function(t,e,n){if(this.relElevation=En(e),this.relAzimuth=En(t),this.relRoll=En(n),this.elevation+=this.relElevation,this.azimuth+=this.relAzimuth,this.roll+=this.relRoll,this.type===Gn.EXPLORING){var r=Mt(Tt(),[1,0,0],Mn((this.rotateWorld?1:-1)*this.relElevation)),i=Mt(Tt(),[0,1,0],Mn((this.rotateWorld?1:-1)*this.relAzimuth)),a=Mt(Tt(),[0,0,1],Mn(this.relRoll)),o=wt(Tt(),i,r);o=wt(Tt(),o,a);var s=W(M(),o);L(this.matrix,this.matrix,[0,0,-this.distance]),O(this.matrix,this.matrix,s),L(this.matrix,this.matrix,[0,0,this.distance])}else{if(Math.abs(this.elevation)>90)return this;this.computeMatrix()}return this._getAxes(),this.type===Gn.ORBITING||this.type===Gn.EXPLORING?this._getPosition():this.type===Gn.TRACKING&&this._getFocalPoint(),this._update(),this}},{key:"pan",value:function(t,e){var n=bn(t,e,0),r=rt(this.position);return ut(r,r,ct(nt(),this.right,n[0])),ut(r,r,ct(nt(),this.up,n[1])),this._setPosition(r),this.triggerUpdate(),this}},{key:"dolly",value:function(t){var e,n=this.forward,r=rt(this.position);return r[0]+=(e=Math.max(Math.min(this.distance+t*this.dollyingStep,this.maxDistance),this.minDistance)-this.distance)*n[0],r[1]+=e*n[1],r[2]+=e*n[2],this._setPosition(r),this.type===Gn.ORBITING||this.type===Gn.EXPLORING?this._getDistance():this.type===Gn.TRACKING&&ut(this.focalPoint,r,this.distanceVector),this.triggerUpdate(),this}},{key:"cancelLandmarkAnimation",value:function(){void 0!==this.landmarkAnimationID&&this.canvas.cancelAnimationFrame(this.landmarkAnimationID)}},{key:"createLandmark",value:function(t){var e,n,r,i,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=a.position,s=void 0===o?this.position:o,u=a.focalPoint,l=void 0===u?this.focalPoint:u,c=a.roll,h=a.zoom,f=new ks.CameraContribution;f.setType(this.type,void 0),f.setPosition(s[0],null!==(e=s[1])&&void 0!==e?e:this.position[1],null!==(n=s[2])&&void 0!==n?n:this.position[2]),f.setFocalPoint(l[0],null!==(r=l[1])&&void 0!==r?r:this.focalPoint[1],null!==(i=l[2])&&void 0!==i?i:this.focalPoint[2]),f.setRoll(null!=c?c:this.roll),f.setZoom(null!=h?h:this.zoom);var d={name:t,matrix:w(f.getWorldTransform()),right:rt(f.right),up:rt(f.up),forward:rt(f.forward),position:rt(f.getPosition()),focalPoint:rt(f.getFocalPoint()),distanceVector:rt(f.getDistanceVector()),distance:f.getDistance(),dollyingStep:f.getDollyingStep(),azimuth:f.getAzimuth(),elevation:f.getElevation(),roll:f.getRoll(),relAzimuth:f.relAzimuth,relElevation:f.relElevation,relRoll:f.relRoll,zoom:f.getZoom()};return this.landmarks.push(d),d}},{key:"gotoLandmark",value:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=Yt(t)?this.landmarks.find((function(e){return e.name===t})):t;if(r){var i=zt(n)?{duration:n}:n,a=i.easing,o=void 0===a?"linear":a,s=i.duration,u=void 0===s?100:s,l=i.easingFunction,c=void 0===l?void 0:l,h=i.onfinish,f=void 0===h?void 0:h,d=i.onframe,v=void 0===d?void 0:d;this.cancelLandmarkAnimation();var p,m=r.position,y=r.focalPoint,g=r.zoom,k=r.roll,E=c||ks.EasingFunction(o),x=function(){e.setFocalPoint(y),e.setPosition(m),e.setRoll(k),e.setZoom(g),e.computeMatrix(),e.triggerUpdate(),null==f||f()};if(0===u)return x();var b=function(t){void 0===p&&(p=t);var n=t-p;if(u>n){var r,i,a=E(n/u),o=nt(),s=nt();if(vt(o,e.focalPoint,y,a),vt(s,e.position,m,a),i=e.roll*(1-a)+k*a,r=e.zoom*(1-a)+g*a,e.setFocalPoint(o),e.setPosition(s),e.setRoll(i),e.setZoom(r),.01>=gt(o,y)+gt(s,m)&&void 0===g&&void 0===k)return x();e.computeMatrix(),e.triggerUpdate(),u>n&&(null==v||v(a),e.landmarkAnimationID=e.canvas.requestAnimationFrame(b))}else x()};this.canvas.requestAnimationFrame(b)}}}])}(zn);ks.CameraContribution=vu;
/*!
   * @antv/g-dom-mutation-observer-api
   * @description A simple implementation of DOM MutationObserver API.
   * @version 2.0.35
   * @date 5/23/2025, 6:59:59 AM
   * <AUTHOR>
   * @docs https://g.antv.antgroup.com/
   */
var pu,mu,yu=function(){function t(e,n){o(this,t),this.addedNodes=[],this.attributeName=null,this.attributeNamespace=null,this.nextSibling=null,this.oldValue=null,this.previousSibling=null,this.removedNodes=[],this.type=e,this.target=n}return u(t,null,[{key:"copy",value:function(e){var n=new t(e.type,e.target);return n.addedNodes=e.addedNodes.slice(),n.removedNodes=e.removedNodes.slice(),n.previousSibling=e.previousSibling,n.nextSibling=e.nextSibling,n.attributeName=e.attributeName,n.attributeNamespace=e.attributeNamespace,n.oldValue=e.oldValue,n}}])}(),gu=0,ku=new WeakMap,Eu=function(){return u((function t(e,n,r){o(this,t),this.transientObservedNodes=[],this.observer=e,this.target=n,this.options=r}),[{key:"enqueue",value:function(t){var e=this.observer.records,n=e.length;if(e.length>0){var r=function(t,e){return t===e?t:mu&&function(t){return t===mu||t===pu}(t)?mu:null}(e[n-1],t);if(r)return void(e[n-1]=r)}else wu.push(this.observer),Mu||(Mu=!0,void 0!==ks.globalThis?ks.globalThis.setTimeout(Nu):Nu());e[n]=t}},{key:"addListeners",value:function(){this.addListeners_(this.target)}},{key:"addListeners_",value:function(t){var e=this.options;e.attributes&&t.addEventListener(Uo.ATTR_MODIFIED,this,!0),e.childList&&t.addEventListener(Uo.INSERTED,this,!0),(e.childList||e.subtree)&&t.addEventListener(Uo.REMOVED,this,!0)}},{key:"removeListeners",value:function(){this.removeListeners_(this.target)}},{key:"removeListeners_",value:function(t){var e=this.options;e.attributes&&t.removeEventListener(Uo.ATTR_MODIFIED,this,!0),e.childList&&t.removeEventListener(Uo.INSERTED,this,!0),(e.childList||e.subtree)&&t.removeEventListener(Uo.REMOVED,this,!0)}},{key:"removeTransientObservers",value:function(){var t=this.transientObservedNodes;this.transientObservedNodes=[],t.forEach((function(t){this.removeListeners_(t);for(var e=ku.get(t),n=0;e.length>n;n++)if(e[n]===this){e.splice(n,1);break}}),this)}},{key:"handleEvent",value:function(t){var e,n;switch(t.stopImmediatePropagation(),t.type){case Uo.ATTR_MODIFIED:var r=t.attrName,i=t.relatedNode.namespaceURI;(e=bu("attributes",n=t.target)).attributeName=r,e.attributeNamespace=i;var a=t.attrChange===zo.ADDITION?null:t.prevValue;Tu(n,(function(t){if(t.attributes&&(!t.attributeFilter||!t.attributeFilter.length||-1!==t.attributeFilter.indexOf(r)||-1!==t.attributeFilter.indexOf(i)))return t.attributeOldValue?function(t){return mu||((mu=yu.copy(pu)).oldValue=t,mu)}(a):e}));break;case Uo.REMOVED:case Uo.INSERTED:var o,s,u=t.target;t.type===Uo.INSERTED?(o=[u],s=[]):(o=[],s=[u]);var l=u.previousSibling,c=u.nextSibling;(e=bu("childList",n=t.relatedNode)).addedNodes=o,e.removedNodes=s,e.previousSibling=l,e.nextSibling=c,Tu(n,(function(t){if(t.childList)return e}))}pu=mu=void 0}}])}(),xu=function(){return u((function t(e){o(this,t),this.nodes=[],this.records=[],this.uid=gu++,this.callback=e}),[{key:"observe",value:function(t,e){if(!e.childList&&!e.attributes&&!e.characterData||e.attributeOldValue&&!e.attributes||e.attributeFilter&&e.attributeFilter.length&&!e.attributes||e.characterDataOldValue&&!e.characterData)throw new SyntaxError;var n,r=ku.get(t);r||ku.set(t,r=[]);for(var i=0;r.length>i;i++)if(r[i].observer===this){(n=r[i]).removeListeners(),n.options=e;break}n||(n=new Eu(this,t,e),r.push(n),this.nodes.push(t)),n.addListeners()}},{key:"disconnect",value:function(){var t=this;this.nodes.forEach((function(e){for(var n=ku.get(e),r=0;n.length>r;r++){var i=n[r];if(i.observer===t){i.removeListeners(),n.splice(r,1);break}}}),this),this.records=[]}},{key:"takeRecords",value:function(){var t=this.records;return this.records=[],t}}])}();function bu(t,e){return pu=new yu(t,e)}function Tu(t,e){for(var n=t;n;n=n.parentNode){var r=ku.get(n);if(r)for(var i=0;r.length>i;i++){var a=r[i],o=a.options;if(n===t||o.subtree){var s=e(o);s&&a.enqueue(s)}}}}var Mu=!1,wu=[];function Nu(){Mu=!1;var t=wu;wu=[],t.sort((function(t,e){return t.uid-e.uid}));var e=!1;t.forEach((function(t){var n=t.takeRecords();!function(t){t.nodes.forEach((function(e){var n=ku.get(e);n&&n.forEach((function(e){e.observer===t&&e.removeTransientObservers()}))}))}(t),n.length&&(t.callback(n,t),e=!0)})),e&&Nu()}
/*!
   * @antv/g-web-animations-api
   * @description A simple implementation of Web Animations API.
   * @version 2.1.25
   * @date 5/23/2025, 7:01:32 AM
   * <AUTHOR>
   * @docs https://g.antv.antgroup.com/
   */var Pu=function(t){function e(t,n,r,i){var a;return o(this,e),(a=p(this,e,[t])).currentTime=r,a.timelineTime=i,a.target=n,a.type="finish",a.bubbles=!1,a.currentTarget=n,a.defaultPrevented=!1,a.eventPhase=a.AT_TARGET,a.timeStamp=Date.now(),a.currentTime=r,a.timelineTime=i,a}return y(e,t),u(e)}(So),Su=0,Au=function(){return u((function t(e,n){var r;o(this,t),this.currentTimePending=!1,this._idle=!0,this._paused=!1,this._finishedFlag=!0,this._currentTime=0,this._playbackRate=1,this._inTimeline=!0,this.effect=e,e.animation=this,this.timeline=n,this.id="".concat(Su++),this._inEffect=!!this.effect.update(0),this._totalDuration=Number(null===(r=this.effect)||void 0===r?void 0:r.getComputedTiming().endTime),this._holdTime=0,this._paused=!1,this.oldPlayState="idle",this.updatePromises()}),[{key:"pending",get:function(){return null===this._startTime&&!this._paused&&0!==this.playbackRate||this.currentTimePending}},{key:"playState",get:function(){return this._idle?"idle":this._isFinished?"finished":this._paused?"paused":"running"}},{key:"ready",get:function(){var t=this;return this.readyPromise||(-1===this.timeline.animationsWithPromises.indexOf(this)&&this.timeline.animationsWithPromises.push(this),this.readyPromise=new Promise((function(e,n){t.resolveReadyPromise=function(){e(t)},t.rejectReadyPromise=function(){n(Error())}})),this.pending||this.resolveReadyPromise()),this.readyPromise}},{key:"finished",get:function(){var t=this;return this.finishedPromise||(-1===this.timeline.animationsWithPromises.indexOf(this)&&this.timeline.animationsWithPromises.push(this),this.finishedPromise=new Promise((function(e,n){t.resolveFinishedPromise=function(){e(t)},t.rejectFinishedPromise=function(){n(Error())}})),"finished"===this.playState&&this.resolveFinishedPromise()),this.finishedPromise}},{key:"currentTime",get:function(){return this.updatePromises(),this._idle||this.currentTimePending?null:this._currentTime},set:function(t){if(!isNaN(t=Number(t))){var e;if(this.timeline.restart(),!this._paused&&null!==this._startTime)this._startTime=Number(null===(e=this.timeline)||void 0===e?void 0:e.currentTime)-t/this.playbackRate;this.currentTimePending=!1,this._currentTime!==t&&(this._idle&&(this._idle=!1,this._paused=!0),this.tickCurrentTime(t,!0),this.timeline.applyDirtiedAnimation(this))}}},{key:"startTime",get:function(){return this._startTime},set:function(t){if(null!==t){if(this.updatePromises(),isNaN(t=Number(t)))return;if(this._paused||this._idle)return;this._startTime=t,this.tickCurrentTime((Number(this.timeline.currentTime)-this._startTime)*this.playbackRate),this.timeline.applyDirtiedAnimation(this),this.updatePromises()}}},{key:"playbackRate",get:function(){return this._playbackRate},set:function(t){if(t!==this._playbackRate){this.updatePromises();var e=this.currentTime;this._playbackRate=t,this.startTime=null,"paused"!==this.playState&&"idle"!==this.playState&&(this._finishedFlag=!1,this._idle=!1,this.ensureAlive(),this.timeline.applyDirtiedAnimation(this)),null!==e&&(this.currentTime=e),this.updatePromises()}}},{key:"_isFinished",get:function(){return!this._idle&&(this._playbackRate>0&&Number(this._currentTime)>=this._totalDuration||0>this._playbackRate&&0>=Number(this._currentTime))}},{key:"totalDuration",get:function(){return this._totalDuration}},{key:"_needsTick",get:function(){return this.pending||"running"===this.playState||!this._finishedFlag}},{key:"updatePromises",value:function(){var t;if(null!==(t=this.effect.target)&&void 0!==t&&t.destroyed)return this.readyPromise=void 0,this.finishedPromise=void 0,!1;var e=this.oldPlayState,n=this.pending?"pending":this.playState;return this.readyPromise&&n!==e&&("idle"===n?(this.rejectReadyPromise(),this.readyPromise=void 0):"pending"===e?this.resolveReadyPromise():"pending"===n&&(this.readyPromise=void 0)),this.finishedPromise&&n!==e&&("idle"===n?(this.rejectFinishedPromise(),this.finishedPromise=void 0):"finished"===n?this.resolveFinishedPromise():"finished"===e&&(this.finishedPromise=void 0)),this.oldPlayState=n,this.readyPromise||this.finishedPromise}},{key:"play",value:function(){this.updatePromises(),this._paused=!1,(this._isFinished||this._idle)&&(this.rewind(),this._startTime=null),this._finishedFlag=!1,this._idle=!1,this.ensureAlive(),this.timeline.applyDirtiedAnimation(this),-1===this.timeline.animations.indexOf(this)&&this.timeline.animations.push(this),this.updatePromises()}},{key:"pause",value:function(){this.updatePromises(),this.currentTime&&(this._holdTime=this.currentTime),this._isFinished||this._paused||this._idle?this._idle&&(this.rewind(),this._idle=!1):this.currentTimePending=!0,this._startTime=null,this._paused=!0,this.updatePromises()}},{key:"finish",value:function(){this.updatePromises(),this._idle||(this.currentTime=this._playbackRate>0?this._totalDuration:0,this._startTime=this._totalDuration-this.currentTime,this.currentTimePending=!1,this.timeline.applyDirtiedAnimation(this),this.updatePromises())}},{key:"cancel",value:function(){var t=this;if(this.updatePromises(),this._inEffect&&(this._inEffect=!1,this._idle=!0,this._paused=!1,this._finishedFlag=!0,this._currentTime=0,this._startTime=null,this.effect.update(null),this.timeline.applyDirtiedAnimation(this),this.updatePromises(),this.oncancel)){var e=new Pu(null,this,this.currentTime,null);setTimeout((function(){t.oncancel(e)}))}}},{key:"reverse",value:function(){this.updatePromises();var t=this.currentTime;this.playbackRate*=-1,this.play(),null!==t&&(this.currentTime=t),this.updatePromises()}},{key:"updatePlaybackRate",value:function(t){this.playbackRate=t}},{key:"targetAnimations",value:function(){var t;return(null===(t=this.effect)||void 0===t?void 0:t.target).getAnimations()}},{key:"markTarget",value:function(){var t=this.targetAnimations();-1===t.indexOf(this)&&t.push(this)}},{key:"unmarkTarget",value:function(){var t=this.targetAnimations(),e=t.indexOf(this);-1!==e&&t.splice(e,1)}},{key:"tick",value:function(t,e){this._idle||this._paused||(null===this._startTime?e&&(this.startTime=t-this._currentTime/this.playbackRate):this._isFinished||this.tickCurrentTime((t-this._startTime)*this.playbackRate)),e&&(this.currentTimePending=!1,this.fireEvents(t))}},{key:"rewind",value:function(){if(this.playbackRate<0){if(this._totalDuration>=1/0)throw Error("Unable to rewind negative playback rate animation with infinite duration");this.currentTime=this._totalDuration}else this.currentTime=0}},{key:"persist",value:function(){throw Error(gn)}},{key:"addEventListener",value:function(t,e,n){throw Error(gn)}},{key:"removeEventListener",value:function(t,e,n){throw Error(gn)}},{key:"dispatchEvent",value:function(t){throw Error(gn)}},{key:"commitStyles",value:function(){throw Error(gn)}},{key:"ensureAlive",value:function(){var t,e;0>this.playbackRate&&0===this.currentTime?this._inEffect=!(null===(t=this.effect)||void 0===t||!t.update(-1)):this._inEffect=!(null===(e=this.effect)||void 0===e||!e.update(this.currentTime));this._inTimeline||!this._inEffect&&this._finishedFlag||(this._inTimeline=!0,this.timeline.animations.push(this))}},{key:"tickCurrentTime",value:function(t,e){t!==this._currentTime&&(this._currentTime=t,this._isFinished&&!e&&(this._currentTime=this._playbackRate>0?this._totalDuration:0),this.ensureAlive())}},{key:"fireEvents",value:function(t){var e=this;if(this._isFinished){if(!this._finishedFlag){if(this.onfinish){var n=new Pu(null,this,this.currentTime,t);setTimeout((function(){e.onfinish&&e.onfinish(n)}))}this._finishedFlag=!0}}else{if(this.onframe&&"running"===this.playState){var r=new Pu(null,this,this.currentTime,t);this.onframe(r)}this._finishedFlag=!1}}}])}(),Cu=.1,Ru="function"==typeof Float32Array,Ou=function(t,e){return 1-3*e+3*t},Lu=function(t,e){return 3*e-6*t},Iu=function(t){return 3*t},_u=function(t,e,n){return((Ou(e,n)*t+Lu(e,n))*t+Iu(e))*t},Du=function(t,e,n){return 3*Ou(e,n)*t*t+2*Lu(e,n)*t+Iu(e)},Fu=function(t,e,n,r){if(0>t||t>1||0>n||n>1)throw Error("bezier x values must be in [0, 1] range");if(t===e&&n===r)return function(t){return t};for(var i=Ru?new Float32Array(11):[,,,,,,,,,,,],a=0;11>a;++a)i[a]=_u(a*Cu,t,n);var o=function(e){for(var r=0,a=1;10!==a&&e>=i[a];++a)r+=Cu;--a;var o=r+(e-i[a])/(i[a+1]-i[a])*Cu,s=Du(o,t,n);return.001>s?0===s?o:function(t,e,n,r,i){var a,o,s=0;do{(a=_u(o=e+(n-e)/2,r,i)-t)>0?n=o:e=o}while(Math.abs(a)>1e-7&&10>++s);return o}(e,r,r+Cu,t,n):function(t,e,n,r){for(var i=0;4>i;++i){var a=Du(e,n,r);if(0===a)return e;e-=(_u(e,n,r)-t)/a}return e}(e,o,t,n)};return function(t){return 0===t||1===t?t:_u(o(t),e,r)}},Gu=function(t){return Math.pow(t,2)},Bu=function(t){return Math.pow(t,3)},Vu=function(t){return Math.pow(t,4)},Yu=function(t){return Math.pow(t,5)},Uu=function(t){return Math.pow(t,6)},zu=function(t){return 1-Math.cos(t*Math.PI/2)},ju=function(t){return 1-Math.sqrt(1-t*t)},Xu=function(t){return t*t*(3*t-2)},Hu=function(t){for(var e,n=4;t<((e=Math.pow(2,--n))-1)/11;);return 1/Math.pow(4,3-n)-7.5625*Math.pow((3*e-2)/22-t,2)},Wu=function(t){var e=g(arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],2),n=e[0],r=e[1],i=void 0===r?.5:r,a=Ut(Number(void 0===n?1:n),1,10),o=Ut(Number(i),.1,2);return 0===t||1===t?t:-a*Math.pow(2,10*(t-1))*Math.sin(2*Math.PI*(t-1-o/(2*Math.PI)*Math.asin(1/a))/o)},qu=function(t){var e=arguments.length>2?arguments[2]:void 0,n=g(arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],4),r=n[0],i=void 0===r?1:r,a=n[1],o=void 0===a?100:a,s=n[2],u=void 0===s?10:s,l=n[3],c=void 0===l?0:l;i=Ut(i,.1,1e3),o=Ut(o,.1,1e3),u=Ut(u,.1,1e3),c=Ut(c,.1,1e3);var h=Math.sqrt(o/i),f=u/(2*Math.sqrt(o*i)),d=1>f?h*Math.sqrt(1-f*f):0,v=1>f?(f*h-c)/d:-c+h,p=e?e*t/1e3:t;return p=1>f?Math.exp(-p*f*h)*(1*Math.cos(d*p)+v*Math.sin(d*p)):(1+v*p)*Math.exp(-p*h),0===t||1===t?t:1-p},Zu=function(t){var e=g(arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],2),n=e[0],r=void 0===n?10:n;return("start"===e[1]?Math.ceil:Math.floor)(Ut(t,0,1)*r)/r},Ku=function(t){var e=g(arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],4);return Fu(e[0],e[1],e[2],e[3])(t)},Qu=Fu(.42,0,1,1),$u=function(t){return function(e){return 1-t(1-e,arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],arguments.length>2?arguments[2]:void 0)}},Ju=function(t){return function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=arguments.length>2?arguments[2]:void 0;return.5>e?t(2*e,n,r)/2:1-t(-2*e+2,n,r)/2}},tl=function(t){return function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=arguments.length>2?arguments[2]:void 0;return.5>e?(1-t(1-2*e,n,r))/2:(t(2*e-1,n,r)+1)/2}},el={steps:Zu,"step-start":function(t){return Zu(t,[1,"start"])},"step-end":function(t){return Zu(t,[1,"end"])},linear:function(t){return t},"cubic-bezier":Ku,ease:function(t){return Ku(t,[.25,.1,.25,1])},in:Qu,out:$u(Qu),"in-out":Ju(Qu),"out-in":tl(Qu),"in-quad":Gu,"out-quad":$u(Gu),"in-out-quad":Ju(Gu),"out-in-quad":tl(Gu),"in-cubic":Bu,"out-cubic":$u(Bu),"in-out-cubic":Ju(Bu),"out-in-cubic":tl(Bu),"in-quart":Vu,"out-quart":$u(Vu),"in-out-quart":Ju(Vu),"out-in-quart":tl(Vu),"in-quint":Yu,"out-quint":$u(Yu),"in-out-quint":Ju(Yu),"out-in-quint":tl(Yu),"in-expo":Uu,"out-expo":$u(Uu),"in-out-expo":Ju(Uu),"out-in-expo":tl(Uu),"in-sine":zu,"out-sine":$u(zu),"in-out-sine":Ju(zu),"out-in-sine":tl(zu),"in-circ":ju,"out-circ":$u(ju),"in-out-circ":Ju(ju),"out-in-circ":tl(ju),"in-back":Xu,"out-back":$u(Xu),"in-out-back":Ju(Xu),"out-in-back":tl(Xu),"in-bounce":Hu,"out-bounce":$u(Hu),"in-out-bounce":Ju(Hu),"out-in-bounce":tl(Hu),"in-elastic":Wu,"out-elastic":$u(Wu),"in-out-elastic":Ju(Wu),"out-in-elastic":tl(Wu),spring:qu,"spring-in":qu,"spring-out":$u(qu),"spring-in-out":Ju(qu),"spring-out-in":tl(qu)},nl=function(t){return t};function rl(t,e){return function(n){if(n>=1)return 1;var r=1/t;return(n+=e*r)-n%r}}var il="\\s*(-?\\d+\\.?\\d*|-?\\.\\d+)\\s*",al=RegExp("cubic-bezier\\(".concat(il,",").concat(il,",").concat(il,",").concat(il,"\\)")),ol=/steps\(\s*(\d+)\s*\)/,sl=/steps\(\s*(\d+)\s*,\s*(start|middle|end)\s*\)/;function ul(t){var e=al.exec(t);if(e)return Fu.apply(void 0,h(e.slice(1).map(Number)));var n=ol.exec(t);if(n)return rl(Number(n[1]),0);var r=sl.exec(t);return r?rl(Number(r[1]),{start:1,middle:.5,end:0}[r[2]]):el[function(t){return function(t){return"-"===(t=t.replace(/([A-Z])/g,(function(t){return"-".concat(t.toLowerCase())}))).charAt(0)?t.substring(1):t}(t).replace(/^ease-/,"").replace(/(\(|\s).+/,"").toLowerCase().trim()}(t)]||el.linear}function ll(t){return Math.abs(function(t){var e;if(0===t.duration||0===t.iterations)return 0;return("auto"===t.duration?0:Number(t.duration))*(null!==(e=t.iterations)&&void 0!==e?e:1)}(t)/(t.playbackRate||1))}function cl(t,e,n){var r=function(t,e,n){if(null===e)return 0;var r=n.endTime;return Math.min(n.delay,r)>e?1:Math.min(n.delay+t+n.endDelay,r)>e?3:2}(t,e,n),i=function(t,e,n,r,i){switch(r){case 1:return"backwards"===e||"both"===e?0:null;case 3:return n-i;case 2:return"forwards"===e||"both"===e?t:null;case 0:return null}}(t,n.fill,e,r,n.delay);if(null===i)return null;var a="auto"===n.duration?0:n.duration,o=function(t,e,n,r,i){var a=i;return 0===t?1!==e&&(a+=n):a+=r/t,a}(a,r,n.iterations,i,n.iterationStart),s=function(t,e,n,r,i,a){var o=t===1/0?e%1:t%1;return 0!==o||2!==n||0===r||0===i&&0!==a||(o=1),o}(o,n.iterationStart,r,n.iterations,i,a),u=function(t,e,n,r){return 2===t&&e===1/0?1/0:1===n?Math.floor(r)-1:Math.floor(r)}(r,n.iterations,s,o),l=function(t,e,n){var r=t;if("normal"!==t&&"reverse"!==t){var i=e;"alternate-reverse"===t&&(i+=1),r="normal",i!==1/0&&i%2!=0&&(r="reverse")}return"normal"===r?n:1-n}(n.direction,u,s);return n.currentIteration=u,n.progress=l,n.easingFunction(l)}function hl(t,e,n){var r=function(t,e){for(var n={},r=0;t.length>r;r++)for(var i in t[r])if(fl(i)){var a={offset:t[r].offset,computedOffset:t[r].computedOffset,easing:t[r].easing,easingFunction:ul(t[r].easing)||e.easingFunction,value:t[r][i]};n[i]=n[i]||[],n[i].push(a)}return n}(t,e),i=function(t,e){var n=[];for(var r in t)for(var i=t[r],a=0;i.length-1>a;a++){var o=a,s=a+1,u=i[o].computedOffset,l=i[s].computedOffset,c=u,h=l;0===a&&(c=-1/0,0===l&&(s=o)),a===i.length-2&&(h=1/0,1===u&&(o=s)),n.push({applyFrom:c,applyTo:h,startOffset:i[o].computedOffset,endOffset:i[s].computedOffset,easingFunction:i[o].easingFunction,property:r,interpolation:vl(r,i[o].value,i[s].value,e)})}return n.sort((function(t,e){return t.startOffset-e.startOffset})),n}(r,n);return function(t,e){if(null!==e)i.filter((function(t){return e>=t.applyFrom&&t.applyTo>e})).forEach((function(n){var r=n.endOffset-n.startOffset;t.setAttribute(n.property,n.interpolation(0===r?0:(e-n.startOffset)/r),!1,!1)}));else for(var n in r)fl(n)&&t.setAttribute(n,null)}}function fl(t){return"offset"!==t&&"easing"!==t&&"composite"!==t&&"computedOffset"!==t}var dl=function(t,e,n){return function(r){var i=pl(t,e,r);return zt(i)?i:n(i)}};function vl(t,e,n,r){var i=pa[t];if(i&&i.syntax&&i.int){var a=ks.styleValueRegistry.getPropertySyntax(i.syntax);if(a){var o=a.parser,s=o?o(e,r):e,u=o?o(n,r):n,l=a.mixer(s,u,r);if(l){var c=dl.apply(void 0,h(l));return function(t){return 0===t?e:1===t?n:c(t)}}}}return dl(!1,!0,(function(t){return t?n:e}))}function pl(t,e,n){if("number"==typeof t&&"number"==typeof e)return t*(1-n)+e*n;if("boolean"==typeof t&&"boolean"==typeof e||"string"==typeof t&&"string"==typeof e)return.5>n?t:e;if(Array.isArray(t)&&Array.isArray(e)){for(var r=t.length,i=e.length,a=Math.max(r,i),o=[],s=0;a>s;s++)o.push(pl(t[r>s?s:r-1],e[i>s?s:i-1],n));return o}throw Error("Mismatched interpolation arguments ".concat(t,":").concat(e))}var ml=function(){return u((function t(){o(this,t),this.delay=0,this.direction="normal",this.duration="auto",this._easing="linear",this.easingFunction=nl,this.endDelay=0,this.fill="auto",this.iterationStart=0,this.iterations=1,this.currentIteration=null,this.progress=null}),[{key:"easing",get:function(){return this._easing},set:function(t){this.easingFunction=ul(t),this._easing=t}}])}();function yl(t,e){if(null===t)return[];Array.isArray(t)||(t=function(t){var e=[];for(var n in t)if(!(n in["easing","offset","composite"])){var r=t[n];Array.isArray(r)||(r=[r]);for(var i=r.length,a=0;i>a;a++){if(!e[a]){var o={};"offset"in t&&(o.offset=Number(t.offset)),"easing"in t&&(o.easing=t.easing),"composite"in t&&(o.composite=t.composite),e[a]=o}null!=r[a]&&(e[a][n]=r[a])}}return e.sort((function(t,e){return(t.computedOffset||0)-(e.computedOffset||0)})),e}(t));for(var n=t.map((function(t){var n={};for(var r in null!=e&&e.composite&&(n.composite="auto"),t){var i=t[r];if("offset"===r){if(null!==i){if(!isFinite(i=Number(i)))throw Error("Keyframe offsets must be numbers.");if(0>i||i>1)throw Error("Keyframe offsets must be between 0 and 1.");n.computedOffset=i}}else if("composite"===r&&-1===["replace","add","accumulate","auto"].indexOf(i))throw Error("".concat(i," compositing is not supported"));n[r]=i}return void 0===n.offset&&(n.offset=null),void 0===n.easing&&(n.easing=(null==e?void 0:e.easing)||"linear"),void 0===n.composite&&(n.composite="auto"),n})),r=!0,i=-1/0,a=0;n.length>a;a++){var o=n[a].offset;if(_t(o))r=!1;else{if(i>o)throw new TypeError("Keyframes are not loosely sorted by offset. Sort or specify offsets.");i=o}}return n=n.filter((function(t){return Number(t.offset)>=0&&1>=Number(t.offset)})),r||function(){var t,e,r=n.length;n[r-1].computedOffset=Number(null!==(t=n[r-1].offset)&&void 0!==t?t:1),r>1&&(n[0].computedOffset=Number(null!==(e=n[0].offset)&&void 0!==e?e:0));for(var i=0,a=Number(n[0].computedOffset),o=1;r>o;o++){var s=n[o].computedOffset;if(!_t(s)&&!_t(a)){for(var u=1;o-i>u;u++)n[i+u].computedOffset=a+(Number(s)-a)*u/(o-i);i=o,a=Number(s)}}}(),n}var gl="backwards|forwards|both|none".split("|"),kl="reverse|alternate|alternate-reverse".split("|");function El(t,e){var n=new ml;return e&&(n.fill="both",n.duration="auto"),"number"!=typeof t||isNaN(t)?void 0!==t&&Object.keys(t).forEach((function(e){if(null!=t[e]&&"auto"!==t[e]){if(("number"==typeof n[e]||"duration"===e)&&("number"!=typeof t[e]||isNaN(t[e])))return;if("fill"===e&&-1===gl.indexOf(t[e]))return;if("direction"===e&&-1===kl.indexOf(t[e]))return;n[e]=t[e]}})):n.duration=t,n}function xl(t,e){return El(t=bl(null!=t?t:{duration:"auto"}),e)}function bl(t){return"number"==typeof t&&(t=isNaN(t)?{duration:"auto"}:{duration:t}),t}var Tl=function(){return u((function t(e,n,r){var i=this;o(this,t),this.composite="replace",this.iterationComposite="replace",this.target=e,this.timing=xl(r,!1),this.timing.effect=this,this.timing.activeDuration=ll(this.timing),this.timing.endTime=Math.max(0,this.timing.delay+this.timing.activeDuration+this.timing.endDelay),this.normalizedKeyframes=yl(n,this.timing),this.interpolations=hl(this.normalizedKeyframes,this.timing,this.target);var a=ks.globalThis.Proxy;this.computedTiming=a?new a(this.timing,{get:function(t,e){return"duration"===e?"auto"===t.duration?0:t.duration:"fill"===e?"auto"===t.fill?"none":t.fill:"localTime"===e?i.animation&&i.animation.currentTime||null:"currentIteration"===e?i.animation&&"running"===i.animation.playState?t.currentIteration||0:null:"progress"===e?i.animation&&"running"===i.animation.playState?t.progress||0:null:t[e]},set:function(){return!0}}):this.timing}),[{key:"applyInterpolations",value:function(){this.interpolations(this.target,Number(this.timeFraction))}},{key:"update",value:function(t){return null!==t&&(this.timeFraction=cl(this.timing.activeDuration,t,this.timing),null!==this.timeFraction)}},{key:"getKeyframes",value:function(){return this.normalizedKeyframes}},{key:"setKeyframes",value:function(t){this.normalizedKeyframes=yl(t)}},{key:"getComputedTiming",value:function(){return this.computedTiming}},{key:"getTiming",value:function(){return this.timing}},{key:"updateTiming",value:function(t){var e=this;Object.keys(t||{}).forEach((function(n){e.timing[n]=t[n]}))}}])}();function Ml(t,e){return Number(t.id)-Number(e.id)}var wl=function(){return u((function t(e){var n=this;o(this,t),this.animations=[],this.ticking=!1,this.timelineTicking=!1,this.hasRestartedThisFrame=!1,this.animationsWithPromises=[],this.inTick=!1,this.pendingEffects=[],this.currentTime=null,this.rafId=0,this.rafCallbacks=[],this.webAnimationsNextTick=function(t){n.currentTime=t,n.discardAnimations(),0===n.animations.length?n.timelineTicking=!1:n.requestAnimationFrame(n.webAnimationsNextTick)},this.processRafCallbacks=function(t){var e=n.rafCallbacks;n.rafCallbacks=[],Number(n.currentTime)>t&&(t=Number(n.currentTime)),n.animations.sort(Ml),n.animations=n.tick(t,!0,n.animations)[0],e.forEach((function(e){e[1](t)})),n.applyPendingEffects()},this.document=e}),[{key:"getAnimations",value:function(){return this.discardAnimations(),this.animations.slice()}},{key:"isTicking",value:function(){return this.inTick}},{key:"play",value:function(t,e,n){var r=new Tl(t,e,n),i=new Au(r,this);return this.animations.push(i),this.restartWebAnimationsNextTick(),i.updatePromises(),i.play(),i.updatePromises(),i}},{key:"applyDirtiedAnimation",value:function(t){var e=this;if(!this.inTick){t.markTarget();var n=t.targetAnimations();n.sort(Ml),this.tick(Number(this.currentTime),!1,n.slice())[1].forEach((function(t){var n=e.animations.indexOf(t);-1!==n&&e.animations.splice(n,1)})),this.applyPendingEffects()}}},{key:"restart",value:function(){return this.ticking||(this.ticking=!0,this.requestAnimationFrame((function(){})),this.hasRestartedThisFrame=!0),this.hasRestartedThisFrame}},{key:"destroy",value:function(){this.document.defaultView.cancelAnimationFrame(this.frameId)}},{key:"applyPendingEffects",value:function(){this.pendingEffects.forEach((function(t){null==t||t.applyInterpolations()})),this.pendingEffects=[]}},{key:"updateAnimationsPromises",value:function(){this.animationsWithPromises=this.animationsWithPromises.filter((function(t){return t.updatePromises()}))}},{key:"discardAnimations",value:function(){this.updateAnimationsPromises(),this.animations=this.animations.filter((function(t){return"finished"!==t.playState&&"idle"!==t.playState}))}},{key:"restartWebAnimationsNextTick",value:function(){this.timelineTicking||(this.timelineTicking=!0,this.requestAnimationFrame(this.webAnimationsNextTick))}},{key:"rAF",value:function(t){var e=this.rafId++;return 0===this.rafCallbacks.length&&(this.frameId=this.document.defaultView.requestAnimationFrame(this.processRafCallbacks)),this.rafCallbacks.push([e,t]),e}},{key:"requestAnimationFrame",value:function(t){var e=this;return this.rAF((function(n){e.updateAnimationsPromises(),t(n),e.updateAnimationsPromises()}))}},{key:"tick",value:function(t,e,n){var r,i,a=this;this.inTick=!0,this.hasRestartedThisFrame=!1,this.currentTime=t,this.ticking=!1;var o=[],s=[],u=[],l=[];return n.forEach((function(n){n.tick(t,e),n._inEffect?(s.push(n.effect),n.markTarget()):(o.push(n.effect),n.unmarkTarget()),n._needsTick&&(a.ticking=!0);var r=n._inEffect||n._needsTick;n._inTimeline=r,r?u.push(n):l.push(n)})),(r=this.pendingEffects).push.apply(r,o),(i=this.pendingEffects).push.apply(i,s),this.ticking&&this.requestAnimationFrame((function(){})),this.inTick=!1,[u,l]}}])}();ks.EasingFunction=ul,ks.AnimationTimeline=wl,t.AABB=fn,t.AbstractRenderer=an,t.AbstractRendererPlugin=rn,t.AdvancedCamera=vu,t.Animation=Au,t.AnimationEvent=Pu,t.AnimationTimeline=wl,t.BUILT_IN_PROPERTIES=da,t.CSS=tu,t.CSSGradientValue=Vr,t.CSSKeywordValue=Yr,t.CSSRGB=Hr,t.CSSStyleValue=Fr,t.CSSUnitValue=zr,t.Camera=zn,t.CameraEvent=Yn,t.CameraProjectionMode=Vn,t.CameraTrackingMode=Bn,t.CameraType=Gn,t.Canvas=du,t.CanvasEvent=su,t.Circle=Ls,t.CircleUpdater=ko,t.ClipSpaceNearZ=nn,t.CustomElement=_s,t.CustomElementRegistry=Js,t.CustomEvent=Oo,t.DefaultSceneGraphSelector=Yo,t.DefaultSceneGraphService=ds,t.DisplayObject=Os,t.Document=eu,t.ERROR_MSG_METHOD_NOT_IMPLEMENTED=gn,t.EasingFunctions=el,t.Element=Ns,t.ElementEvent=Uo,t.Ellipse=Ds,t.EllipseUpdater=Eo,t.EventService=Do,t.EventTarget=Io,t.FederatedEvent=So,t.FederatedMouseEvent=Ao,t.FederatedPointerEvent=Co,t.FederatedWheelEvent=Ro,t.Fragment=Fs,t.Frustum=pn,t.GradientType=Br,t.Group=Gs,t.GroupUpdater=No,t.HTML=Vs,t.HTMLUpdater=Po,t.Image=Ys,t.KeyframeEffect=Tl,t.Line=zs,t.LineUpdater=xo,t.Mask=vn,t.MutationEvent=zo,t.MutationObserver=xu,t.MutationRecord=yu,t.Node=_o,t.OffscreenCanvasCreator=Fo,t.Path=Xs,t.PathUpdater=bo,t.Plane=dn,t.Point=mn,t.Polygon=Ws,t.Polyline=Zs,t.PolylineUpdater=To,t.PropertySyntax=Wn,t.RBush=tn,t.Rect=Ks,t.RectUpdater=Mo,t.Rectangle=yn,t.Registration=Eu,t.RenderReason=Go,t.RenderingService=Bo,t.Shape=en,t.SortReason=Xn,t.Strategy=jn,t.Text=$s,t.TextService=gs,t.TextUpdater=wo,t.UnitType=Sr,t.attrModifiedEvent=As,t.compareAnimations=Ml,t.computeLinearGradient=function(t,e,n,r){var i=Mn(r.value),a=0+e/2,o=0+n/2,s=Math.abs(e*Math.cos(i))+Math.abs(n*Math.sin(i));return{x1:t[0]+a-Math.cos(i)*s/2,y1:t[1]+o-Math.sin(i)*s/2,x2:t[0]+a+Math.cos(i)*s/2,y2:t[1]+o+Math.sin(i)*s/2}},t.computeRadialGradient=function(t,e,n,r,i,a){var o=r.value,s=i.value;r.unit===Sr.kPercentage&&(o=r.value/100*e),i.unit===Sr.kPercentage&&(s=i.value/100*n);var u=Math.max(ke([0,0],[o,s]),ke([0,n],[o,s]),ke([e,n],[o,s]),ke([e,0],[o,s]));return a&&(a instanceof zr?u=a.value:a instanceof Yr&&("closest-side"===a.value?u=Math.min(o,e-o,s,n-s):"farthest-side"===a.value?u=Math.max(o,e-o,s,n-s):"closest-corner"===a.value&&(u=Math.min(ke([0,0],[o,s]),ke([0,n],[o,s]),ke([e,n],[o,s]),ke([e,0],[o,s]))))),{x:o+t[0],y:s+t[1],r:u}},t.convertToPath=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t.getLocalTransform(),n=[];switch(t.nodeName){case en.LINE:var r=t.parsedStyle,i=r.x1,a=r.y1,o=r.x2,s=r.y2;n=function(t,e,n,r){return[["M",t,e],["L",n,r]]}(void 0===i?0:i,void 0===a?0:a,void 0===o?0:o,void 0===s?0:s);break;case en.CIRCLE:var u=t.parsedStyle,l=u.r,c=void 0===l?0:l,f=u.cx,d=u.cy;n=Ui(c,c,void 0===f?0:f,void 0===d?0:d);break;case en.ELLIPSE:var v=t.parsedStyle,p=v.rx,m=v.ry,y=v.cx,k=v.cy;n=Ui(void 0===p?0:p,void 0===m?0:m,void 0===y?0:y,void 0===k?0:k);break;case en.POLYLINE:case en.POLYGON:n=function(t,e){var n=t.map((function(t,e){return[0===e?"M":"L",t[0],t[1]]}));return e&&n.push(["Z"]),n}(t.parsedStyle.points.points,t.nodeName===en.POLYGON);break;case en.RECT:var E=t.parsedStyle,x=E.width,b=void 0===x?0:x,T=E.height,M=void 0===T?0:T,w=E.x,N=void 0===w?0:w,P=E.y,S=void 0===P?0:P,A=E.radius,C=A&&A.some((function(t){return 0!==t}));n=function(t,e,n,r,i){if(i){var a=g(i,4),o=a[0],s=a[1],u=a[2],l=a[3],c=t>0?1:-1,h=e>0?1:-1,f=c+h!==0?1:0;return[["M",c*o+n,r],["L",t-c*s+n,r],s?["A",s,s,0,0,f,t+n,h*s+r]:null,["L",t+n,e-h*u+r],u?["A",u,u,0,0,f,t+n-c*u,e+r]:null,["L",n+c*l,e+r],l?["A",l,l,0,0,f,n,e+r-h*l]:null,["L",n,h*o+r],o?["A",o,o,0,0,f,c*o+n,r]:null,["Z"]].filter((function(t){return t}))}return[["M",n,r],["L",n+t,r],["L",n+t,r+e],["L",n,r+e],["Z"]]}(b,M,N,S,C&&A.map((function(t){return Ut(t,0,Math.min(Math.abs(b)/2,Math.abs(M)/2))})));break;case en.PATH:n=h(t.parsedStyle.d.absolutePath)}if(n.length)return function(t,e,n){return t.reduce((function(t,e){var r="";if("M"===e[0]||"L"===e[0]){var i=at(e[1],e[2],0);n&&pt(i,i,n),r="".concat(e[0]).concat(i[0],",").concat(i[1])}else if("Z"===e[0])r=e[0];else if("C"===e[0]){var a=at(e[1],e[2],0),o=at(e[3],e[4],0),s=at(e[5],e[6],0);n&&(pt(a,a,n),pt(o,o,n),pt(s,s,n)),r="".concat(e[0]).concat(a[0],",").concat(a[1],",").concat(o[0],",").concat(o[1],",").concat(s[0],",").concat(s[1])}else if("A"===e[0]){var u=at(e[6],e[7],0);n&&pt(u,u,n),r="".concat(e[0]).concat(e[1],",").concat(e[2],",").concat(e[3],",").concat(e[4],",").concat(e[5],",").concat(u[0],",").concat(u[1])}else if("Q"===e[0]){var l=at(e[1],e[2],0),c=at(e[3],e[4],0);n&&(pt(l,l,n),pt(c,c,n)),r="".concat(e[0]).concat(e[1],",").concat(e[2],",").concat(e[3],",").concat(e[4],"}")}return t+r}),"")}(n,0,e)},t.createVec3=bn,t.decompose=Rn,t.definedProps=function(t){return Object.fromEntries(Object.entries(t).filter((function(t){return void 0!==g(t,2)[1]})))},t.deg2rad=Mn,t.deg2turn=function(t){return t/360},t.destroyEvent=ws,t.findClosestClipPathTarget=Ba,t.fromRotationTranslationScale=Cn,t.getAngle=En,t.getEuler=An,t.getOrCalculatePathTotalLength=Ri,t.grad2deg=function(t){return 0>(t%=400)&&(t+=400),.9*t},t.insertedEvent=Ts,t.isBrowser=Da,t.isCSSGradientValue=function(t){return!!t.type&&!!t.value},t.isCSSRGB=ci,t.isCanvas=function(t){return!!t.document},t.isDisplayObject=Ps,t.isFederatedEvent=function(t){return!!t.type},t.isFillOrStrokeAffected=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"auto",e=arguments.length>1?arguments[1]:void 0,n=arguments.length>2?arguments[2]:void 0,r=!1,i=!1;return"visiblepainted"===t||"painted"===t||"auto"===t?(r=!!e&&!e.isNone,i=!!n&&!n.isNone):"visiblefill"===t||"fill"===t?r=!0:"visiblestroke"===t||"stroke"===t?i=!0:"visible"!==t&&"all"!==t||(r=!0,i=!0),[r,i]},t.isPattern=li,t.makeTiming=El,t.mergeColors=fi,t.normalizeKeyframes=yl,t.normalizeTimingInput=xl,t.numericTimingToObject=bl,t.parseColor=hi,t.parseLength=pi,t.parsePath=Xi,t.parseTransform=ta,t.parsedTransformToMat4=po,t.propertyMetadataCache=pa,t.rad2deg=Nn,t.removedEvent=Ms,t.resetEntityCounter=function(){bs=0},t.runtime=ks,t.setDOMSize=function(t,e,n){Da&&t.style&&(t.style.width=e+"px",t.style.height=n+"px")},t.translatePathToString=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,a=t.map((function(a,o){var s=a[0],u=t[o+1],l=(o===t.length-1||u&&("M"===u[0]||"Z"===u[0]))&&0!==r&&0!==i,c=g(0===o&&(0!==e||0!==n)?[e,n]:[0,0],2),h=c[0],f=c[1],d=g(l?[r,i]:[0,0],2),v=d[0],p=d[1];switch(s){case"M":return"M ".concat(a[1]+h,",").concat(a[2]+f);case"L":return"L ".concat(a[1]+v,",").concat(a[2]+p);case"Q":return"Q ".concat(a[1]," ").concat(a[2],",").concat(a[3]+v," ").concat(a[4]+p);case"C":return"C ".concat(a[1]," ").concat(a[2],",").concat(a[3]," ").concat(a[4],",").concat(a[5]+v," ").concat(a[6]+p);case"A":return"A ".concat(a[1]," ").concat(a[2]," ").concat(a[3]," ").concat(a[4]," ").concat(a[5]," ").concat(a[6]," ").concat(a[7]).concat(l?" L ".concat(a[6]+r,",").concat(a[7]+i):"");case"Z":return"Z";default:return null}})).filter((function(t){return null!==t})).join(" ");return~a.indexOf("NaN")?"":a},t.turn2deg=Pn}));
//# sourceMappingURL=index.umd.min.js.map
