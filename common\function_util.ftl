<#-- 获取常用静态类 -->
<#function getWebConstants><#return statics["neo.lib.web.WebConstants"]></#function>
<#function getCommonUtils><#return statics["neo.lib.util.CommonUtils"]></#function>
<#function getMarkdownUtils><#return statics["neo.lib.util.MarkdownUtils"]></#function>
<#function getJsoupUtils><#return statics["neo.lib.util.JsoupUtils"]></#function>
<#function getRandomStringUtils><#return statics["org.apache.commons.lang3.RandomStringUtils"]></#function>
<#function getDigestUtils><#return statics["org.apache.commons.codec.digest.DigestUtils"]></#function>
<#function getJavaScriptUtils><#return statics["neo.lib.util.JavaScriptUtils"]></#function>
<#function getIPUtils><#return statics["neo.lib.util.IPUtils"]></#function>
<#function getGzipUtils><#return statics["neo.lib.util.GzipUtils"]></#function>

<#-- 生成 URL 查询字符串 -->
<#function queryString map ignoreKeys=[] arrayKeys=[]>
  <#local result = "">
  <#list map as mkey, mvalue>
    <#if !ignoreKeys?seq_contains(mkey)>
      <#local qskey = mkey + arrayKeys?seq_contains(mkey)?then("[]","")>
      <#if mvalue?is_sequence>
        <#list mvalue as qsval>
          <#local result += "&" + qskey?url + "=" + qsval?url>
        </#list>
      <#else>
        <#local result += "&" + qskey?url + "=" + mvalue?url>
      </#if>
    </#if>
  </#list>
  <#if result?has_content>
    <#local result = result[1..]>
  </#if>
  <#return result>
</#function>

<#-- 生成浏览器安全访问路径 -->
<#function safeUrl link default="javascript:void(0)">
  <#if link?has_content>
    <#if link?contains(":")>
      <#return link><#-- 外部地址 -->
    <#else>
      <#return base + link?absolute_template_name(.caller_template_name)><#-- 内部地址（相对或绝对均可） -->
    </#if>
  <#else>
    <#return default><#-- 默认值 -->
  </#if>
</#function>

<#-- 加载 JSON 文件 -->
<#function loadJson file ignore_missing=false default="">
  <#outputformat "plainText"><#local jsonData><#include file?absolute_template_name(.caller_template_name) parse=false ignore_missing=ignore_missing></#local></#outputformat>
  <#if jsonData?trim?has_content>
    <#return jsonData?eval_json>
  <#else>
    <#return default>
  </#if>
</#function>

<#-- 格式化 JSON 字符串 -->
<#function formatJson jsonText>
  <#return getCommonUtils().formatJson(jsonText)>
</#function>

<#-- 读取 JSON 字符串到 List -->
<#function json2List jsonText>
  <#return getCommonUtils().json2List(jsonText)>
</#function>

<#-- 读取 JSON 字符串到 Map -->
<#function json2Map jsonText>
  <#return getCommonUtils().json2Map(jsonText)>
</#function>

<#-- 将对象输出为 JSON 字符串 -->
<#function toJson obj>
  <#return getCommonUtils().toJson(obj)>
</#function>

<#-- 快速构建 action 结果 -->
<#function actionResult code message>
  <#return { "code":code, "message":message }>
</#function>

<#-- 在 Java 控制台输出日志 -->
<#function log msg>
  <#if appParams.devMode!false>
    ${getCommonUtils().getLogger().debug("[/" + .caller_template_name + "] {}", msg)}
  </#if>
  <#return "">
</#function>

<#-- 判断文件是否存在 -->
<#function isFileExists fileName>
  <#return getCommonUtils().isFileExists(fileName)>
</#function>

<#-- 获取文件夹内文件列表 -->
<#function listFileNames dirName>
  <#return getCommonUtils().listFileNames(dirName)>
</#function>

<#-- 获取文件夹内子目录列表 -->
<#function listDirNames dirName>
  <#return getCommonUtils().listDirNames(dirName)>
</#function>

<#-- 获取 Zip 压缩包内文件夹及文件列表 -->
<#function listZipFileNames zipFileName>
  <#return getCommonUtils().listZipFileNames(zipFileName)>
</#function>

<#-- 格式化文件尺寸 -->
<#function formatFileSize size>
  <#return getCommonUtils().formatFileSize(size)>
</#function>

<#-- 对文本进行 md5 加密 -->
<#function md5 text>
  <#return getDigestUtils().md5Hex(text)>
</#function>

<#-- 将换行替换为 <br> -->
<#function nl2br text>
  <#outputformat "plainText"><#local htmlText>${text?html}</#local></#outputformat>
  <#return htmlText?replace("\r\n","\n")?replace("\n","<br>")?no_esc>
</#function>

<#-- 将文本转换为行（同时去掉空行） -->
<#function text2lines data sepchar="\n">
  <#local result = []>
  <#list data?replace("\r\n", "\n")?split(sepchar) as row>
    <#if row?trim?has_content>
      <#local result = result + [row?trim]>
    </#if>
  </#list>
  <#return result>
</#function>

<#-- 对比文本差异 -->
<#function diffText text1 text2>
  <#return getCommonUtils.diffText(text1, text2)>
</#function>

<#-- 主动抛出异常 -->
<#function throwException errorInfo>
  ${getCommonUtils().throwException(errorInfo)}
</#function>

<#-- 获取当前用户（模块内）ID -->
<#function currentUserId moduleId="">
  <#if loginUser??>
    <#if moduleId?has_content>
      <#local moduleUserIdKey = "_MODULE_USER_" + moduleId>
      <#local moduleUserId = loginUser[moduleUserIdKey]!"">
      <#if !moduleUserId?has_content>
        <#local defineUserId = loadModuleConfig(moduleId, "defineUserId")>
        <#if defineUserId?is_macro>
          <#local moduleUserId = defineUserId()>
        <#else>
          <#local moduleUserId = loginUser.login_name>
        </#if>
        ${_.putToMap(loginUser, moduleUserIdKey, moduleUserId)}
      </#if>
      <#return moduleUserId>
    <#else>
      <#return loginUser.id>
    </#if>
  <#else>
    <#return "">
  </#if>
</#function>

<#-- 获取当前用户（模块内）角色 -->
<#function currentUserRole moduleId="">
  <#if loginUser??>
    <#if moduleId?has_content>
      <#local moduleUserRoleKey = "_MODULE_ROLE_" + moduleId>
      <#local moduleUserRole = loginUser[moduleUserRoleKey]!"">
      <#if !moduleUserRole?has_content>
        <#local defineUserRole = loadModuleConfig(moduleId, "defineUserRole")>
        <#if defineUserRole?is_macro>
          <#local moduleUserRole = defineUserRole()>
        <#else>
          <#local moduleUserRole = "USER">
        </#if>
        ${_.putToMap(loginUser, moduleUserRoleKey, moduleUserRole)}
      </#if>
      <#return moduleUserRole>
    <#else>
      <#return (loginUser.role)!"UNKNOWN">
    </#if>
  <#else>
    <#return "GUEST">
  </#if>
</#function>

<#-- 获取 Macro 参数名称 -->
<#function getMacroParamNames macro>
  <#return getCommonUtils().getMacroParamNames(macro)>
</#function>

<#-- 获取 Macro 参数值 -->
<#function getMacroParamValue macro paramName>
  <#return getCommonUtils().getMacroParamValue(macro, paramName)>
</#function>

<#-- 混淆 JavaScript 代码 -->
<#function obfuscateJavaScript code>
  <#return getJavaScriptUtils().obfuscateJavaScript(code)>
</#function>

<#-- 提取使用 throwException 主动抛出的错误信息 -->
<#function getCustomExceptionInfo error>
  <#if error?contains(r'${_.throwException("')>
    <#return error?keep_after(r'${_.throwException("')?keep_before(r'\"|\[in template', "r")>
  <#else>
    <#return "">
  </#if>
</#function>

<#-------------
  Module 相关
--------------->

<#-- 获取模块配置参数 -->
<#function loadModuleConfig moduleId name="moduleConfig">
  <#local result = (.get_optional_template("${appParams.moduleDefault.rootPath}/${moduleId}/${appParams.moduleDefault.configFile}").import())[name]!>
  <#if name == "moduleConfig">
    <#return appParams.moduleDefault + result>
  <#else>
    <#return result>
  </#if>
</#function>

<#-- 加载模块 Lib -->
<#function loadModuleLib moduleId params={}>
  <#local moduleConfig = loadModuleConfig(moduleId)>
  <#local moduleLib = .get_optional_template("${moduleConfig.rootPath}/${moduleId}/${moduleConfig.libFile}").import()>
  <#assign moduleId = moduleId in moduleLib>
  <#assign currentUserId = currentUserId(moduleId) in moduleLib>
  <#assign currentUserRole = currentUserRole(moduleId) in moduleLib>
  <#assign params = moduleConfig + params in moduleLib>
  <#return moduleLib>
</#function>

<#-- 加载模块 Action -->
<#function loadModuleAction moduleId params={}>
  <#local moduleConfig = loadModuleConfig(moduleId)>
  <#local moduleAction = .get_optional_template("${moduleConfig.rootPath}/${moduleId}/${moduleConfig.actionFile}").import()>
  <#assign moduleId = moduleId in moduleAction>
  <#assign currentUserId = currentUserId(moduleId) in moduleAction>
  <#assign currentUserRole = currentUserRole(moduleId) in moduleAction>
  <#assign params = moduleConfig + params in moduleAction>
  <#return moduleAction>
</#function>

<#--------------------
  转换 Java 容器对象
---------------------->

<#-- 从 List<Map> 中筛选对象 -->
<#function filterList list conds>
  <#local resultList = createList()>
  <#list list as map>
    <#list conds as ckey, cvalue>
      <#if ckey?index == 0><#local matched = true></#if>
      <#if matched>
        <#if (map[ckey]??) && (map[ckey] == cvalue)>
          <#local matched = true>
        <#else>
          <#local matched = false>
        </#if>
      <#else>
        <#break>
      </#if>
    </#list>
    <#if matched>
      ${addToList(resultList, map)}
    </#if>
  </#list>
  <#return resultList>
</#function>

<#-- 将 List 转换为 Map -->
<#function listToMap srcList keyFromMap valueFromMap="">
  <#if valueFromMap?has_content>
    <#return getCommonUtils().listToMap(srcList, keyFromMap, valueFromMap)>
  <#else>
    <#return getCommonUtils().listToMap(srcList, keyFromMap)>
  </#if>
</#function>

<#-- 将 List 转换为 Array -->
<#function listToArray srcList itemFromMap>
  <#return getCommonUtils().listToArray(srcList, itemFromMap)>
</#function>

<#--------------------
  操作 Java 容器对象
---------------------->

<#-- 创建 List -->
<#function createList srcList=[]>
  <#return getCommonUtils().createList(srcList)>
</#function>

<#-- 添加对象到 List -->
<#function addToList list obj>
  ${getCommonUtils().addToList(list, obj)}
  <#return "">
</#function>

<#-- 从 List 中删除指定位置的对象 -->
<#function removeFromList list index>
  ${getCommonUtils().removeFromList(list, index)}
  <#return "">
</#function>

<#-- 创建 Set -->
<#function createSet srcSet=[]>
  <#return getCommonUtils().createSet(srcSet)>
</#function>

<#-- 添加对象到 Set -->
<#function addToSet set obj>
  ${getCommonUtils().addToSet(set, obj)}
  <#return "">
</#function>

<#-- 从 Set 中删除对象 -->
<#function removeFromSet set obj>
  ${getCommonUtils().removeFromSet(set, obj)}
  <#return "">
</#function>

<#-- 创建 Map -->
<#function createMap srcMap={}>
  <#return getCommonUtils().createMap(srcMap)>
</#function>

<#-- 添加对象到 Map -->
<#function putToMap map key obj>
  ${getCommonUtils().putToMap(map, key, obj)}
  <#return "">
</#function>

<#-- 从 Map 中删除指定 Key 的对象 -->
<#function removeFromMap map key>
  ${getCommonUtils().removeFromMap(map, key)}
  <#return "">
</#function>
