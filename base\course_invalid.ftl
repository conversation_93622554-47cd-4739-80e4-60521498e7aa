<@p.page title="存疑课程">

  <@p.menu module="course">
  </@p.menu>

  <@p.card title="相同课程代码不同课程名称清单">
    <table class="table table-bordered table-hover">
      <thead>
        <tr>
          <th>课程代码</th>
          <th>不同课程名称数量</th>
          <th>课程名称及使用专业数量</th>
        </tr>
      </thead>
      <tbody>
        <#list sqlt.sqlQueryForList("obe.getSameIdDiffNameCourses") as item>
          <tr>
            <td>${item.courseid}</td>
            <td>${item.namenum}</td>
            <td class="text-monospace">${item.namestats}</td>
          </tr>
        </#list>
      </tbody>
    </table>
  </@p.card>

  <@p.card title="相同课程名称不同课程代码清单">
    <table class="table table-bordered table-hover">
      <thead>
        <tr>
          <th>课程名称</th>
          <th>不同课程代码数量</th>
          <th>课程代码及使用专业数量</th>
        </tr>
      </thead>
      <tbody>
        <#list sqlt.sqlQueryForList("obe.getSameNameDiffIdCourses") as item>
          <tr>
            <td>${item.coursename}</td>
            <td>${item.idnum}</td>
            <td class="text-monospace">${item.idstats}</td>
          </tr>
        </#list>
      </tbody>
    </table>
  </@p.card>

</@p.page>
