<#-- 基础内容（共享，只需执行一次） -->
<#macro commons>
  <style>
    .dt-buttons {
      display: none;
    }
    .dataTables_wrapper .dataTables_info {
      margin-bottom: 0;
      display: none;
    }
    .DTFC_LeftBodyLiner {
      overflow-x: hidden;
    }
  </style>
  <@p.ready>
    var c2gSuppLevelThemes = ${_.toJson(app.suppLevelThemes)?no_esc};
    function toggleC2GSuppLevel(el, goutlevel, supplevel) {
      var theme = c2gSuppLevelThemes[supplevel];
      var objs = $("td.course2gout" + goutlevel + "_" + supplevel);
      if ($(el).prop("checked")) {
        objs.addClass(theme.text).addClass(theme.bg);
        objs.each(function () {
          $(this).html($(this).data("suppval"));
        });
        $("[data-statsclass]").each(function () {
          if ($(this).data("statsclass").slice(-1) == supplevel) {
            $(this).show();
          }
        });
      } else {
        objs.html("").removeClass(theme.text).removeClass(theme.bg);
        $("[data-statsclass]").each(function () {
          if ($(this).data("statsclass").slice(-1) == supplevel) {
            $(this).hide();
          }
        });
      }
      calcStats();
    }
    function toggleC2GCourseAttr(el, goutlevel, attr) {
      var objs = $("tr[data-courseattr='" + attr + "']");
      if ($(el).prop("checked")) {
        objs.show();
        window["table" + goutlevel].draw();
        $("[data-switchclass]").each(function() {
          var key = $(this).data("switchclass");
          var count = $("." + key + ":visible").length;
          $(this).html(count);
        });
      } else {
        objs.hide();
        window["table" + goutlevel].draw();
        $("[data-switchclass]").each(function() {
          var key = $(this).data("switchclass");
          var count = $("." + key + ":visible").length;
          $(this).html(count);
        });
      }
      calcStats();
    }
  </@p.ready>
</#macro>

<#-- 课程支撑毕业要求或指标点 -->
<#macro course2gout goutlevel>
  <#local goutTitle = goutlevel?switch(1, "毕业要求", 2, "指标点", "未知")>
  <#local sqlParams = {"majid": majid, "goutlevel": goutlevel, "r_level": goutlevel}>
  <#local courseList = sqlt.sqlQueryForList("obe.getRawMapCourses", sqlParams)>
  <#local reqCourses = courseList?filter(x -> (x.attr!) == "必修")>
  <#local optCourses = courseList?filter(x -> (x.attr!) == "选修")>
  <#local unknownCourses = courseList?filter(x -> (!x.attr?has_content))>
  <#local courseAttrs = []>
  <#if reqCourses?size gt 0>
    <#local courseAttrs += ["必修 ${reqCourses?size} 门"]>
  </#if>
  <#if optCourses?size gt 0>
    <#local courseAttrs += ["选修 ${optCourses?size} 门"]>
  </#if>
  <#if unknownCourses?size gt 0>
    <#local courseAttrs += ["其他 ${unknownCourses?size} 门"]>
  </#if>
  <#local course2goutList = sqlt.sqlQueryForList("obe.getCourse2Gout", sqlParams)>
  <#local course2goutMap = _.listToMap(course2goutList, "mapkey", "supplevel")>
  <#local course2goutMapWeight = _.listToMap(course2goutList, "mapkey", "suppweight")>
  <#local invalidCourses = _.listToArray(sqlt.sqlQueryForList("obe.getCourse2GoutInvalidCourses", sqlParams + {"attr": "必修"}), "coursename")>
  <#local invalidGouts = _.listToArray(sqlt.sqlQueryForList("obe.getCourse2GoutInvalidGouts", sqlParams + {"attr": "必修"}), "goutid")>
  <#local title>
    <b>课程支撑${goutTitle}</b><span class="tag tag-rounded ml-4">共&nbsp;<span data-statsclass="course2gout${goutlevel}">${course2goutList?size}</span>&nbsp;项</span>
    <#if goutlevel == 2>
      <a href="course2gout_extract.ftl?majid=${majid}" data-toggle="tooltip" title="根据课程支撑指标点情况，自动计算课程支撑毕业要求情况，支撑强度取下级指标点的最大值，在新页面打开。" class="ml-2 btn btn-outline-primary btn-sm" target="_blank">计算支撑毕业要求情况</a>
    </#if>
  </#local>
  <#local actions>
    <#if unknownCourses?size gt 0>
      <span class="tag tag-rounded tag-red mr-2">有 ${unknownCourses?size} 门课程未提供基本信息</span>
    </#if>
    <#if invalidGouts?size gt 0>
      <span class="tag tag-rounded tag-red mr-2">有 ${invalidGouts?size} 项${goutTitle}缺失必修课 H 强支撑</span>
    </#if>
    <#if invalidCourses?size gt 0>
      <span class="tag tag-rounded tag-red mr-2">有 ${invalidCourses?size} 门必修课程未提供 H 强支撑</span>
    </#if>
    <#if !(isMaxView!false)>
      <a href="course2gout_view${goutlevel}.ftl?majid=${majid}" target="_blank" class="mr-2" data-toggle="tooltip" title="在新页面最大化显示"><@p.icon name="fe-maximize" /></a>
    </#if>
  </#local>
  <@p.card title=title actions=actions>
    <#if course2goutList?size == 0>
      <div class="alert alert-danger text-center font-weight-bold">暂无数据</div>
    <#else>
      <div class="mb-2 d-flex justify-content-between">
        <div>
          图例及展示开关：
          <#list app.suppLevelThemes?values as item>
            <label class="custom-control custom-checkbox custom-control-inline">
              <input type="checkbox" class="custom-control-input" name="cbsupplevel" onchange="toggleC2GSuppLevel(this, ${goutlevel}, '${item.id}')" checked>
              <span class="custom-control-label font-weight-bold ${item.text} ${item.bg} px-2">${item.id} ${item.name} <span data-switchclass="course2gout${goutlevel}_${item.id}">${course2goutList?filter(x -> (x.supplevel!) == item.id)?size}</span> 项</span>
            </label>
          </#list>
        </div>
        <div>
          映射课程 ${courseList?size} 门：
          <#list courseAttrs as attr>
            <label class="custom-control custom-checkbox custom-control-inline">
              <input type="checkbox" class="custom-control-input" name="cbattr" onchange="toggleC2GCourseAttr(this, ${goutlevel}, '${attr[0..1]}')" checked>
              <span class="custom-control-label font-weight-bold">${attr}</span>
            </label>
          </#list>
        </div>
      </div>
      <#-- 映射表格 -->
      <#local mappingRules = "四个数值分别是：H+M+L 总数、H 强支撑数、M 中支撑数、L 低支撑数。">
      <#if goutlevel == 2>
        <#local mappingRules += "一般来说一门课程支撑 2~5 个指标点（毕业设计类综合课程可适当增加），一个指标点由 2~6 门课程支撑。">
      </#if>
      <table class="table table-bordered table-vcenter table-sm w-100" id="course2gout${goutlevel}table">
        <thead>
          <#if goutlevel == 1>
            <tr class="text-center text-nowrap bg-light font-weight-bold">
              <td class="bg-light" rowspan="2">课程 \ ${goutTitle}</td>
              <td rowspan="2"><span data-toggle="tooltip" title="${mappingRules}">支撑数量统计</span></td>
              <#list .vars["gradouts${goutlevel}"] as gout>
                <#local bgClass = "">
                <#local tdTitle = app.getGoutFullName(gout)>
                <#if invalidGouts?seq_contains(gout.goutid)>
                  <#local bgClass = "bg-red-lighter text-danger">
                  <#local tdTitle = "${goutTitle} ${gout.goutid} 缺失必修课 H 强支撑">
                </#if>
                <td class="${bgClass}" title="${tdTitle}" data-toggle="tooltip">${gout.goutid}</td>
              </#list>
            </tr>
          <#else>
            <#-- 映射指标点时加入毕业要求 -->
            <tr class="text-center text-nowrap bg-light font-weight-bold">
              <td class="bg-light" rowspan="3">课程 \ ${goutTitle}</td>
              <td rowspan="3"><span data-toggle="tooltip" title="${mappingRules}">支撑数量统计</span></td>
              <#list .vars["gradouts1"] as gout>
                <td colspan="${gout.gout2num}" title="${app.getGoutFullName(gout)}" data-toggle="tooltip">${gout.goutid}</td>
              </#list>
            </tr>
            <tr class="text-center bg-light">
              <#list .vars["gradouts${goutlevel}"] as gout>
                <#local bgClass = "">
                <#local tdTitle = app.getGoutFullName(gout)>
                <#if invalidGouts?seq_contains(gout.goutid)>
                  <#local bgClass = "bg-red-lighter text-danger">
                  <#local tdTitle = '<span class="font-weight-bold text-danger">缺失必修课 H 强支撑</span><br>' + tdTitle>
                </#if>
                <td class="${bgClass}" data-toggle="tooltip" title="${tdTitle}" data-html="true">${gout.goutid}</td>
              </#list>
            </tr>
          </#if>
          <tr class="text-center">
            <#list .vars["gradouts${goutlevel}"] as gout>
              <td class="text-nowrap" style="line-height: 1">
                <div data-min="2" data-max="6" data-statsclass="course2gout${goutlevel}_${gout.goutid?replace('.','-')}">0</div>
                <#list app.suppLevelThemes?values as item>
                  <div class="${item.text}" data-statsclass="course2gout${goutlevel}_${gout.goutid?replace('.','-')}_${item.id}">0</div>
                </#list>
              </td>
            </#list>
          </tr>
        </thead>
        <tbody>
          <#list courseList as course>
            <tr class="text-center text-nowrap" data-courseattr="${course.attr!'其他'}">
              <#local bgClass = "">
              <#local tdTitle = "">
              <#if invalidCourses?seq_contains(course.coursename)>
                <#local bgClass = "bg-red-lighter text-danger">
                <#local tdTitle = "本课程未提供 H 强支撑">
              <#elseif !course.attr?has_content>
                <#local bgClass = "bg-red-lighter text-danger">
                <#local tdTitle = "本课程未提供基本信息">
              </#if>
              <td class="${bgClass}" title="${tdTitle}"><b>${course.coursename}</b><#if (course.attr!) != "必修"><span class="text-black-50">（${course.attr!"信息缺失"}）</span></#if></td>
              <td>
                <span data-min="2" data-max="5" data-statsclass="course2gout${goutlevel}idx_${course?index}">0</span>
                <#list app.suppLevelThemes?values as item>
                  <span class="${item.text}" data-statsclass="course2gout${goutlevel}idx_${course?index}_${item.id}">0</span>
                </#list>
              </td>
              <#list .vars["gradouts${goutlevel}"] as gout>
                <#local supplevel = course2goutMap[gout.goutid + '-' + course.coursename]!>
                <#local suppweight = course2goutMapWeight[gout.goutid + '-' + course.coursename]!>
                <#local suppval = suppweight?has_content?then(suppweight, supplevel)>
                <#local statsClass = "">
                <#if supplevel?has_content>
                  <#local statsClass = "course2gout${goutlevel}_${gout.goutid?replace('.','-')} course2gout${goutlevel}_${gout.goutid?replace('.','-')}_${supplevel} course2gout${goutlevel}idx_${course?index} course2gout${goutlevel}idx_${course?index}_${supplevel} course2gout${goutlevel}_${supplevel} course2gout${goutlevel}">
                </#if>
                <td class="h5 ${app.getSuppLevelTheme(supplevel)} ${statsClass!}" data-suppval="${suppval}">
                  ${suppval}
                </td>
              </#list>
            </tr>
          </#list>
        </tbody>
      </table>
      <@p.ready>
        $(function() {
          calcStats();
        });
        function calcStats() {
          $("[data-statsclass]").each(function() {
            var key = $(this).data("statsclass");
            var max = $(this).data("max");
            var min = $(this).data("min");
            var count = $("." + key + ":visible").length;
            var countEmpty = $("." + key + ":visible:empty").length;
            var val = count - countEmpty;
            $(this).html(val);
            if (max && min) {
              if (val < parseInt(min, 10) || val > parseInt(max, 10)) {
                $(this).addClass("text-danger font-weight-bold");
                $(this).parent().addClass("bg-red-lightest");
              } else {
                $(this).removeClass("text-danger font-weight-bold");
                $(this).parent().removeClass("bg-red-lightest");
              }
            }
          });
        }
        var noticeHeight = $("#course2gout${goutlevel}notice").outerHeight();
        var table${goutlevel} = $("#course2gout${goutlevel}table").DataTable({
          <#if goutlevel == 2>
            "fixedColumns": {
              "leftColumns": 2
            },
            "scrollX": true,
          </#if>
          "paging": false,
          "ordering": false,
          "searching": false,
          "buttons": [],
          "scrollCollapse": true,
          "scrollY": "calc(100vh - <#if isMaxView!false>238px<#else>258px</#if> - <#if goutlevel == 2>3rem -<#else>14px -</#if> " + noticeHeight + "px)"
        });
      </@p.ready>
    </#if>
    <#if goutlevel == 1>
      <div id="course2gout1notice">
        <@app.noticeInfo "course2gout1-${majid}" />
      </div>
    </#if>
    <#if goutlevel == 2>
      <div id="course2gout2notice" class="mt-3">
        <div class="alert alert-info mt-2 mb-0">
          <b>提示</b>：一般来说一门课程支撑 2~5 个指标点（毕业设计类综合课程可适当增加），一个指标点由 2~6 门课程支撑。不符合该条件的统计值已高亮显示，供参考。
        </div>
        <@app.noticeInfo "course2gout2-${majid}" />
      </div>
    </#if>
  </@p.card>
</#macro>
