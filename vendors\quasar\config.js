/*!
 * Quasar Framework v1.22.10
 * (c) 2015-present <PERSON><PERSON><PERSON>
 * Released under the MIT License.
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):((e=e||self).Quasar=e.<PERSON>uasar||{},e.Quasar.lang=e.Quasar.lang||{},e.Quasar.lang.zhHans=t())}(this,function(){"use strict";return{isoName:"zh-hans",nativeName:"中文(简体)",label:{clear:"清空",ok:"确定",cancel:"取消",close:"关闭",set:"设置",select:"选择",reset:"重置",remove:"移除",update:"更新",create:"创建",search:"搜索",filter:"过滤",refresh:"刷新",expand:function(e){return e?'展开"'+e+'"':"扩张"},collapse:function(e){return e?'折叠"'+e+'"':"坍塌"}},date:{days:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),daysShort:"周日_周一_周二_周三_周四_周五_周六".split("_"),months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),headerTitle:function(e){return new Intl.DateTimeFormat("zh-CN",{weekday:"short",month:"short",day:"numeric"}).format(e)},firstDayOfWeek:0,format24h:!1,pluralDay:"天"},table:{noData:"没有可用数据",noResults:"找不到匹配的数据",loading:"正在加载...",selectedRecords:function(e){return"已选择"+e+"行"},recordsPerPage:"每页的行数:",allRows:"全部",pagination:function(e,t,n){return e+"-"+t+" / "+n},columns:"列"},editor:{url:"URL",bold:"粗体",italic:"斜体",strikethrough:"删除线",underline:"下划线",unorderedList:"无序列表",orderedList:"有序列表",subscript:"下标",superscript:"上标",hyperlink:"超链接",toggleFullscreen:"全屏切换",quote:"引号",left:"左对齐",center:"居中对齐",right:"右对齐",justify:"两端对齐",print:"打印",outdent:"减少缩进",indent:"增加缩进",removeFormat:"清除样式",formatting:"格式化",fontSize:"字体大小",align:"对齐",hr:"插入水平线",undo:"撤消",redo:"重做",heading1:"标题一",heading2:"标题二",heading3:"标题三",heading4:"标题四",heading5:"标题五",heading6:"标题六",paragraph:"段落",code:"代码",size1:"非常小",size2:"比较小",size3:"正常",size4:"中等偏大",size5:"大",size6:"非常大",size7:"超级大",defaultFont:"默认字体",viewSource:"查看资料"},tree:{noNodes:"没有可用节点",noResults:"找不到匹配的节点"}}});

/*!
 * Quasar Framework v1.22.10
 * (c) 2015-present Razvan Stoenescu
 * Released under the MIT License.
 */
!function(i,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):((i=i||self).Quasar=i.Quasar||{},i.Quasar.iconSet=i.Quasar.iconSet||{},i.Quasar.iconSet.bootstrapIcons=e())}(this,function(){"use strict";return{name:"bootstrap-icons",type:{positive:"bi-check",negative:"bi-exclamation-triangle-fill",info:"bi-exclamation-circle-fill",warning:"bi-exclamation"},arrow:{up:"bi-arrow-up",right:"bi-arrow-right",down:"bi-arrow-down",left:"bi-arrow-left",dropdown:"bi-caret-down-fill"},chevron:{left:"bi-chevron-left",right:"bi-chevron-right"},colorPicker:{spectrum:"bi-eyedropper",tune:"bi-sliders",palette:"bi-palette2"},pullToRefresh:{icon:"bi-arrow-repeat"},carousel:{left:"bi-chevron-left",right:"bi-chevron-right",up:"bi-chevron-up",down:"bi-chevron-down",navigationIcon:"bi-circle-fill"},chip:{remove:"bi-x-circle-fill",selected:"bi-check"},datetime:{arrowLeft:"bi-chevron-left",arrowRight:"bi-chevron-right",now:"bi-clock-fill",today:"bi-calendar-check-fill"},editor:{bold:"bi-type-bold",italic:"bi-type-italic",strikethrough:"bi-type-strikethrough",underline:"bi-type-underline",unorderedList:"bi-list-ul",orderedList:"bi-list-ol",subscript:"M16,7.41L11.41,12L16,16.59L14.59,18L10,13.41L5.41,18L4,16.59L8.59,12L4,7.41L5.41,6L10,10.59L14.59,6L16,7.41M21.85,21.03H16.97V20.03L17.86,19.23C18.62,18.58 19.18,18.04 19.56,17.6C19.93,17.16 20.12,16.75 20.13,16.36C20.14,16.08 20.05,15.85 19.86,15.66C19.68,15.5 19.39,15.38 19,15.38C18.69,15.38 18.42,15.44 18.16,15.56L17.5,15.94L17.05,14.77C17.32,14.56 17.64,14.38 18.03,14.24C18.42,14.1 18.85,14 19.32,14C20.1,14.04 20.7,14.25 21.1,14.66C21.5,15.07 21.72,15.59 21.72,16.23C21.71,16.79 21.53,17.31 21.18,17.78C20.84,18.25 20.42,18.7 19.91,19.14L19.27,19.66V19.68H21.85V21.03Z",superscript:"M16,7.41L11.41,12L16,16.59L14.59,18L10,13.41L5.41,18L4,16.59L8.59,12L4,7.41L5.41,6L10,10.59L14.59,6L16,7.41M21.85,9H16.97V8L17.86,7.18C18.62,6.54 19.18,6 19.56,5.55C19.93,5.11 20.12,4.7 20.13,4.32C20.14,4.04 20.05,3.8 19.86,3.62C19.68,3.43 19.39,3.34 19,3.33C18.69,3.34 18.42,3.4 18.16,3.5L17.5,3.89L17.05,2.72C17.32,2.5 17.64,2.33 18.03,2.19C18.42,2.05 18.85,2 19.32,2C20.1,2 20.7,2.2 21.1,2.61C21.5,3 21.72,3.54 21.72,4.18C21.71,4.74 21.53,5.26 21.18,5.73C20.84,6.21 20.42,6.66 19.91,7.09L19.27,7.61V7.63H21.85V9Z",hyperlink:"bi-link",toggleFullscreen:"bi-arrows-fullscreen",quote:"bi-chat-square-quote-fill",left:"bi-justify-left",center:"bi-justify",right:"bi-justify-right",justify:"bi-text-center",print:"bi-printer-fill",outdent:"bi-text-indent-right",indent:"bi-text-indent-left",removeFormat:"bi-eraser-fill",formatting:"bi-textarea",fontSize:"bi-textarea-t",align:"bi-text-left",hr:"bi-dash-square-fill",undo:"bi-arrow-counterclockwise",redo:"bi-arrow-clockwise",heading:"bi-type-h1",code:"bi-code",size:"bi-bounding-box",font:"bi-fonts",viewSource:"bi-code-slash"},expansionItem:{icon:"bi-chevron-down",denseIcon:"bi-caret-down-fill"},fab:{icon:"bi-plus",activeIcon:"bi-x"},field:{clear:"bi-x-circle-fill",error:"bi-exclamation-circle-fill"},pagination:{first:"bi-chevron-bar-left",prev:"bi-chevron-left",next:"bi-chevron-right",last:"bi-chevron-bar-right"},rating:{icon:"bi-star-fill"},stepper:{done:"bi-check",active:"bi-pencil-fill",error:"bi-exclamation-triangle-fill"},tabs:{left:"bi-chevron-left",right:"bi-chevron-right",up:"bi-chevron-up",down:"bi-chevron-down"},table:{arrowUp:"bi-arrow-up",warning:"bi-exclamation-triangle-fill",firstPage:"bi-skip-start-fill",prevPage:"bi-chevron-left",nextPage:"bi-chevron-right",lastPage:"bi-skip-end-fill"},tree:{icon:"bi-caret-right-fill"},uploader:{done:"bi-check",clear:"bi-x",add:"bi-plus-square-fill",upload:"bi-upload",removeQueue:"bi-clipboard-x",removeUploaded:"bi-clipboard-check"}}});

/* 初始化设置 */
Quasar.lang.set(Quasar.lang.zhHans);
Quasar.iconSet.set(Quasar.iconSet.bootstrapIcons);
