<#global majid = ctxMap.majid>
<#global major = app.getMajor(majid)>
<@p.page title="课程支撑指标点-${major.majid}-${major.majname}" extVendors=["datatables"]>

  <style>
    html, body {
      overflow: hidden; /* 完全禁用滚动 */
      width: 100%;
      height: 100%;
    }
    body {
      background: #FFF;
    }
  </style>

  <#global gradouts = sqlt.sqlQueryForList("obe.getGradouts", {"majid": majid})>
  <#global gradouts1 = gradouts?filter(x -> x.goutlevel == 1)>
  <#global gradouts2 = gradouts?filter(x -> x.goutlevel == 2)>
  <#global isMaxView = true>

  <#-- 课程支撑指标点 -->
  <#import "course2gout_common.ftl" as c2g>
  <@c2g.commons />
  <@c2g.course2gout 2 />

</@p.page>
