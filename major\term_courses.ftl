<#global majid = ctxMap.majid>
<#global major = app.getMajor(majid)>
<@p.page title="${major.majid}-${major.majname}：专业课开设情况">

  <#assign pageActions>
    <div class="text-nowrap">
      <a href="term_courses_fishbone.ftl?majid=${majid}" target="_blank" class="btn btn-secondary mr-2">鱼骨图</a>
    </div>
    <select class="form-control" onchange="location.href='?majid='+this.value">
      <#list sqlt.sqlQueryForList("obe.getMajors") as item>
        <option value="${item.majid}" ${(majid == item.majid)?then("selected", "")}>${item.majid} - ${item.majname}</option>
      </#list>
    </select>
  </#assign>

  <@p.menu module="major" subNavis=["major.term_courses"] actions=pageActions>
  </@p.menu>

  <#-- 专业课开设情况 -->
  <#assign courses = sqlt.sqlQueryForList("obe.getMajorCourseList", {"majid": majid})>
  <#assign title><b>专业课开设情况</b><span class="tag tag-rounded ml-4">共 ${courses?size} 门课程</span></#assign>
  <#assign actions>
    <div class="d-flex align-items-center mr-2">
      点击课程名称可高亮显示，图例：<button class="btn btn-outline-primary btn-sm">必修</button><button class="btn btn-outline-warning btn-sm ml-2">选修</button><button class="btn btn-outline-success btn-sm ml-2">实践</button>
    </div>
  </#assign>
  <@p.card title=title actions=actions>
    <table class="table table-bordered table-vcenter table-sm">
      <thead>
        <tr>
          <th class="text-center text-nowrap" style="width: 10%">学期</th>
          <th class="text-center text-nowrap" style="width: 90%">开设课程</th>
        </tr>
      </thead>
      <tbody>
        <#assign currTerm = "">
        <#list courses as item>
          <#if item.term != currTerm>
            <#if currTerm != "">
                </td>
              </tr>
            </#if>
            <tr>
              <td class="text-center text-nowrap h2 font-weight-bold">${item.term}</td>
              <td>
                <#assign currTerm = item.term>
          </#if>
          <#if item.rcflag == "P">
            <#assign btnTheme = "btn-outline-success">
          <#elseif item.attr == "选修">
            <#assign btnTheme = "btn-outline-warning">
          <#elseif item.attr == "必修">
            <#assign btnTheme = "btn-outline-primary">
          <#else>
            <#-- 应该没有这种情况 -->
            <#assign btnTheme = "btn-outline-secondary">
          </#if>
          <button class="btn py-1 m-1 ${btnTheme}" onclick="toggleTheme(this)">
            <div class="h5 mt-1 mb-0">${item.coursename}</div>
            <div class="font-weight-normal">${item.typename!}</div>
          </button>
        </#list>
        <#if currTerm != "">
            </td>
          </tr>
        </#if>
      </tbody>
    </table>
  </@p.card>

  <@p.ready>
    function toggleTheme(src) {
      if ($(src).hasClass("btn-outline-primary")) {
        $(src).removeClass("btn-outline-primary").addClass("btn-primary");
      } else if ($(src).hasClass("btn-outline-warning")) {
        $(src).removeClass("btn-outline-warning").addClass("btn-warning");
      } else if ($(src).hasClass("btn-outline-success")) {
        $(src).removeClass("btn-outline-success").addClass("btn-success");
      } else if ($(src).hasClass("btn-primary")) {
        $(src).removeClass("btn-primary").addClass("btn-outline-primary");
      } else if ($(src).hasClass("btn-warning")) {
        $(src).removeClass("btn-warning").addClass("btn-outline-warning");
      } else if ($(src).hasClass("btn-success")) {
        $(src).removeClass("btn-success").addClass("btn-outline-success");
      }
    }
  </@p.ready>

</@p.page>
