<#-- 页面基础模板 -->
<#macro page title="" extVendors=[] mobileMode=appParams.mobileMode>
<#global usePageMacro = true><#-- 声明 page 宏已被调用 -->
<#global pageVendors = appParams.vendors + extVendors><#-- 声明最终页面使用的外部前端库 -->
<#global pageTitle = title><#-- 声明页面 title -->
<#import "macro_vendor.ftl" as v>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="${appParams.charset}">
  <meta http-equiv="X-UA-Compatible" content="IE=Edge">
  <#if mobileMode>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  </#if>
  <title><#if title?has_content>${title} - </#if>${appParams.title}</title>
  <#if appParams.logo?has_content>
    <link rel="icon" href="${base}${appParams.logo}" type="image/png">
  </#if>
  <#-- 导入 CSS -->
  <@v.importVendors keys=pageVendors type="css" />
  <#-- 导入 app 定制文件 -->
  <@v.import file="${v.vendorRoot}/app/app.css.ftl" type="css" />
</head>
<body>
  <#attempt>
    <#nested>
    <#-- 必要时显示页尾 -->
    <#if .globals.showMenu!false>
          </div><!-- .my-5 -->
        </div><!-- .flex-fill -->
        <footer class="footer">
          <div class="container-fluid">
            <div class="row">
              <div class="col-9" style="padding-top: 0.1rem">
                <#if appParams.sideNav>
                  【 ${appParams.title} 】
                <#else>
                  Copyright &copy; ${appParams.copyright}
                </#if>
              </div>
              <div class="col-3 text-right">Version <span class="tag tag-rounded">${appParams.version}</span></div>
            </div>
          </div>
        </footer>
        <#if appParams.sideNav></div><!-- .page-wrapper --></#if>
      </div><!-- .page -->
    </#if>
    <#-- 必要时显示 Flash Message -->
    <#local fmsg = (request.getSession().getAttribute("_FLASH_MESSAGE_")!"")>
    <#if fmsg?has_content>
      ${request.getSession().removeAttribute("_FLASH_MESSAGE_")}
      <@ready>
        PNotify.success("${fmsg}");
      </@ready>
    </#if>
  <#recover>
    <#-- 系统错误提示 -->
    <div class="page">
      <div class="page-content">
        <div class="${appParams.container} text-center">
          <div class="display-3 text-muted mb-6">
            <#local customExceptionInfo = _.getCustomExceptionInfo(.error)>
            <#if customExceptionInfo?has_content>
              <#-- 支持 app 自定义错误文本 -->
              ${(app.sysErrors[customExceptionInfo])!customExceptionInfo}
            <#else>
              ERROR
            </#if>
          </div>
          <h1 class="mb-3">对不起，系统出现错误，请联系管理员</h1>
          <p class="h4 text-muted font-weight-normal mb-7">Sorry, a system error occurs, contact the administrator for processing.</p>
          <a class="btn btn-primary" href="javascript:history.back()">
            <i class="fe fe-arrow-left mr-2"></i>返回上一页
          </a>
        </div>
      </div>
      <#-- 开发模式下显示错误详情 -->
      <#if appParams.devMode>
        <div class="${appParams.container}">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">错误详情</h3>
              <div class="card-options">
                <div class="tag tag-dark">devMode<span class="tag-addon tag-success">true</span></div>
              </div>
            </div>
            <pre class="mb-0" style="background:#FFF; white-space: pre-wrap; word-wrap: break-word;">${.error}</pre>
          </div>
        </div>
      </#if>
    </div>
  </#attempt>
  <#-- 定义 JavaScript 中应用路径变量 -->
  <script>
    var base = "${base}";
  </script>
  <#-- 导入 JS -->
  <@v.importVendors keys=pageVendors type="js" />
  <#-- 导入 app 定制文件 -->
  <@v.import file="${v.vendorRoot}/app/app.js.ftl" type="js" />
  <#-- 页面加载完毕后执行脚本 -->
  <#list (.globals.readyScripts)![]>
    <script>
      <#items as script>
        //Ready Script ${script?counter}
        ${script}
      </#items>
    </script>
  </#list>
</body>
</html>
</#macro>

<#-- 页面导航菜单及帮助信息 -->
<#macro menu module="" subModule="" subNavis=[] title=.globals.pageTitle subTitle="" actions="" id="appMain">
  <#global showMenu=true><#-- 声明是否显示了页面导航菜单 -->
  <div class="page">
    <#if appParams.sideNav>
      <div class="page-wrapper">
    </#if>
    <div class="flex-fill">
      <div class="header py-4">
        <div class="container-fluid">
          <div class="d-flex">
            <#if appParams.sideNav>
              <#if module?has_content && appMenus[module]??>
                <#local moduleItem = appMenus[module]>
                <div class="d-flex align-items-center" style="font-size: 16px">
                  <#if moduleItem.icon??><div class="mr-3"><@icon name=moduleItem.icon ext="h2 text-primary" /></div></#if>
                  <div class="pb-1"><a href="${_.safeUrl(moduleItem.link!)}">${moduleItem.name}</a></div>
                  <#if moduleItem.subs?? && subModule?has_content>
                    <#local subModuleItem = (moduleItem.subs?filter(x -> (x.key!) == subModule)?first)!>
                    <#if subModuleItem?has_content>
                      <div class="ml-3"><@icon name="fe-arrow-right-circle" ext="h3 text-black-50" /></div>
                      <div class="ml-3 pb-1"><a href="${subModuleItem.link!'javascript:void(0)'}">${subModuleItem.name}</a></div>
                    </#if>
                  </#if>
                  <#-- 追加业务导航 -->
                  <#list subNavis>
                    <#items as item>
                      <#local navi = item>
                      <#if item?is_string>
                        <#local navi = app.sysNavis[item]![item]>
                      </#if>
                      <#if navi?is_sequence>
                        <div class="ml-3"><@icon name="fe-arrow-right-circle" ext="h3 text-black-50" /></div>
                        <div class="ml-3 pb-1">
                          <#if navi?size == 2>
                            <a href="<@(navi[1]?interpret) />">${navi[0]}</a>
                          <#elseif navi?size == 1>
                            <span class="text-black-50">${navi[0]}</span>
                          </#if>
                        </div>
                      </#if>
                    </#items>
                  </#list>
                </div>
              </#if>
            <#else>
              <a class="header-brand" href="${base}${homepage}">
                <#if appParams.logo?has_content><img src="${base}${appParams.logo}" class="header-brand-img" alt="logo"></#if>
                ${appParams.title}
              </a>
            </#if>
            <div class="d-flex ml-auto">
              <div class="dropdown">
                <a href="#" class="nav-link pr-0 leading-none" data-toggle="dropdown">
                  <#if loginUser.avatar?has_content>
                    <span class="avatar avatar-azure" style="background-image: url(${base}/files_upload/avatar/${loginUser.avatar})"></span>
                  <#else>
                    <span class="avatar avatar-azure"><i class="fe fe-user"></i></span>
                  </#if>
                  <span class="ml-2 d-none d-block">
                    <span class="text-default">${loginUser.name}</span>
                    <small class="text-muted d-block mt-1">${loginUser.login_name}</small>
                  </span>
                </a>
                <div class="dropdown-menu dropdown-menu-right dropdown-menu-arrow">
                  <@dropDownMenu appUserMenus />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    <#if appParams.sideNav>
      <aside class="navbar-vertical navbar-${(appParams.sideNavTheme)!'dark'}">
        <div class="container-fluid">
          <div class="d-flex justify-content-center pt-4 pb-5">
            <a class="header-brand" href="${base}${homepage}">
              <#if appParams.logo?has_content><img src="${base}${appParams.logo}" class="header-brand-img" alt="logo"></#if>
              <span class="app-title"><#if appParams.shortTitle?has_content>${appParams.shortTitle}<#else>${appParams.title}</#if></span>
            </a>
          </div>
          <div class="navbar-collapse">
            <#list appMenus>
              <ul class="navbar-nav">
                <#items as menuKey, menu>
                  <#local hasSubs = menu.subs??>
                  <li class="nav-item ${hasSubs?then('dropdown','')} ${(module==menuKey)?then('active','')}">
                    <a
                      href="${_.safeUrl(menu.link!)}"
                      class="nav-link ${hasSubs?then('dropdown-toggle','')}"
                      <#if hasSubs>data-toggle="verticaldropdown"</#if>
                      <#if menu.target??>target="${menu.target}"</#if>
                    >
                      <#if menu.icon??><@icon name=menu.icon ext="nav-link-icon"/></#if>
                      <span class="menuname">${menu.name}</span>
                    </a>
                    <#if hasSubs>
                      <div class="dropdown-menu dropdown-menu-arrow ${(module==menuKey)?then('show','')}"">
                        <@dropDownMenu menu.subs subModule />
                      </div>
                    </#if>
                  </li>
                </#items>
              </ul>
            </#list>
          </div>
          <div class="text-center navbar-copyright">&copy; <span class="full-copyright">${appParams.copyright}</span><span class="short-copyright">Neuedu</span></div>
        </div>
      </aside>
    <#else>
      <#list appMenus>
        <div class="header d-flex p-0">
          <div class="${appParams.container}">
            <div class="row align-items-center">
              <div class="col-12">
                <ul class="nav nav-tabs border-0 flex-row">
                  <#items as menuKey, menu>
                    <#local hasSubs = menu.subs??>
                    <li class="nav-item ${hasSubs?then('dropdown','')}">
                      <a
                        href="${_.safeUrl(menu.link!)}"
                        class="nav-link ${(module==menuKey)?then('active','')} ${hasSubs?then('dropdown-toggle','')}"
                        <#if hasSubs>data-toggle="dropdown"</#if>
                        <#if menu.target??>target="${menu.target}"</#if>
                      >
                        <#if menu.icon??><@icon name=menu.icon /></#if>
                        ${menu.name}
                      </a>
                      <#if hasSubs>
                        <div class="dropdown-menu dropdown-menu-arrow">
                          <@dropDownMenu menu.subs subModule />
                        </div>
                      </#if>
                    </li>
                  </#items>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </#list>
    </#if>

      <#-- 页面主体 -->
      <div class="${appParams.container} my-5" <#if id?has_content>id="${id}"</#if>>
        <#-- 本页功能信息 -->
        <#local intro><#compress><#nested></#compress></#local>
        <#if title?has_content || subTitle?has_content || intro?has_content || actions?has_content>
          <div class="page-header">
            <h1 class="page-title">${title}</h1>
            <div class="page-subtitle">${subTitle}</div>
            <div class="page-options d-flex">
              ${actions?no_esc}
              <#if intro?has_content>
                <button type="button" class="btn btn-icon btn-secondary ml-2" title="查看帮助信息" onclick="$('#pageIntro').toggle()"><i class="fe fe-help-circle"></i></button>
              </#if>
            </div>
          </div>
          <#if intro?has_content>
            <div class="alert alert-secondary alert-dismissible" id="pageIntro">
              <button type="button" class="close" onclick="$('#pageIntro').hide()"></button>
              ${intro}
            </div>
          </#if>
        </#if>
</#macro>

<#-- 下拉菜单 -->
<#macro dropDownMenu items activeKey="">
  <#list items as item>
    <#if "divider" == (item.type!"")>
      <div class="dropdown-divider"></div>
    <#elseif "header" == (item.type!"")>
      <h6 class="dropdown-header">${item.name}</h6>
    <#else>
      <a
        class="dropdown-item <#if activeKey?has_content && item.key?has_content && activeKey == item.key>active</#if>"
        href="${_.safeUrl(item.link!)}"
        <#if item.target??>target="${item.target}"</#if>
        <#list item.attrs!{} as key, value>
          ${key} = "${value?string}"
        </#list>
      >
        <#-- 二级不建议使用图标，视觉效果较乱 -->
        <#--
        <#if item.icon??><@icon name=item.icon ext="dropdown-icon" /></#if>
        -->
        ${item.name}
      </a>
    </#if>
  </#list>
</#macro>

<#-- 批量渲染标签属性 -->
<#macro renderTagAttrs attrs><#if attrs?is_hash_ex><#list attrs as attkey, attvalue> ${attkey}="${attvalue}" </#list></#if></#macro>

<#-- 页尾执行 JavaScript 代码 -->
<#macro ready file="" parse=false obfuscate=(!appParams.devMode)>
  <#outputformat "plainText">
    <#local jsText><#if file?has_content><#include file?absolute_template_name(.caller_template_name) parse=parse><#else><#nested></#if></#local>
    <#if obfuscate>
      <#local jsText = _.obfuscateJavaScript(jsText)>
    </#if>
  </#outputformat>
  <#if (.globals.usePageMacro!false)>
    <#global readyScripts = (.globals.readyScripts![]) + [jsText?no_esc]>
  <#else>
    <script>
      ${jsText?no_esc}
    </script>
  </#if>
</#macro>

<#-- 确保加载指定组件 -->
<#macro ensureVendors vendors global=true>
  <#import "macro_vendor.ftl" as v>
  <#if global>
    <#if (.globals.usePageMacro!false)>
      <#list vendors as vkey>
        <#-- 对于未加载的库，CSS 文件在页面当前位置直接加载，JS 文件交给 page 宏在页尾加载 -->
        <#if !.globals.pageVendors?seq_contains(vkey)>
          <@v.importVendors keys=vendors type="css" merge=false />
          <#global pageVendors += vendors>
        </#if>
      </#list>
    </#if>
  <#else>
    <@v.importVendors keys=vendors type="css" />
    <@v.importVendors keys=vendors type="js" />
  </#if>
</#macro>

<#-- 字体图标 -->
<#macro icon name ext="" attrs...><i class="<#if name?starts_with("fa-")>fa <#elseif name?starts_with("fe-")>fe <#elseif name?starts_with("ti-")>ti <#elseif name?starts_with("t-icon-")>t-icon <#elseif !name?starts_with("el-icon-")>fe fe-</#if>${name} ${ext}" <@renderTagAttrs attrs=attrs />></i></#macro>

<#-- 卡片 -->
<#macro card title="" cardClass="" statusClass="" actions="" footer="" direct=false attrs...>
  <div class="card ${cardClass}" <@p.renderTagAttrs attrs=attrs />>
    <#if title?has_content || actions?has_content>
      <#if statusClass?has_content><div class="card-status ${statusClass}"></div></#if>
      <div class="card-header">
        <div class="card-title">${title}</div>
        <#if actions?has_content>
          <div class="card-options">${actions?no_esc}</div>
        </#if>
      </div>
    </#if>
    <#if direct>
      <#nested>
    <#else>
      <div class="card-body">
        <#nested>
      </div>
    </#if>
    <#if footer?has_content>
      <div class="card-footer">${footer?no_esc}</div>
    </#if>
  </div>
</#macro>

<#-- 输出 Action 结果 -->
<#macro renderActionResult actionContainer=.main actionNames=[]>
  <#noautoesc>
    <#local actionName = ctxMap.action!"">
    <#-- 参数验证 -->
    <#if !actionName?has_content>
      {"code": -2, "message": "未指定 Action 名称"}<#t><#return>
    <#elseif !actionName?is_string>
      {"code": -3, "message": "只能执行一个 Action"}<#t><#return>
    <#elseif !actionContainer[actionName]??>
      {"code": -4, "message": "未找到名为 [${actionName?json_string}] 的 Action"}<#t><#return>
    <#elseif actionName?starts_with("_")>
      {"code": -5, "message": "下划线开头的 Action 仅供内部调用"}<#t><#return>
    </#if>
    <#attempt>
      <#-- 访问权限检查 -->
      <#local accessDenied = false>
      <#if actionNames?has_content && !actionNames?seq_contains(actionName)>
        <#local accessDenied = true>
      </#if>
      <#local macroParamNames = _.getMacroParamNames(actionContainer[actionName])>
      <#if (macroParamNames?size gt 0) && (!macroParamNames?seq_contains(_.currentUserRole(actionContainer["moduleId"]!"")))>
        <#local accessDenied = true>
      </#if>
      <#if accessDenied>
        {"code": -9, "message": "当前用户无权访问名为 [${actionName?json_string}] 的 Action"}<#t>
      <#else>
        <#-- 调用 Action 宏 -->
        <#local readyToRender = true>
        <#if actionContainer["_readyToRender"]??>
          <#local readyToRender = actionContainer["_readyToRender"]()>
        </#if>
        <#if readyToRender>
          <@actionContainer[actionName] />
        </#if>
        <#-- 获取结果 -->
        <#local actionResult = {"code": 0, "message": ""} + actionContainer["actionResult"]!{}>
        <#-- 保存 Flash Message（条件：flash:true, code:0, message:有内容） -->
        <#if (actionResult.flash!false) && (actionResult.code == 0) && (actionResult.message?has_content)>
          ${request.getSession().setAttribute("_FLASH_MESSAGE_", actionResult.message)}
        </#if>
        <#-- 输出结果 -->
        ${_.toJson(actionResult)}<#t>
      </#if>
    <#recover>
      {"code": -1, "message": "-1: 未知错误（Unexpected Exception）"<#if appParams.devMode>, "error": "${.error?json_string}"</#if>}<#t>
    </#attempt>
  </#noautoesc>
</#macro>

<#-- 代码语法高亮 -->
<#macro code2html file="" language="" lineNumbers=false start=1 highlight="" attrs...>
  <@ensureVendors vendors=["prism"] /><#-- 确保加载所需组件 -->
  <#-- 获取代码 -->
  <#outputformat "plainText">
    <#local code><#if file?has_content><#include file?absolute_template_name(.caller_template_name) parse=false><#else><#nested></#if></#local>
  </#outputformat>
  <#-- 输出 HTML 代码 -->
  <pre
    <#if highlight?has_content>
      <#local lineNumbers=true>
      data-line="${highlight}"
    </#if>
    <#if lineNumbers>class="line-numbers"</#if>
    <#if start != 1>data-start="${start}"</#if>
    <@renderTagAttrs attrs=attrs />
  ><code <#if language?has_content>class="language-${language}"</#if> v-pre>${code}</code></pre>
</#macro>

<#-- 将 Markdown 渲染为 HTML -->
<#macro md2html file="" showToc=true>
  <@ensureVendors vendors=["flexmark", "prism"] /><#-- 确保加载所需组件 -->
  <#outputformat "plainText">
    <#local mdtext><#if file?has_content><#if !file?ends_with(".md")>!!! error "目标文件名必须以 .md 结尾"
    当前文件名：<code>${file?html}</code><#else><#include file?absolute_template_name(.caller_template_name) parse=false></#if><#else><#nested></#if><#if showToc>${"\r\n"}[TOC]: # "目录"</#if></#local>
    <#local htmlContent>${_.getMarkdownUtils().md2html(mdtext)}</#local>
  </#outputformat>
  <#if showToc>
    <div class="markdown"><div class="row no-gutters"><div class="col-9 text-wrap p-6 border-right markdown-content">${htmlContent?keep_before('<div class="markdown-toc">')?no_esc}</div><div class="col-3"><div class="markdown-toc py-3">${htmlContent?keep_after('<div class="markdown-toc">')?no_esc}</div></div></div>
  <#else>
    <div class="markdown"><div class="text-wrap p-6 markdown-content">${htmlContent?no_esc}</div></div>
  </#if>
</#macro>

<#-- 激活富文本编辑器 -->
<#macro initEditor textarea heightMin=200 toolbars="full" picUploader="/vendors/fr-editor/pic_uploader.jsp">
  <@ensureVendors vendors=["fr-editor"] />
  <#-- 预置 toolbar 图标 -->
  <#if toolbars?is_string>
    <#if toolbars == "full">
      <#local toolbars = ['fullscreen', 'bold', 'italic', 'underline', 'strikeThrough', '|', 'paragraphFormat', 'fontSize', 'color', 'align', 'formatOL', 'formatUL', 'outdent', 'indent', '|', 'insertLink', 'insertImage', 'insertTable', 'insertHR', 'clearFormatting', '|', 'undo', 'redo']>
    <#elseif toolbars == "simple">
      <#local toolbars = ['bold', 'italic', 'underline', 'strikeThrough', '|', 'paragraphFormat', 'align', 'formatOL', 'formatUL', 'outdent', 'indent', '|', 'undo', 'redo']>
    <#else>
      <#local toolbars = []>
    </#if>
  </#if>
  <#-- 不允许粘贴的标签 -->
  <#local pasteDeniedTags = _.createList()>
  <#-- 初始化脚本 -->
  <@ready>
    $("${textarea}").froalaEditor({
      language: 'zh_cn',
      heightMin: ${heightMin},
      charCounterCount: false,
      listAdvancedTypes: false,
      quickInsertTags: [],
      fontSize: ['12', '14', '18', '24', '30', '36', '48', '60', '72', '96'],
      htmlAllowedTags: ['a', 'b', 'blockquote', 'br', 'caption', 'code', 'col', 'colgroup', 'dd', 'del', 'div', 'dl', 'dt', 'em', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'hr', 'i', 'img', 'ins', 'kbd', 'li', 'ol', 'p', 'pre', 's', 'small', 'span', 'strong', 'sub', 'sup', 'table', 'tbody', 'td', 'tfoot', 'th', 'thead', 'tr', 'ul'],

      toolbarButtons: [<#list toolbars as tb>'${tb}'<#sep>, </#list>],

      <#-- 超链接 -->
      <#if toolbars?seq_contains("insertLink")>
        linkAutoPrefix: '',
        linkInsertButtons: [],
        linkAlwaysBlank: true,
        linkEditButtons: ['linkOpen', 'linkEdit', 'linkRemove'],
      <#else>
        ${_.addToList(pasteDeniedTags, "a")}
      </#if>

      <#-- 图片 -->
      <#if toolbars?seq_contains("insertImage")>
        <@include_page path=picUploader />
        imageDefaultWidth: 0,
        imageAllowedTypes: ['jpeg', 'jpg', 'gif', 'png'],
        imageInsertButtons: ['imageBack', 'imageUpload'],
        imageEditButtons: ['imageReplace', 'imageAlign', 'imageCaption', 'imageRemove', 'imageSize', '|', 'imageLink', 'linkOpen', 'linkEdit', 'linkRemove'],
        imageUploadURL: '${base}${picUploader}',
        imageUploadParams: {
          timestamp: '${pic_uploader_timestamp!}',
          sign: '${pic_uploader_sign!}'
        },
      <#else>
        imagePaste: false,
      </#if>

      <#-- 表格 -->
      <#if toolbars?seq_contains("insertTable")>
        tableEditButtons: ['tableHeader', 'tableRemove', 'tableRows', 'tableColumns', '-', 'tableCells', 'tableCellBackground', 'tableCellVerticalAlign', 'tableCellHorizontalAlign'],
      <#else>
        ${_.addToList(pasteDeniedTags, "table")}
      </#if>

      <#-- 水平线 -->
      <#if !toolbars?seq_contains("insertHR")>
        ${_.addToList(pasteDeniedTags, "hr")}
      </#if>

      <#-- 禁止粘贴的标签 -->
      pasteDeniedTags: [<#list pasteDeniedTags as tag>'${tag}'<#sep>, </#list>]

    })
    <#-- 图片上传错误处理 -->
    <#if toolbars?seq_contains("insertImage")>
      .on('froalaEditor.image.error', function (e, editor, error, response) {
        var msg = "";
        if (error.code == 2) {
          msg = JSON.parse(response).error;
        } else if (error.code == 5) {
          msg = "文件过大";
        } else if (error.code == 6) {
          msg = "文件类型不支持";
        }
        if (msg !== "") {
          PNotify.error(msg);
        }
      })
    </#if>
    ;
  </@ready>
</#macro>

<#-- 显示富文本内容 -->
<#macro viewEditorContent>
  <@ensureVendors vendors=["fr-view"] />
  <div class="fr-view"><#nested></div>
</#macro>

<#-- 生成分页信息 -->
<#macro pagination pagingList smallBtn=false arrayKeys=[] attrs...>
  <#local btnStyle = "btn btn-secondary" + smallBtn?then(" btn-sm","")>
  <#local ellipsis = "..."><#-- 省略号 -->
  <#local maxButtons = 7><#-- 必须是奇数 -->
  <#local showAllMaxCount = 1000><#-- 支持全部显示的最大记录数 -->
  <#local halfButtons = (maxButtons / 2)?floor>

  <#local rowCount = pagingList.rowCount>
  <#local pageCount = pagingList.pageCount>
  <#local pageSize = pagingList.pageSize>
  <#local pageNum = pagingList.pageNum>
  <#local startIndex = pagingList.startIndex>
  <#local endIndex = [pagingList.endIndex, rowCount]?min>

  <#local pageBaseUrl = base + "/" + .main_template_name + "?">
  <#local pageUrl = pageBaseUrl + _.queryString(ctxMap + {"pageSize":pageSize}, ["pageNum"], arrayKeys)>

  <div <@renderTagAttrs attrs=attrs />>
    <div class="float-left <#if !smallBtn>mt-2</#if>">第 ${startIndex} 至 ${endIndex} 条记录， 共 ${rowCount} 条</div>
    <div class="float-right">
      <#-- 全部/分页显示 -->
      <#if rowCount lte showAllMaxCount>
        <#if pageCount gt 1>
          <#local showAllUrl = pageBaseUrl + _.queryString(ctxMap + {"pageSize":showAllMaxCount, "pageNum":1}, [], arrayKeys)>
          <a class="${btnStyle}" href="${showAllUrl}">全部显示</a>
        <#elseif pageSize == showAllMaxCount>
          <#local showPagingUrl = pageBaseUrl + _.queryString(ctxMap + {"pageNum":1}, ["pageSize"], arrayKeys)>
          <a class="${btnStyle}" href="${showPagingUrl}">分页显示</a>
        </#if>
      </#if>
      <#-- 上页 -->
      <#if pageNum == 1>
        <a class="${btnStyle} disabled">上页</a>
      <#else>
        <a class="${btnStyle}" href="${pageUrl}&pageNum=${pageNum-1}">上页</a>
      </#if>

      <#local pageIndexes = _.createList()>

      <#-- 页码 -->
      <#if pageCount lte maxButtons>
        <#list 1..pageCount as idx>${_.addToList(pageIndexes, idx)}</#list>
      <#elseif pageNum lte halfButtons + 1>
        <#list 1..(maxButtons - 2) as idx>${_.addToList(pageIndexes, idx)}</#list>
        ${_.addToList(pageIndexes, ellipsis)}
        ${_.addToList(pageIndexes, pageCount)}
      <#elseif pageNum gte (pageCount - halfButtons)>
        ${_.addToList(pageIndexes, 1)}
        ${_.addToList(pageIndexes, ellipsis)}
        <#list (pageCount - maxButtons + 3)..pageCount as idx>${_.addToList(pageIndexes, idx)}</#list>
      <#else>
        ${_.addToList(pageIndexes, 1)}
        ${_.addToList(pageIndexes, ellipsis)}
        <#list (pageNum - halfButtons + 2)..(pageNum + halfButtons - 2) as idx>${_.addToList(pageIndexes, idx)}</#list>
        ${_.addToList(pageIndexes, ellipsis)}
        ${_.addToList(pageIndexes, pageCount)}
      </#if>

      <#list pageIndexes as pidx>
        <#if pidx?is_number>
          <a class="btn btn-${(pidx==pageNum)?then("primary","secondary")} <#if smallBtn>btn-sm</#if>" href="${pageUrl}&pageNum=${pidx}">${pidx}</a>
        <#else>
          ${pidx}
        </#if>
      </#list>

      <#-- 下页 -->
      <#if pageNum == pageCount>
        <a class="${btnStyle} disabled">下页</a>
      <#else>
        <a class="${btnStyle}" href="${pageUrl}&pageNum=${pageNum+1}">下页</a>
      </#if>
    </div>
  </div>
</#macro>

<#-- 生成上传文件按钮 -->
<#macro renderUploader id smallBtn=false btnText="上传文件" allowExts="pdf,jpg,zip,rar" maxSize=10 extFormData={} boxStyle="" afterUpload="addUploadFileToList" fileUploader="/vendors/webuploader/file_uploader.jsp">
  <@ensureVendors vendors=["webuploader", "sortable"] /><#-- 确保加载所需组件 -->
  <@include_page path=fileUploader />
  <#if smallBtn>
    <style>
      #${id} > .webuploader-pick {
        font-size: .75rem;
        min-width: 1.625rem;
        padding: .25rem .5rem;
        line-height: 1.33333333;
      }
    </style>
  </#if>
  <#if boxStyle?has_content>
    <style>
      #${id} > .webuploader-pick {
        ${boxStyle}
      }
    </style>
  </#if>
  <div id="${id}">${btnText} <span class="fileBox"></span></div>
  <@ready>
    initUploader("${base}", "#${id}", "${base}${fileUploader}", "${allowExts}", ${maxSize}, "${file_uploader_timestamp!}", "${file_uploader_sign!}", ${_.toJson(extFormData)?no_esc}, ${afterUpload});
  </@ready>
</#macro>

<#-- 服务器代理获取远程内容 -->
<#macro remoteContent params>
  <#local target_url = params.target_url>
  ${_.removeFromMap(params, "target_url")}<#t>
  <#if params.target_selector??>
    <#local target_selector = params.target_selector>
    <#local target_html_type = (params.target_html_type!"text")>
    ${_.removeFromMap(params, "target_selector")}<#t>
    ${_.removeFromMap(params, "target_html_type")}<#t>
    ${_.getJsoupUtils().getRemoteHtml(target_url, params, target_selector, target_html_type)?no_esc}<#t>
  <#else>
    ${_.getJsoupUtils().getRemoteContent(target_url, params)?no_esc}<#t>
  </#if>
</#macro>

<#-- 视频播放器 -->
<#macro videoPlayer file width=640 style="margin:auto" attrs...>
  <@ensureVendors vendors=["video.js"] />
  <video class="video-js vjs-big-play-centered" controls preload="auto" width="${width}" data-setup="{}" style="${style}" <@renderTagAttrs attrs=attrs />>
    <source src="${file}" type="video/mp4">
    <p class="vjs-no-js">您的浏览器不支持视频播放</a></p>
  </video>
</#macro>

<#-- 绘制图表 -->
<#macro chart id type width="auto" height="400px">
  <@ensureVendors vendors=["echarts"] />
  <#outputformat "plainText"><#local nestedContent><#nested></#local></#outputformat>
  <div id="${id}">
    <ve-${type} :data="chartData" :settings="chartSettings" width="${width}" height="${height}"></ve-${type}>
  </div>
  <@ready>
    new Vue({
      el: '#${id}',
      data: function () {
        return {
          <#if nestedContent?index_of("chartSettings") == -1>
            chartSettings: {},
          </#if>
          ${nestedContent?no_esc}
        }
      }
    });
  </@ready>
</#macro>

<#-- 注册 Vue 组件 -->
<#macro vueComponent file name="" params={} moduleId="">
  <#-- 如果设置了 moduleId，则需要重新定位文件位置 -->
  <#if moduleId?has_content>
    <#local moduleConfig = _.loadModuleConfig(moduleId)>
    <#local params = moduleConfig + params>
    <#local file = "${moduleConfig.rootPath}/${moduleId}/${file}">
  </#if>
  <#-- 自动命名组件 -->
  <#if name == "">
    <#local name = file?keep_before(".")>
    <#if name?contains("/")>
      <#local name = name?keep_after_last("/")>
    </#if>
  </#if>
  <#-- 加载组件 -->
  <#import file?absolute_template_name(.caller_template_name) as component>
  <#-- 注入参数 -->
  <#if moduleId?has_content>
    <#assign moduleId = moduleId in component>
    <#assign currentUserId = _.currentUserId(moduleId) in component>
    <#assign currentUserRole = _.currentUserRole(moduleId) in component>
  </#if>
  <#assign params = params in component>
  <#-- 组件初始化 -->
  <#if component.init??>
    <@component.init />
  </#if>
  <#-- 组件模板 -->
  <#if component.template??>
    <script type="text/x-template" id="${name}">
      <@component.template />
    </script>
  </#if>
  <#-- 组件样式 -->
  <#if component.style??>
    <style>
      <@component.style />
    </style>
  </#if>
  <#-- 组件代码 -->
  <@ready>
    Vue.component("${name}", {
      <#if component.template??>
        template: "#${name}"
        <#if moduleId?has_content>
          ,mixins: [{
            data: function () {
              return {
                currentUserId: '${_.currentUserId(moduleId)}',
                currentUserRole: '${_.currentUserRole(moduleId)}'
              }
            },
            methods: {
              moduleAction: function(actionName, customParams, customFunction, validFormRefName) {
                var isValid = true;
                if (validFormRefName) {
                  this.$refs[validFormRefName].validate((valid) => {
                    isValid = valid;
                  });
                }
                if (isValid) {
                  ajaxAction("${_.safeUrl(moduleConfig.actionUrl)}", $.extend({
                    module_id: "${moduleId}"
                  }, {
                    action: actionName
                  }, this.commonParams || {}, customParams), customFunction);
                }
              },
            }
          }]
        </#if>
        <#if component.script??>,</#if>
      </#if>
      <#if component.script??>
        <@component.script />
      </#if>
    });
  </@ready>
</#macro>

<#-- 建立 Vue 容器并初始化 -->
<#macro vueContainer id>
  <#-- 初始化代码放在前面，在 ready 中优先执行 -->
  <@ready>
    new Vue({
      el: '#${id}'
    });
  </@ready>
  <div id="${id}"><#nested></div>
</#macro>

<#-- 在页面中仅渲染一次 -->
<#macro renderOnce id>
  <#if !((.globals.renderOnceList![])?seq_contains(id))>
    <#global renderOnceList = (renderOnceList![]) + [id]>
    <#nested><#t>
  </#if>
</#macro>

<#-- 加载页面局部（实现多个页面局部放在同一个文件中，使用 macro 区分） -->
<#macro renderPart part>
  <#local errorInfo = "">
  <#if !(.main[part]??)>
    <#local errorInfo = "页面未找到">
  <#elseif part?starts_with("_")>
    <#local errorInfo = "下划线开头的页面仅供内部调用">
  </#if>
  <#if errorInfo?has_content>
    <div class="alert alert-danger text-center mb-0">
      ${errorInfo}
      <div class="tag tag-dark ml-2">part<span class="tag-addon tag-danger">${part}</span></div>
    </div>
  <#else>
    <@.main[part] />
  </#if>
</#macro>
