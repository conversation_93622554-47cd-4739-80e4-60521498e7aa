-- [getMajors] 获取专业列表
SELECT
  m.*
  <#if withStats!false>
    ,
    (
      SELECT count(*) FROM eduobjs
      WHERE majid = m.majid
    ) AS eobjnum,
    (
      SELECT count(*) FROM gradouts AS gout
      WHERE majid = m.majid AND gout.goutlevel = 1
    ) AS gout1num,
    (
      SELECT count(*) FROM gradouts AS gout
      WHERE majid = m.majid AND gout.goutlevel = 2
    ) AS gout2num,
    (
      SELECT count(*) FROM raw_courses
      WHERE majid = m.majid AND isvalid
    ) AS coursenum,
    (
      SELECT count(*) FROM view_courses
      WHERE majid = m.majid
    ) AS viewcoursenum,
    (
      SELECT count(*) FROM raw_mapcourses
      WHERE majid = m.majid AND r_level = 1 AND isvalid
    ) AS mapcourse1num,
    (
      SELECT count(*) FROM raw_mapcourses
      WHERE majid = m.majid AND r_level = 2 AND isvalid
    ) AS mapcourse2num,
    (
      SELECT count(*) FROM gout2eobj
      WHERE majid = m.majid AND goutlevel(goutid) = 1
    ) AS gout2eobj1num,
    (
      SELECT count(*) FROM gout2eobj
      WHERE majid = m.majid AND goutlevel(goutid) = 2
    ) AS gout2eobj2num,
    (
      SELECT count(*) FROM raw_course2gout
      WHERE majid = m.majid AND goutlevel(goutid) = 1 AND isvalid
    ) AS course2gout1num,
    (
      SELECT count(*) FROM raw_course2gout
      WHERE majid = m.majid AND goutlevel(goutid) = 2 AND isvalid
    ) AS course2gout2num
  </#if>
FROM majors AS m
ORDER BY majid

-- [getEduobjs] 获取培养目标列表
SELECT * FROM eduobjs
WHERE majid = :majid
ORDER BY eobjid

-- [getGradouts] 获取毕业要求列表
SELECT
  gout.*,
  gout.goutlevel,
  CASE
    WHEN gout.goutlevel = 1 THEN (
      SELECT count(*) FROM gradouts
      WHERE majid = gout.majid AND goutid LIKE gout.goutid || '.%'
    ) ELSE 0
  END AS gout2num
FROM gradouts AS gout
WHERE
  majid = :majid
  <#if goutlevel?has_content>
    AND gout.goutlevel = :goutlevel::integer
  </#if>
ORDER BY string_to_array(goutid, '.')::int[]

-- [getMajor] 获取专业基本信息
SELECT * FROM majors
WHERE majid = :majid

-- [getGout2Eobj] 获取毕业要求与培养目标映射关系
SELECT
  *,
  goutid || '-' || eobjid AS mapkey
FROM gout2eobj
WHERE majid = :majid
  <#if goutlevel?has_content>
    AND goutlevel(goutid) = :goutlevel::integer
  </#if>
ORDER BY string_to_array(goutid, '.')::int[], eobjid

-- [checkGE1&GE2] 检查毕业要求两个层级是否存在不合理的映射（通常从 GE2 角度判定）
WITH ge1 AS (
  SELECT
    majid,
    goutid,
    eobjid,
    supplevel_int(supplevel) AS supplevel_int
  FROM gout2eobj
  WHERE
    majid = :majid
    AND goutlevel(goutid) = 1
),

ge2 AS (
  SELECT
    majid,
    eobjid,
    split_part(goutid, '.', 1) AS parent_goutid,
    max(supplevel_int(supplevel)) AS max_supplevel_int,
    min(supplevel_int(supplevel)) AS min_supplevel_int
  FROM gout2eobj
  WHERE
    goutlevel(goutid) = 2
    AND majid = :majid
  GROUP BY majid, split_part(goutid, '.', 1), eobjid
)

SELECT
  coalesce(ge1.majid, ge2.majid) AS majid,
  coalesce(ge1.goutid, ge2.parent_goutid) AS goutid,
  coalesce(ge1.eobjid, ge2.eobjid) AS eobjid,
  coalesce(ge1.goutid, ge2.parent_goutid) || '-' || coalesce(ge1.eobjid, ge2.eobjid) AS mapkey,
  coalesce(ge1.supplevel_int, 0) AS ge1_supplevel_int,
  coalesce(ge2.min_supplevel_int, 0) AS ge2_min_supplevel_int,
  coalesce(ge2.max_supplevel_int, 0) AS ge2_max_supplevel_int,
  CASE
    WHEN ge1.majid IS NULL THEN 'NO_GE1'
    WHEN ge2.majid IS NULL THEN 'NO_GE2'
    WHEN ge2.max_supplevel_int < ge1.supplevel_int THEN 'LESS_GE2'
    WHEN ge2.min_supplevel_int > ge1.supplevel_int THEN 'MORE_GE2'
  END AS result
FROM ge1
FULL JOIN ge2 ON ge1.majid = ge2.majid AND ge1.goutid = ge2.parent_goutid AND ge1.eobjid = ge2.eobjid
WHERE
  ge1.majid IS NULL
  OR ge2.majid IS NULL
  OR ge2.max_supplevel_int < ge1.supplevel_int
  OR ge2.min_supplevel_int > ge1.supplevel_int

-- [getGout2EobjInvalidEobjs] 获取缺失强支撑关系的培养目标
SELECT * FROM eduobjs AS eobj
WHERE
  majid = :majid
  AND NOT EXISTS (
    SELECT * FROM gout2eobj
    WHERE
      majid = eobj.majid
      AND eobjid = eobj.eobjid
      AND supplevel = 'H'
      <#if goutlevel?has_content>
        AND goutlevel(goutid) = :goutlevel::integer
      </#if>
  )
ORDER BY eobjid

-- [getGout2EobjInvalidGouts] 获取未提供强支撑关系的毕业要求
SELECT * FROM gradouts AS gout
WHERE
  majid = :majid
  AND NOT EXISTS (
    SELECT * FROM gout2eobj
    WHERE
      majid = gout.majid
      AND goutid = gout.goutid
      AND supplevel = 'H'
  )
  <#if goutlevel?has_content>
    AND goutlevel(goutid) = :goutlevel::integer
  </#if>
ORDER BY goutid

-- [getRawCourses] 获取课程列表
SELECT t.* FROM (
  SELECT
    rc.*,
    coalesce(u_coursename, r_coursename) AS coursename,
    coalesce(u_credit::text, r_credit) AS credit,
    coalesce(u_attr, r_attr) AS attr,
    coalesce(u_typename, r_typename) AS typename
  FROM raw_courses AS rc
) AS t
WHERE t.majid = :majid
  AND t.isvalid
  <#if attr?has_content>AND t.attr = '必修'</#if>
ORDER BY
  <#switch orderby!>
    <#on "coursename">
      convert_to(t.coursename, 'GBK'),
    <#on "coursetype">
      coursetypeseq(t.typename),
  </#switch>
  t.r_seq

-- [getViewCourses] 获取课程列表（基于底层数据整合）
SELECT
  <#if withMapping!false>
    (
      SELECT count(*) FROM raw_mapcourses
      WHERE majid = vc.majid
      AND coalesce(u_coursename, r_coursename) = vc.coursename
      AND r_level = 1
    ) AS mapcourse1num,
    (
      SELECT count(*) FROM raw_mapcourses
      WHERE majid = vc.majid
      AND coalesce(u_coursename, r_coursename) = vc.coursename
      AND r_level = 2
    ) AS mapcourse2num,
    (
      SELECT count(*) FROM raw_course2gout
      WHERE majid = vc.majid
      AND goutlevel(goutid) = 1
      AND coalesce(u_coursename, r_coursename) = vc.coursename
    ) AS course2gout1num,
    (
      SELECT count(*) FROM raw_course2gout
      WHERE majid = vc.majid
      AND goutlevel(goutid) = 2
      AND coalesce(u_coursename, r_coursename) = vc.coursename
    ) AS course2gout2num,
  </#if>
  vc.*
FROM view_courses AS vc
WHERE majid = :majid
  <#if attr?has_content>AND attr = '必修'</#if>
ORDER BY
  <#switch orderby!>
    <#on "coursename">
      convert_to(coursename, 'GBK'),
    <#on "coursetype">
      coursetypeseq(typename),
  </#switch>
  seq

-- [getRawMapCourses] 获取参与映射课程列表
SELECT t.* FROM (
  SELECT
    rmc.*,
    coalesce(u_coursename, r_coursename) AS coursename,
    (
      SELECT DISTINCT attr FROM view_courses
      WHERE
        majid = rmc.majid
        AND coursename = coalesce(u_coursename, r_coursename)
    ) AS attr
  FROM raw_mapcourses AS rmc
) AS t
WHERE majid = :majid AND r_level = :r_level::integer AND isvalid
  <#if attr?has_content>AND attr = :attr</#if>
ORDER BY
  <#if orderby! == "coursename">
    convert_to(coursename, 'GBK')
  <#else>
    r_seq
  </#if>

-- [getCourseTypes] 获取课程类型列表
SELECT * FROM (
  SELECT
    typeseq,
    typename
  FROM coursetypes
  UNION ALL
  SELECT DISTINCT
    NULL::int AS typeseq,
    coalesce(u_typename, r_typename) AS typename
  FROM raw_courses
  WHERE coalesce(u_typename, r_typename) NOT IN (SELECT typename FROM coursetypes)
) AS t
ORDER BY t.typeseq, convert_to(t.typename, 'GBK')

-- [getDepts] 获取开课机构列表
SELECT
  d.*,
  (
    SELECT count(*) FROM raw_courses
    WHERE coalesce(u_deptid, r_dept) = d.deptid
  ) AS coursenum
FROM depts AS d
ORDER BY deptid

-- [getCourse2Gout] 获取课程支撑毕业要求映射关系
SELECT
  cg.*,
  coalesce(u_coursename, r_coursename) AS coursename,
  goutid || '-' || coalesce(u_coursename, r_coursename) AS mapkey
FROM raw_course2gout AS cg
WHERE majid = :majid AND isvalid
  <#if goutlevel?has_content>
    AND goutlevel(goutid) = :goutlevel::integer
  </#if>
ORDER BY r_seq

-- [getGout2EobjInvalidEobjs] 获取缺失强支撑关系的培养目标
SELECT * FROM eduobjs AS eobj
WHERE
  majid = :majid
  AND NOT EXISTS (
    SELECT * FROM gout2eobj
    WHERE
      majid = eobj.majid
      AND eobjid = eobj.eobjid
      AND supplevel = 'H'
      <#if goutlevel?has_content>
        AND goutlevel(goutid) = :goutlevel::integer
      </#if>
  )
ORDER BY eobjid

-- [getCourse2GoutInvalidCourses] 获取未提供强支撑关系的课程
SELECT
  rmc.*,
  rmc.coursename
FROM raw_mapcourses AS rmc
WHERE
  majid = :majid
  AND NOT EXISTS (
    SELECT * FROM raw_course2gout
    WHERE
      majid = rmc.majid
      AND coalesce(u_coursename, r_coursename) = rmc.coursename
      AND supplevel = 'H'
      <#if goutlevel?has_content>
        AND goutlevel(goutid) = :goutlevel::integer
      </#if>
  )
  <#if attr?has_content>
    AND (
      SELECT DISTINCT attr FROM view_courses
      WHERE
        majid = rmc.majid
        AND coursename = rmc.coursename
    ) = :attr
  </#if>
  <#if r_level?has_content>
    AND r_level = :r_level::integer
  </#if>
ORDER BY r_seq

-- [getCourse2GoutInvalidGouts] 获取缺失强支撑关系的毕业要求
SELECT * FROM gradouts AS gout
WHERE
  majid = :majid
  AND NOT EXISTS (
    SELECT * FROM raw_course2gout AS cg
    WHERE
      majid = gout.majid
      AND goutid = gout.goutid
      AND supplevel = 'H'
      <#if attr?has_content>
        AND (
          SELECT attr FROM view_courses
          WHERE
            majid = gout.majid
            AND coursename = cg.coursename
        ) = :attr
      </#if>
  )
  <#if goutlevel?has_content>
    AND goutlevel(goutid) = :goutlevel::integer
  </#if>
ORDER BY goutid

-- [getSameNameDiffIdCourses] 统计同名不同 ID 的课程信息
SELECT coursename,
       idnum,
       (SELECT STRING_AGG('【' || x.elem || '：' || x.count || '】', '' ORDER BY x.count DESC, elem)
        FROM (SELECT elem, COUNT(*) AS count FROM UNNEST(courseids) AS elem GROUP BY elem) x) AS idstats
FROM (SELECT coursename, COUNT(DISTINCT courseid) AS idnum, ARRAY_AGG(courseid) AS courseids
      FROM view_courses
      WHERE courseid != '/'
        AND groupcoursenum = 0
      GROUP BY coursename
      HAVING COUNT(DISTINCT courseid) > 1) t
ORDER BY CONVERT_TO(coursename, 'GBK')

-- [getSameIdDiffNameCourses] 统计同 ID 不同名的课程信息
SELECT courseid,
       namenum,
       (SELECT STRING_AGG('【' || x.elem || '：' || x.count || '】', '' ORDER BY x.count DESC, CONVERT_TO(elem, 'GBK'))
        FROM (SELECT elem, COUNT(*) AS count FROM UNNEST(coursenames) AS elem GROUP BY elem) x) AS namestats
FROM (SELECT courseid, COUNT(DISTINCT coursename) AS namenum, ARRAY_AGG(coursename) AS coursenames
      FROM view_courses
      WHERE courseid NOT IN ('/', 'NA')
        AND groupcoursenum = 0
      GROUP BY courseid
      HAVING COUNT(DISTINCT coursename) > 1) t
ORDER BY courseid ASC

-- [extractCourse2Gout1] 从课程支撑指标点信息中提取课程支撑毕业要求情况
WITH g AS (SELECT gout.majid,
                  gout.goutid,
                  gout.goutlevel,
                  (SELECT COUNT(*)
                   FROM gradouts
                   WHERE majid = gout.majid
                     AND goutid LIKE gout.goutid || '.%')
                    AS gout2num
           FROM gradouts AS gout
           WHERE majid = :majid
             AND gout.goutlevel = 1)
SELECT t.majid,
       t.coursename,
       t.goutid,
       t.goutid || '-' || t.coursename AS mapkey,
       ARRAY_TO_STRING(t.supplevels, '') AS supplevels,
       t.supplevelsum,
       g.gout2num,
       ROUND(supplevelsum::numeric / gout2num, 2) AS supplevelavg,
       CASE
         WHEN supplevelsum::numeric / gout2num <= 1 THEN 'L'
         WHEN supplevelsum::numeric / gout2num <= 2 THEN 'M'
         WHEN supplevelsum::numeric / gout2num > 2 THEN 'H'
         ELSE '' END AS supplevelcalc,
       CASE
         WHEN 'H' = ANY (supplevels) THEN 'H'
         WHEN 'M' = ANY (supplevels) THEN 'M'
         WHEN 'L' = ANY (supplevels) THEN 'L'
         ELSE '' END AS supplevelmax
FROM (SELECT majid,
             cg.coursename,
             SPLIT_PART(cg.goutid, '.', 1) AS goutid,
             ARRAY_AGG(supplevel ORDER BY supplevel_int(supplevel) DESC) AS supplevels,
             SUM(supplevel_int(supplevel)) AS supplevelsum
      FROM raw_course2gout cg
      WHERE majid = :majid
        AND goutlevel(goutid) = 2
        AND isvalid
      GROUP BY majid, cg.coursename, SPLIT_PART(cg.goutid, '.', 1)) t
     JOIN g ON t.majid = g.majid AND t.goutid = g.goutid

-- [getCoursesForCreditStats] 获取课程列表（用于学分统计展示）
SELECT
  COALESCE(rc.u_coursename, rc.r_coursename) AS coursename,
  COALESCE(rc.u_attr, rc.r_attr) AS attr,
  COALESCE(rc.u_typename, rc.r_typename) AS typename,
  COALESCE(rc.u_credit::text, rc.r_credit) AS credit,
  rc.r_credit_t,
  rc.r_credit_p
FROM raw_courses AS rc
WHERE majid = :majid AND isvalid
ORDER BY rc.r_seq

-- [getCreditStatsByCourseType] 按课程类别统计学分
WITH s AS (
  SELECT
    COALESCE(rc.u_typename, rc.r_typename) AS typename,
    COALESCE(rc.u_attr, rc.r_attr) AS attr,
    COALESCE(SUM(rc.r_credit_t::numeric), 0) + COALESCE(SUM(rc.r_credit_p::numeric), 0) AS credit,
    COALESCE(SUM(rc.r_credit_t::numeric), 0) AS credit_t,
    COALESCE(SUM(rc.r_credit_p::numeric), 0) AS credit_p
  FROM raw_courses AS rc
  WHERE rc.majid = :majid AND isvalid
  GROUP BY COALESCE(rc.u_typename, rc.r_typename), COALESCE(rc.u_attr, rc.r_attr)
),

t AS (SELECT SUM(credit_t) + SUM(credit_p) AS total_credit FROM s)

SELECT
  ct.typename,
  ROW_NUMBER() OVER (
    PARTITION BY ct.typename
    ORDER BY attr
  ) AS tanum,
  COUNT(*) OVER (PARTITION BY ct.typename) AS tacount,
  attr,
  credit,
  credit_t,
  credit_p,
  total_credit,
  ROUND((credit_t + credit_p) / total_credit, 3) AS credit_percent
FROM s
JOIN coursetypes AS ct ON s.typename = ct.typename
JOIN t ON TRUE
ORDER BY ct.typeseq, s.attr

-- [getMajorCourseList] 获取专业课程列表
SELECT
  rc.r_seq,
  rc.rcflag,
  COALESCE(rc.u_coursename, rc.r_coursename) AS coursename,
  COALESCE(rc.u_term::text, rc.r_term) AS term,
  COALESCE(u_typename, rc.r_typename) AS typename,
  COALESCE(rc.u_attr, rc.r_attr) AS attr,
  COALESCE(rc.r_credit_t::numeric, 0) + COALESCE(rc.r_credit_p::numeric, 0) AS credit,
  COALESCE(rc.r_credit_t::numeric, 0) AS credit_t,
  COALESCE(rc.r_credit_p::numeric, 0) AS credit_p
FROM raw_courses AS rc
WHERE
  majid = :majid
  AND rcflag IS NOT NULL
  AND isvalid
ORDER BY term, r_seq
