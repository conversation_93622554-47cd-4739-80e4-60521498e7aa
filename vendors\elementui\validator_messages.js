var ASYNC_VALIDATOR_MESSAGES = {
  default: function() { return "数据验证有误"; /* Validation error on field %s */ },
  required: function() { return "请完成必填项"; /* %s is required */ },
  enum: function(field, info) { return "值必须是以下内容之一：" + info; /* %s must be one of %s */ },
  whitespace: function() { return "内容不可为空白字符"; /* %s cannot be empty */ },
  date: {
    format: function() { return "日期格式不正确"; /* %s date %s is invalid for format %s */ },
    parse: function() { return "日期不能被正确解析"; /* %s date could not be parsed, %s is invalid  */ },
    invalid: function() { return "日期无效"; /* %s date %s is invalid */ }
  },
  types: {
    string: function() { return "类型应当是字符串"; /* %s is not a %s */ },
    method: function() { return "类型应当是方法（函数）"; /* %s is not a %s (function) */ },
    array: function() { return "类型应当是数组"; /* %s is not an %s */ },
    object: function() { return "类型应当是对象"; /* %s is not an %s */ },
    number: function() { return "类型应该是数字"; /* %s is not a %s */ },
    date: function() { return "类型应该是日期"; /* %s is not a %s */ },
    boolean: function() { return "类型应该是布尔型"; /* %s is not a %s */ },
    integer: function() { return "类型应该是整数"; /* %s is not an %s */ },
    float: function() { return "类型应该是浮点数"; /* %s is not a %s */ },
    regexp: function() { return "请输入有效的正则表达式"; /* %s is not a valid %s */ },
    email: function() { return "请输入有效的电子邮件地址"; /* %s is not a valid %s */ },
    url: function() { return "请输入有效的网址"; /* %s is not a valid %s */ },
    hex: function() { return "请输入有效的十六进制颜色码"; /* %s is not a valid %s */ }
  },
  string: {
    len: function(field, info) { return "字符长度必须是 " + info; /* %s must be exactly %s characters */ },
    min: function(field, info) { return "最少要输入 " + info + " 个字符"; /* %s must be at least %s characters */ },
    max: function(field, info) { return "最多可以输入 " + info + " 个字符"; /* %s cannot be longer than %s characters */ },
    range: function(field, rmin, rmax) { return "请输入长度在 " + rmin + " 到 " + rmax + " 之间的字符串"; /* %s must be between %s and %s characters */ }
  },
  number: {
    len: function(field, info) { return "数值必须是 " + info; /* %s must equal %s */ },
    min: function(field, info) { return "请输入不小于 " + info + " 的数值"; /* %s cannot be less than %s */ },
    max: function(field, info) { return "请输入不大于 " + info + " 的数值"; /* %s cannot be greater than %s */ },
    range: function(field, rmin, rmax) { return "请输入范围在 " + rmin + " 到 " + rmax + " 之间的数值"; /* %s must be between %s and %s */ }
  },
  array: {
    len: function(field, info) { return "必须选择 " + info + " 项"; /* %s must be exactly %s in length */ },
    min: function(field, info) { return "至少选择 " + info + " 项"; /* %s cannot be less than %s in length */ },
    max: function(field, info) { return "最多选择 " + info + " 项"; /* %s cannot be greater than %s in length */ },
    range: function(field, rmin, rmax) { return "请选择 " + rmin + " 到 " + rmax + " 项"; /* %s must be between %s and %s in length */ }
  },
  pattern: {
    mismatch: function() { return "数据格式不符合要求"; /* %s value %s does not match pattern %s */ }
  },
  clone: function () {
    var e = JSON.parse(JSON.stringify(this));
    return (e.clone = this.clone), e;
  },
};
