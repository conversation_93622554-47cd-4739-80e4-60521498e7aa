<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd">

  <!-- JDBC Template -->
  <bean id="jdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
    <property name="dataSource" ref="dataSource" />
  </bean>

  <bean id="namedParameterJdbcTemplate" class="org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate">
    <constructor-arg ref="jdbcTemplate" />
  </bean>

  <!-- Transaction Manager -->
  <bean id="txManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
    <property name="dataSource" ref="dataSource" />
  </bean>

  <bean id="txTemplate" class="org.springframework.transaction.support.TransactionTemplate">
    <property name="transactionManager" ref="txManager" />
    <property name="readOnly" value="false" />
    <property name="isolationLevelName" value="ISOLATION_DEFAULT" />
    <property name="propagationBehaviorName" value="PROPAGATION_REQUIRED" />
  </bean>

  <!-- SqlTemplate -->
  <bean id="sqlTemplate" class="neo.lib.sql.SqlTemplate">
    <constructor-arg name="jt" ref="jdbcTemplate" />
    <constructor-arg name="npjt" ref="namedParameterJdbcTemplate" />
    <constructor-arg name="sqlMgr" ref="sqlManager" />
  </bean>

</beans>