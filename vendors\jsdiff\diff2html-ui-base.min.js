!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var n=t();for(var r in n)("object"==typeof exports?exports:e)[r]=n[r]}}(this,(()=>{return e={583:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.convertChangesToDMP=function(e){for(var t,n,r=[],i=0;i<e.length;i++)n=(t=e[i]).added?1:t.removed?-1:0,r.push([n,t.value]);return r}},591:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.convertChangesToXML=function(e){for(var t=[],n=0;n<e.length;n++){var r=e[n];r.added?t.push("<ins>"):r.removed&&t.push("<del>"),t.push(r.value.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;")),r.added?t.push("</ins>"):r.removed&&t.push("</del>")}return t.join("")}},321:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.arrayDiff=void 0,t.diffArrays=function(e,t,n){return s.diff(e,t,n)};var r,i=(r=n(255))&&r.__esModule?r:{default:r},s=t.arrayDiff=new i.default;s.tokenize=function(e){return e.slice()},s.join=s.removeEmpty=function(e){return e}},255:(e,t)=>{"use strict";function n(){}function r(e,t,n,r,i){for(var s,l=[];t;)l.push(t),s=t.previousComponent,delete t.previousComponent,t=s;l.reverse();for(var o=0,a=l.length,c=0,u=0;o<a;o++){var f=l[o];if(f.removed)f.value=e.join(r.slice(u,u+f.count)),u+=f.count;else{if(!f.added&&i){var d=n.slice(c,c+f.count);d=d.map((function(e,t){var n=r[u+t];return n.length>e.length?n:e})),f.value=e.join(d)}else f.value=e.join(n.slice(c,c+f.count));c+=f.count,f.added||(u+=f.count)}}return l}Object.defineProperty(t,"__esModule",{value:!0}),t.default=n,n.prototype={diff:function(e,t){var n,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},s=i.callback;"function"==typeof i&&(s=i,i={});var l=this;function o(e){return e=l.postProcess(e,i),s?(setTimeout((function(){s(e)}),0),!0):e}e=this.castInput(e,i),t=this.castInput(t,i),e=this.removeEmpty(this.tokenize(e,i));var a=(t=this.removeEmpty(this.tokenize(t,i))).length,c=e.length,u=1,f=a+c;null!=i.maxEditLength&&(f=Math.min(f,i.maxEditLength));var d=null!==(n=i.timeout)&&void 0!==n?n:1/0,h=Date.now()+d,p=[{oldPos:-1,lastComponent:void 0}],g=this.extractCommon(p[0],t,e,0,i);if(p[0].oldPos+1>=c&&g+1>=a)return o(r(l,p[0].lastComponent,t,e,l.useLongestToken));var m=-1/0,b=1/0;function v(){for(var n=Math.max(m,-u);n<=Math.min(b,u);n+=2){var s=void 0,f=p[n-1],d=p[n+1];f&&(p[n-1]=void 0);var h=!1;if(d){var v=d.oldPos-n;h=d&&0<=v&&v<a}var y=f&&f.oldPos+1<c;if(h||y){if(s=!y||h&&f.oldPos<d.oldPos?l.addToPath(d,!0,!1,0,i):l.addToPath(f,!1,!0,1,i),g=l.extractCommon(s,t,e,n,i),s.oldPos+1>=c&&g+1>=a)return o(r(l,s.lastComponent,t,e,l.useLongestToken));p[n]=s,s.oldPos+1>=c&&(b=Math.min(b,n-1)),g+1>=a&&(m=Math.max(m,n+1))}else p[n]=void 0}u++}if(s)!function e(){setTimeout((function(){if(u>f||Date.now()>h)return s();v()||e()}),0)}();else for(;u<=f&&Date.now()<=h;){var y=v();if(y)return y}},addToPath:function(e,t,n,r,i){var s=e.lastComponent;return s&&!i.oneChangePerToken&&s.added===t&&s.removed===n?{oldPos:e.oldPos+r,lastComponent:{count:s.count+1,added:t,removed:n,previousComponent:s.previousComponent}}:{oldPos:e.oldPos+r,lastComponent:{count:1,added:t,removed:n,previousComponent:s}}},extractCommon:function(e,t,n,r,i){for(var s=t.length,l=n.length,o=e.oldPos,a=o-r,c=0;a+1<s&&o+1<l&&this.equals(n[o+1],t[a+1],i);)a++,o++,c++,i.oneChangePerToken&&(e.lastComponent={count:1,previousComponent:e.lastComponent,added:!1,removed:!1});return c&&!i.oneChangePerToken&&(e.lastComponent={count:c,previousComponent:e.lastComponent,added:!1,removed:!1}),e.oldPos=o,a},equals:function(e,t,n){return n.comparator?n.comparator(e,t):e===t||n.ignoreCase&&e.toLowerCase()===t.toLowerCase()},removeEmpty:function(e){for(var t=[],n=0;n<e.length;n++)e[n]&&t.push(e[n]);return t},castInput:function(e){return e},tokenize:function(e){return Array.from(e)},join:function(e){return e.join("")},postProcess:function(e){return e}}},687:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.characterDiff=void 0,t.diffChars=function(e,t,n){return s.diff(e,t,n)};var r,i=(r=n(255))&&r.__esModule?r:{default:r},s=t.characterDiff=new i.default},79:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.cssDiff=void 0,t.diffCss=function(e,t,n){return s.diff(e,t,n)};var r,i=(r=n(255))&&r.__esModule?r:{default:r},s=t.cssDiff=new i.default;s.tokenize=function(e){return e.split(/([{}:;,]|\s+)/)}},728:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.canonicalize=a,t.diffJson=function(e,t,n){return o.diff(e,t,n)},t.jsonDiff=void 0;var r,i=(r=n(255))&&r.__esModule?r:{default:r},s=n(268);function l(e){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},l(e)}var o=t.jsonDiff=new i.default;function a(e,t,n,r,i){var s,o;for(t=t||[],n=n||[],r&&(e=r(i,e)),s=0;s<t.length;s+=1)if(t[s]===e)return n[s];if("[object Array]"===Object.prototype.toString.call(e)){for(t.push(e),o=new Array(e.length),n.push(o),s=0;s<e.length;s+=1)o[s]=a(e[s],t,n,r,i);return t.pop(),n.pop(),o}if(e&&e.toJSON&&(e=e.toJSON()),"object"===l(e)&&null!==e){t.push(e),o={},n.push(o);var c,u=[];for(c in e)Object.prototype.hasOwnProperty.call(e,c)&&u.push(c);for(u.sort(),s=0;s<u.length;s+=1)o[c=u[s]]=a(e[c],t,n,r,c);t.pop(),n.pop()}else o=e;return o}o.useLongestToken=!0,o.tokenize=s.lineDiff.tokenize,o.castInput=function(e,t){var n=t.undefinedReplacement,r=t.stringifyReplacer,i=void 0===r?function(e,t){return void 0===t?n:t}:r;return"string"==typeof e?e:JSON.stringify(a(e,null,null,i),i,"  ")},o.equals=function(e,t,n){return i.default.prototype.equals.call(o,e.replace(/,([\r\n])/g,"$1"),t.replace(/,([\r\n])/g,"$1"),n)}},268:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.diffLines=function(e,t,n){return l.diff(e,t,n)},t.diffTrimmedLines=function(e,t,n){var r=(0,s.generateOptions)(n,{ignoreWhitespace:!0});return l.diff(e,t,r)},t.lineDiff=void 0;var r,i=(r=n(255))&&r.__esModule?r:{default:r},s=n(65),l=t.lineDiff=new i.default;l.tokenize=function(e,t){t.stripTrailingCr&&(e=e.replace(/\r\n/g,"\n"));var n=[],r=e.split(/(\n|\r\n)/);r[r.length-1]||r.pop();for(var i=0;i<r.length;i++){var s=r[i];i%2&&!t.newlineIsToken?n[n.length-1]+=s:n.push(s)}return n},l.equals=function(e,t,n){return n.ignoreWhitespace?(n.newlineIsToken&&e.includes("\n")||(e=e.trim()),n.newlineIsToken&&t.includes("\n")||(t=t.trim())):n.ignoreNewlineAtEof&&!n.newlineIsToken&&(e.endsWith("\n")&&(e=e.slice(0,-1)),t.endsWith("\n")&&(t=t.slice(0,-1))),i.default.prototype.equals.call(this,e,t,n)}},883:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.diffSentences=function(e,t,n){return s.diff(e,t,n)},t.sentenceDiff=void 0;var r,i=(r=n(255))&&r.__esModule?r:{default:r},s=t.sentenceDiff=new i.default;s.tokenize=function(e){return e.split(/(\S.+?[.!?])(?=\s+|$)/)}},422:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.diffWords=function(e,t,n){return null==(null==n?void 0:n.ignoreWhitespace)||n.ignoreWhitespace?a.diff(e,t,n):f(e,t,n)},t.diffWordsWithSpace=f,t.wordWithSpaceDiff=t.wordDiff=void 0;var r,i=(r=n(255))&&r.__esModule?r:{default:r},s=n(666),l="a-zA-Z0-9_\\u{C0}-\\u{FF}\\u{D8}-\\u{F6}\\u{F8}-\\u{2C6}\\u{2C8}-\\u{2D7}\\u{2DE}-\\u{2FF}\\u{1E00}-\\u{1EFF}",o=new RegExp("[".concat(l,"]+|\\s+|[^").concat(l,"]"),"ug"),a=t.wordDiff=new i.default;function c(e,t,n,r){if(t&&n){var i=t.value.match(/^\s*/)[0],l=t.value.match(/\s*$/)[0],o=n.value.match(/^\s*/)[0],a=n.value.match(/\s*$/)[0];if(e){var c=(0,s.longestCommonPrefix)(i,o);e.value=(0,s.replaceSuffix)(e.value,o,c),t.value=(0,s.removePrefix)(t.value,c),n.value=(0,s.removePrefix)(n.value,c)}if(r){var u=(0,s.longestCommonSuffix)(l,a);r.value=(0,s.replacePrefix)(r.value,a,u),t.value=(0,s.removeSuffix)(t.value,u),n.value=(0,s.removeSuffix)(n.value,u)}}else if(n)e&&(n.value=n.value.replace(/^\s*/,"")),r&&(r.value=r.value.replace(/^\s*/,""));else if(e&&r){var f=r.value.match(/^\s*/)[0],d=t.value.match(/^\s*/)[0],h=t.value.match(/\s*$/)[0],p=(0,s.longestCommonPrefix)(f,d);t.value=(0,s.removePrefix)(t.value,p);var g=(0,s.longestCommonSuffix)((0,s.removePrefix)(f,p),h);t.value=(0,s.removeSuffix)(t.value,g),r.value=(0,s.replacePrefix)(r.value,f,g),e.value=(0,s.replaceSuffix)(e.value,f,f.slice(0,f.length-g.length))}else if(r){var m=r.value.match(/^\s*/)[0],b=t.value.match(/\s*$/)[0],v=(0,s.maximumOverlap)(b,m);t.value=(0,s.removeSuffix)(t.value,v)}else if(e){var y=e.value.match(/\s*$/)[0],w=t.value.match(/^\s*/)[0],j=(0,s.maximumOverlap)(y,w);t.value=(0,s.removePrefix)(t.value,j)}}a.equals=function(e,t,n){return n.ignoreCase&&(e=e.toLowerCase(),t=t.toLowerCase()),e.trim()===t.trim()},a.tokenize=function(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(n.intlSegmenter){if("word"!=n.intlSegmenter.resolvedOptions().granularity)throw new Error('The segmenter passed must have a granularity of "word"');t=Array.from(n.intlSegmenter.segment(e),(function(e){return e.segment}))}else t=e.match(o)||[];var r=[],i=null;return t.forEach((function(e){/\s/.test(e)?null==i?r.push(e):r.push(r.pop()+e):/\s/.test(i)?r[r.length-1]==i?r.push(r.pop()+e):r.push(i+e):r.push(e),i=e})),r},a.join=function(e){return e.map((function(e,t){return 0==t?e:e.replace(/^\s+/,"")})).join("")},a.postProcess=function(e,t){if(!e||t.oneChangePerToken)return e;var n=null,r=null,i=null;return e.forEach((function(e){e.added?r=e:e.removed?i=e:((r||i)&&c(n,i,r,e),n=e,r=null,i=null)})),(r||i)&&c(n,i,r,null),e};var u=t.wordWithSpaceDiff=new i.default;function f(e,t,n){return u.diff(e,t,n)}u.tokenize=function(e){var t=new RegExp("(\\r?\\n)|[".concat(l,"]+|[^\\S\\n\\r]+|[^").concat(l,"]"),"ug");return e.match(t)||[]}},546:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Diff",{enumerable:!0,get:function(){return i.default}}),Object.defineProperty(t,"applyPatch",{enumerable:!0,get:function(){return d.applyPatch}}),Object.defineProperty(t,"applyPatches",{enumerable:!0,get:function(){return d.applyPatches}}),Object.defineProperty(t,"canonicalize",{enumerable:!0,get:function(){return u.canonicalize}}),Object.defineProperty(t,"convertChangesToDMP",{enumerable:!0,get:function(){return b.convertChangesToDMP}}),Object.defineProperty(t,"convertChangesToXML",{enumerable:!0,get:function(){return v.convertChangesToXML}}),Object.defineProperty(t,"createPatch",{enumerable:!0,get:function(){return m.createPatch}}),Object.defineProperty(t,"createTwoFilesPatch",{enumerable:!0,get:function(){return m.createTwoFilesPatch}}),Object.defineProperty(t,"diffArrays",{enumerable:!0,get:function(){return f.diffArrays}}),Object.defineProperty(t,"diffChars",{enumerable:!0,get:function(){return s.diffChars}}),Object.defineProperty(t,"diffCss",{enumerable:!0,get:function(){return c.diffCss}}),Object.defineProperty(t,"diffJson",{enumerable:!0,get:function(){return u.diffJson}}),Object.defineProperty(t,"diffLines",{enumerable:!0,get:function(){return o.diffLines}}),Object.defineProperty(t,"diffSentences",{enumerable:!0,get:function(){return a.diffSentences}}),Object.defineProperty(t,"diffTrimmedLines",{enumerable:!0,get:function(){return o.diffTrimmedLines}}),Object.defineProperty(t,"diffWords",{enumerable:!0,get:function(){return l.diffWords}}),Object.defineProperty(t,"diffWordsWithSpace",{enumerable:!0,get:function(){return l.diffWordsWithSpace}}),Object.defineProperty(t,"formatPatch",{enumerable:!0,get:function(){return m.formatPatch}}),Object.defineProperty(t,"merge",{enumerable:!0,get:function(){return p.merge}}),Object.defineProperty(t,"parsePatch",{enumerable:!0,get:function(){return h.parsePatch}}),Object.defineProperty(t,"reversePatch",{enumerable:!0,get:function(){return g.reversePatch}}),Object.defineProperty(t,"structuredPatch",{enumerable:!0,get:function(){return m.structuredPatch}});var r,i=(r=n(255))&&r.__esModule?r:{default:r},s=n(687),l=n(422),o=n(268),a=n(883),c=n(79),u=n(728),f=n(321),d=n(467),h=n(20),p=n(555),g=n(769),m=n(723),b=n(583),v=n(591)},467:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.applyPatch=a,t.applyPatches=function(e,t){"string"==typeof e&&(e=(0,l.parsePatch)(e));var n=0;!function r(){var i=e[n++];if(!i)return t.complete();t.loadFile(i,(function(e,n){if(e)return t.complete(e);var s=a(n,i,t);t.patched(i,s,(function(e){if(e)return t.complete(e);r()}))}))}()};var r,i=n(666),s=n(380),l=n(20),o=(r=n(439))&&r.__esModule?r:{default:r};function a(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if("string"==typeof t&&(t=(0,l.parsePatch)(t)),Array.isArray(t)){if(t.length>1)throw new Error("applyPatch only works with a single input.");t=t[0]}(n.autoConvertLineEndings||null==n.autoConvertLineEndings)&&((0,i.hasOnlyWinLineEndings)(e)&&(0,s.isUnix)(t)?t=(0,s.unixToWin)(t):(0,i.hasOnlyUnixLineEndings)(e)&&(0,s.isWin)(t)&&(t=(0,s.winToUnix)(t)));var r=e.split("\n"),a=t.hunks,c=n.compareLine||function(e,t,n,r){return t===r},u=n.fuzzFactor||0,f=0;if(u<0||!Number.isInteger(u))throw new Error("fuzzFactor must be a non-negative integer");if(!a.length)return e;for(var d="",h=!1,p=!1,g=0;g<a[a.length-1].lines.length;g++){var m=a[a.length-1].lines[g];"\\"==m[0]&&("+"==d[0]?h=!0:"-"==d[0]&&(p=!0)),d=m}if(h){if(p){if(!u&&""==r[r.length-1])return!1}else if(""==r[r.length-1])r.pop();else if(!u)return!1}else if(p)if(""!=r[r.length-1])r.push("");else if(!u)return!1;function b(e,t,n){for(var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,s=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],l=arguments.length>5&&void 0!==arguments[5]?arguments[5]:[],o=arguments.length>6&&void 0!==arguments[6]?arguments[6]:0,a=0,u=!1;i<e.length;i++){var f=e[i],d=f.length>0?f[0]:" ",h=f.length>0?f.substr(1):f;if("-"===d){if(!c(t+1,r[t],d,h))return n&&null!=r[t]?(l[o]=r[t],b(e,t+1,n-1,i,!1,l,o+1)):null;t++,a=0}if("+"===d){if(!s)return null;l[o]=h,o++,a=0,u=!0}if(" "===d){if(a++,l[o]=r[t],!c(t+1,r[t],d,h))return u||!n?null:r[t]&&(b(e,t+1,n-1,i+1,!1,l,o+1)||b(e,t+1,n-1,i,!1,l,o+1))||b(e,t,n-1,i+1,!1,l,o);o++,s=!0,u=!1,t++}}return o-=a,t-=a,l.length=o,{patchedLines:l,oldLineLastI:t-1}}for(var v=[],y=0,w=0;w<a.length;w++){for(var j=a[w],x=void 0,S=r.length-j.oldLines+u,O=void 0,L=0;L<=u;L++){O=j.oldStart+y-1;for(var C=(0,o.default)(O,f,S);void 0!==O&&!(x=b(j.lines,O,L));O=C());if(x)break}if(!x)return!1;for(var T=f;T<O;T++)v.push(r[T]);for(var P=0;P<x.patchedLines.length;P++){var k=x.patchedLines[P];v.push(k)}f=x.oldLineLastI+1,y=O+1-j.oldStart}for(var N=f;N<r.length;N++)v.push(r[N]);return v.join("\n")}},723:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createPatch=function(e,t,n,r,i,s){return d(e,e,t,n,r,i,s)},t.createTwoFilesPatch=d,t.formatPatch=f,t.structuredPatch=u;var r=n(268);function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function s(e){return function(e){if(Array.isArray(e))return l(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return l(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?l(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function o(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function a(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?o(Object(n),!0).forEach((function(t){c(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function c(e,t,n){var r;return r=function(e){if("object"!=i(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=i(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(t),(t="symbol"==i(r)?r:r+"")in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function u(e,t,n,i,l,o,c){if(c||(c={}),"function"==typeof c&&(c={callback:c}),void 0===c.context&&(c.context=4),c.newlineIsToken)throw new Error("newlineIsToken may not be used with patch-generation functions, only with diffing functions");if(!c.callback)return f((0,r.diffLines)(n,i,c));var u=c.callback;function f(n){if(n){n.push({value:"",lines:[]});for(var r=[],i=0,a=0,u=[],f=1,d=1,h=function(){var e,t,l,o=n[p],h=o.lines||(t=(e=o.value).endsWith("\n"),l=e.split("\n").map((function(e){return e+"\n"})),t?l.pop():l.push(l.pop().slice(0,-1)),l);if(o.lines=h,o.added||o.removed){var g;if(!i){var m=n[p-1];i=f,a=d,m&&(u=c.context>0?y(m.lines.slice(-c.context)):[],i-=u.length,a-=u.length)}(g=u).push.apply(g,s(h.map((function(e){return(o.added?"+":"-")+e})))),o.added?d+=h.length:f+=h.length}else{if(i)if(h.length<=2*c.context&&p<n.length-2){var b;(b=u).push.apply(b,s(y(h)))}else{var v,w=Math.min(h.length,c.context);(v=u).push.apply(v,s(y(h.slice(0,w))));var j={oldStart:i,oldLines:f-i+w,newStart:a,newLines:d-a+w,lines:u};r.push(j),i=0,a=0,u=[]}f+=h.length,d+=h.length}},p=0;p<n.length;p++)h();for(var g=0,m=r;g<m.length;g++)for(var b=m[g],v=0;v<b.lines.length;v++)b.lines[v].endsWith("\n")?b.lines[v]=b.lines[v].slice(0,-1):(b.lines.splice(v+1,0,"\\ No newline at end of file"),v++);return{oldFileName:e,newFileName:t,oldHeader:l,newHeader:o,hunks:r}}function y(e){return e.map((function(e){return" "+e}))}}(0,r.diffLines)(n,i,a(a({},c),{},{callback:function(e){var t=f(e);u(t)}}))}function f(e){if(Array.isArray(e))return e.map(f).join("\n");var t=[];e.oldFileName==e.newFileName&&t.push("Index: "+e.oldFileName),t.push("==================================================================="),t.push("--- "+e.oldFileName+(void 0===e.oldHeader?"":"\t"+e.oldHeader)),t.push("+++ "+e.newFileName+(void 0===e.newHeader?"":"\t"+e.newHeader));for(var n=0;n<e.hunks.length;n++){var r=e.hunks[n];0===r.oldLines&&(r.oldStart-=1),0===r.newLines&&(r.newStart-=1),t.push("@@ -"+r.oldStart+","+r.oldLines+" +"+r.newStart+","+r.newLines+" @@"),t.push.apply(t,r.lines)}return t.join("\n")+"\n"}function d(e,t,n,r,i,s,l){var o;if("function"==typeof l&&(l={callback:l}),null===(o=l)||void 0===o||!o.callback){var c=u(e,t,n,r,i,s,l);if(!c)return;return f(c)}var d=l.callback;u(e,t,n,r,i,s,a(a({},l),{},{callback:function(e){e?d(f(e)):d()}}))}},380:(e,t)=>{"use strict";function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function r(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function i(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?r(Object(n),!0).forEach((function(t){s(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):r(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function s(e,t,r){var i;return i=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(t),(t="symbol"==n(i)?i:i+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}Object.defineProperty(t,"__esModule",{value:!0}),t.isUnix=function(e){return Array.isArray(e)||(e=[e]),!e.some((function(e){return e.hunks.some((function(e){return e.lines.some((function(e){return!e.startsWith("\\")&&e.endsWith("\r")}))}))}))},t.isWin=function(e){return Array.isArray(e)||(e=[e]),e.some((function(e){return e.hunks.some((function(e){return e.lines.some((function(e){return e.endsWith("\r")}))}))}))&&e.every((function(e){return e.hunks.every((function(e){return e.lines.every((function(t,n){var r;return t.startsWith("\\")||t.endsWith("\r")||(null===(r=e.lines[n+1])||void 0===r?void 0:r.startsWith("\\"))}))}))}))},t.unixToWin=function e(t){return Array.isArray(t)?t.map(e):i(i({},t),{},{hunks:t.hunks.map((function(e){return i(i({},e),{},{lines:e.lines.map((function(t,n){var r;return t.startsWith("\\")||t.endsWith("\r")||null!==(r=e.lines[n+1])&&void 0!==r&&r.startsWith("\\")?t:t+"\r"}))})}))})},t.winToUnix=function e(t){return Array.isArray(t)?t.map(e):i(i({},t),{},{hunks:t.hunks.map((function(e){return i(i({},e),{},{lines:e.lines.map((function(e){return e.endsWith("\r")?e.substring(0,e.length-1):e}))})}))})}},555:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.calcLineCount=a,t.merge=function(e,t,n){e=c(e,n),t=c(t,n);var r={};(e.index||t.index)&&(r.index=e.index||t.index),(e.newFileName||t.newFileName)&&(u(e)?u(t)?(r.oldFileName=f(r,e.oldFileName,t.oldFileName),r.newFileName=f(r,e.newFileName,t.newFileName),r.oldHeader=f(r,e.oldHeader,t.oldHeader),r.newHeader=f(r,e.newHeader,t.newHeader)):(r.oldFileName=e.oldFileName,r.newFileName=e.newFileName,r.oldHeader=e.oldHeader,r.newHeader=e.newHeader):(r.oldFileName=t.oldFileName||e.oldFileName,r.newFileName=t.newFileName||e.newFileName,r.oldHeader=t.oldHeader||e.oldHeader,r.newHeader=t.newHeader||e.newHeader)),r.hunks=[];for(var i=0,s=0,l=0,o=0;i<e.hunks.length||s<t.hunks.length;){var a=e.hunks[i]||{oldStart:1/0},g=t.hunks[s]||{oldStart:1/0};if(d(a,g))r.hunks.push(h(a,l)),i++,o+=a.newLines-a.oldLines;else if(d(g,a))r.hunks.push(h(g,o)),s++,l+=g.newLines-g.oldLines;else{var m={oldStart:Math.min(a.oldStart,g.oldStart),oldLines:0,newStart:Math.min(a.newStart+l,g.oldStart+o),newLines:0,lines:[]};p(m,a.oldStart,a.lines,g.oldStart,g.lines),s++,i++,r.hunks.push(m)}}return r};var r=n(723),i=n(20),s=n(750);function l(e){return function(e){if(Array.isArray(e))return o(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return o(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?o(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function a(e){var t=S(e.lines),n=t.oldLines,r=t.newLines;void 0!==n?e.oldLines=n:delete e.oldLines,void 0!==r?e.newLines=r:delete e.newLines}function c(e,t){if("string"==typeof e){if(/^@@/m.test(e)||/^Index:/m.test(e))return(0,i.parsePatch)(e)[0];if(!t)throw new Error("Must provide a base reference or pass in a patch");return(0,r.structuredPatch)(void 0,void 0,t,e)}return e}function u(e){return e.newFileName&&e.newFileName!==e.oldFileName}function f(e,t,n){return t===n?t:(e.conflict=!0,{mine:t,theirs:n})}function d(e,t){return e.oldStart<t.oldStart&&e.oldStart+e.oldLines<t.oldStart}function h(e,t){return{oldStart:e.oldStart,oldLines:e.oldLines,newStart:e.newStart+t,newLines:e.newLines,lines:e.lines}}function p(e,t,n,r,i){var s={offset:t,lines:n,index:0},o={offset:r,lines:i,index:0};for(v(e,s,o),v(e,o,s);s.index<s.lines.length&&o.index<o.lines.length;){var c=s.lines[s.index],u=o.lines[o.index];if("-"!==c[0]&&"+"!==c[0]||"-"!==u[0]&&"+"!==u[0])if("+"===c[0]&&" "===u[0]){var f;(f=e.lines).push.apply(f,l(w(s)))}else if("+"===u[0]&&" "===c[0]){var d;(d=e.lines).push.apply(d,l(w(o)))}else"-"===c[0]&&" "===u[0]?m(e,s,o):"-"===u[0]&&" "===c[0]?m(e,o,s,!0):c===u?(e.lines.push(c),s.index++,o.index++):b(e,w(s),w(o));else g(e,s,o)}y(e,s),y(e,o),a(e)}function g(e,t,n){var r=w(t),i=w(n);if(j(r)&&j(i)){var o,a;if((0,s.arrayStartsWith)(r,i)&&x(n,r,r.length-i.length))return void(o=e.lines).push.apply(o,l(r));if((0,s.arrayStartsWith)(i,r)&&x(t,i,i.length-r.length))return void(a=e.lines).push.apply(a,l(i))}else if((0,s.arrayEqual)(r,i)){var c;return void(c=e.lines).push.apply(c,l(r))}b(e,r,i)}function m(e,t,n,r){var i,s=w(t),o=function(e,t){for(var n=[],r=[],i=0,s=!1,l=!1;i<t.length&&e.index<e.lines.length;){var o=e.lines[e.index],a=t[i];if("+"===a[0])break;if(s=s||" "!==o[0],r.push(a),i++,"+"===o[0])for(l=!0;"+"===o[0];)n.push(o),o=e.lines[++e.index];a.substr(1)===o.substr(1)?(n.push(o),e.index++):l=!0}if("+"===(t[i]||"")[0]&&s&&(l=!0),l)return n;for(;i<t.length;)r.push(t[i++]);return{merged:r,changes:n}}(n,s);o.merged?(i=e.lines).push.apply(i,l(o.merged)):b(e,r?o:s,r?s:o)}function b(e,t,n){e.conflict=!0,e.lines.push({conflict:!0,mine:t,theirs:n})}function v(e,t,n){for(;t.offset<n.offset&&t.index<t.lines.length;){var r=t.lines[t.index++];e.lines.push(r),t.offset++}}function y(e,t){for(;t.index<t.lines.length;){var n=t.lines[t.index++];e.lines.push(n)}}function w(e){for(var t=[],n=e.lines[e.index][0];e.index<e.lines.length;){var r=e.lines[e.index];if("-"===n&&"+"===r[0]&&(n="+"),n!==r[0])break;t.push(r),e.index++}return t}function j(e){return e.reduce((function(e,t){return e&&"-"===t[0]}),!0)}function x(e,t,n){for(var r=0;r<n;r++){var i=t[t.length-n+r].substr(1);if(e.lines[e.index+r]!==" "+i)return!1}return e.index+=n,!0}function S(e){var t=0,n=0;return e.forEach((function(e){if("string"!=typeof e){var r=S(e.mine),i=S(e.theirs);void 0!==t&&(r.oldLines===i.oldLines?t+=r.oldLines:t=void 0),void 0!==n&&(r.newLines===i.newLines?n+=r.newLines:n=void 0)}else void 0===n||"+"!==e[0]&&" "!==e[0]||n++,void 0===t||"-"!==e[0]&&" "!==e[0]||t++})),{oldLines:t,newLines:n}}},20:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.parsePatch=function(e){var t=e.split(/\n/),n=[],r=0;function i(){var e={};for(n.push(e);r<t.length;){var i=t[r];if(/^(\-\-\-|\+\+\+|@@)\s/.test(i))break;var o=/^(?:Index:|diff(?: -r \w+)+)\s+(.+?)\s*$/.exec(i);o&&(e.index=o[1]),r++}for(s(e),s(e),e.hunks=[];r<t.length;){var a=t[r];if(/^(Index:\s|diff\s|\-\-\-\s|\+\+\+\s|===================================================================)/.test(a))break;if(/^@@/.test(a))e.hunks.push(l());else{if(a)throw new Error("Unknown line "+(r+1)+" "+JSON.stringify(a));r++}}}function s(e){var n=/^(---|\+\+\+)\s+(.*)\r?$/.exec(t[r]);if(n){var i="---"===n[1]?"old":"new",s=n[2].split("\t",2),l=s[0].replace(/\\\\/g,"\\");/^".*"$/.test(l)&&(l=l.substr(1,l.length-2)),e[i+"FileName"]=l,e[i+"Header"]=(s[1]||"").trim(),r++}}function l(){var e=r,n=t[r++].split(/@@ -(\d+)(?:,(\d+))? \+(\d+)(?:,(\d+))? @@/),i={oldStart:+n[1],oldLines:void 0===n[2]?1:+n[2],newStart:+n[3],newLines:void 0===n[4]?1:+n[4],lines:[]};0===i.oldLines&&(i.oldStart+=1),0===i.newLines&&(i.newStart+=1);for(var s=0,l=0;r<t.length&&(l<i.oldLines||s<i.newLines||null!==(o=t[r])&&void 0!==o&&o.startsWith("\\"));r++){var o,a=0==t[r].length&&r!=t.length-1?" ":t[r][0];if("+"!==a&&"-"!==a&&" "!==a&&"\\"!==a)throw new Error("Hunk at line ".concat(e+1," contained invalid line ").concat(t[r]));i.lines.push(t[r]),"+"===a?s++:"-"===a?l++:" "===a&&(s++,l++)}if(s||1!==i.newLines||(i.newLines=0),l||1!==i.oldLines||(i.oldLines=0),s!==i.newLines)throw new Error("Added line count did not match for hunk at line "+(e+1));if(l!==i.oldLines)throw new Error("Removed line count did not match for hunk at line "+(e+1));return i}for(;r<t.length;)i();return n}},769:(e,t)=>{"use strict";function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function r(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function i(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?r(Object(n),!0).forEach((function(t){s(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):r(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function s(e,t,r){var i;return i=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(t),(t="symbol"==n(i)?i:i+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}Object.defineProperty(t,"__esModule",{value:!0}),t.reversePatch=function e(t){return Array.isArray(t)?t.map(e).reverse():i(i({},t),{},{oldFileName:t.newFileName,oldHeader:t.newHeader,newFileName:t.oldFileName,newHeader:t.oldHeader,hunks:t.hunks.map((function(e){return{oldLines:e.newLines,oldStart:e.newStart,newLines:e.oldLines,newStart:e.oldStart,lines:e.lines.map((function(e){return e.startsWith("-")?"+".concat(e.slice(1)):e.startsWith("+")?"-".concat(e.slice(1)):e}))}}))})}},750:(e,t)=>{"use strict";function n(e,t){if(t.length>e.length)return!1;for(var n=0;n<t.length;n++)if(t[n]!==e[n])return!1;return!0}Object.defineProperty(t,"__esModule",{value:!0}),t.arrayEqual=function(e,t){return e.length===t.length&&n(e,t)},t.arrayStartsWith=n},439:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,n){var r=!0,i=!1,s=!1,l=1;return function o(){if(r&&!s){if(i?l++:r=!1,e+l<=n)return e+l;s=!0}if(!i)return s||(r=!0),t<=e-l?e-l++:(i=!0,o())}}},65:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.generateOptions=function(e,t){if("function"==typeof e)t.callback=e;else if(e)for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t}},666:(e,t)=>{"use strict";function n(e,t,n){if(e.slice(0,t.length)!=t)throw Error("string ".concat(JSON.stringify(e)," doesn't start with prefix ").concat(JSON.stringify(t),"; this is a bug"));return n+e.slice(t.length)}function r(e,t,n){if(!t)return e+n;if(e.slice(-t.length)!=t)throw Error("string ".concat(JSON.stringify(e)," doesn't end with suffix ").concat(JSON.stringify(t),"; this is a bug"));return e.slice(0,-t.length)+n}Object.defineProperty(t,"__esModule",{value:!0}),t.hasOnlyUnixLineEndings=function(e){return!e.includes("\r\n")&&e.includes("\n")},t.hasOnlyWinLineEndings=function(e){return e.includes("\r\n")&&!e.startsWith("\n")&&!e.match(/[^\r]\n/)},t.longestCommonPrefix=function(e,t){var n;for(n=0;n<e.length&&n<t.length;n++)if(e[n]!=t[n])return e.slice(0,n);return e.slice(0,n)},t.longestCommonSuffix=function(e,t){var n;if(!e||!t||e[e.length-1]!=t[t.length-1])return"";for(n=0;n<e.length&&n<t.length;n++)if(e[e.length-(n+1)]!=t[t.length-(n+1)])return e.slice(-n);return e.slice(-n)},t.maximumOverlap=function(e,t){return t.slice(0,function(e,t){var n=0;e.length>t.length&&(n=e.length-t.length);var r=t.length;e.length<t.length&&(r=e.length);var i=Array(r),s=0;i[0]=0;for(var l=1;l<r;l++){for(t[l]==t[s]?i[l]=i[s]:i[l]=s;s>0&&t[l]!=t[s];)s=i[s];t[l]==t[s]&&s++}s=0;for(var o=n;o<e.length;o++){for(;s>0&&e[o]!=t[s];)s=i[s];e[o]==t[s]&&s++}return s}(e,t))},t.removePrefix=function(e,t){return n(e,t,"")},t.removeSuffix=function(e,t){return r(e,t,"")},t.replacePrefix=n,t.replaceSuffix=r},110:(e,t)=>{!function(e){var t=/\S/,n=/\"/g,r=/\n/g,i=/\r/g,s=/\\/g,l=/\u2028/,o=/\u2029/;function a(e){return e.trim?e.trim():e.replace(/^\s*|\s*$/g,"")}function c(e,t,n){if(t.charAt(n)!=e.charAt(0))return!1;for(var r=1,i=e.length;r<i;r++)if(t.charAt(n+r)!=e.charAt(r))return!1;return!0}e.tags={"#":1,"^":2,"<":3,$:4,"/":5,"!":6,">":7,"=":8,_v:9,"{":10,"&":11,_t:12},e.scan=function(n,r){var i,s=n.length,l=0,o=null,u=null,f="",d=[],h=!1,p=0,g=0,m="{{",b="}}";function v(){f.length>0&&(d.push({tag:"_t",text:new String(f)}),f="")}function y(n,r){if(v(),n&&function(){for(var n=!0,r=g;r<d.length;r++)if(!(n=e.tags[d[r].tag]<e.tags._v||"_t"==d[r].tag&&null===d[r].text.match(t)))return!1;return n}())for(var i,s=g;s<d.length;s++)d[s].text&&((i=d[s+1])&&">"==i.tag&&(i.indent=d[s].text.toString()),d.splice(s,1));else r||d.push({tag:"\n"});h=!1,g=d.length}function w(e,t){var n="="+b,r=e.indexOf(n,t),i=a(e.substring(e.indexOf("=",t)+1,r)).split(" ");return m=i[0],b=i[i.length-1],r+n.length-1}for(r&&(r=r.split(" "),m=r[0],b=r[1]),p=0;p<s;p++)0==l?c(m,n,p)?(--p,v(),l=1):"\n"==n.charAt(p)?y(h):f+=n.charAt(p):1==l?(p+=m.length-1,"="==(o=(u=e.tags[n.charAt(p+1)])?n.charAt(p+1):"_v")?(p=w(n,p),l=0):(u&&p++,l=2),h=p):c(b,n,p)?(d.push({tag:o,n:a(f),otag:m,ctag:b,i:"/"==o?h-m.length:p+b.length}),f="",p+=b.length-1,l=0,"{"==o&&("}}"==b?p++:"}"===(i=d[d.length-1]).n.substr(i.n.length-1)&&(i.n=i.n.substring(0,i.n.length-1)))):f+=n.charAt(p);return y(h,!0),d};var u={_t:!0,"\n":!0,$:!0,"/":!0};function f(t,n,r,i){var s,l=[],o=null,a=null;for(s=r[r.length-1];t.length>0;){if(a=t.shift(),s&&"<"==s.tag&&!(a.tag in u))throw new Error("Illegal content in < super tag.");if(e.tags[a.tag]<=e.tags.$||d(a,i))r.push(a),a.nodes=f(t,a.tag,r,i);else{if("/"==a.tag){if(0===r.length)throw new Error("Closing tag without opener: /"+a.n);if(o=r.pop(),a.n!=o.n&&!h(a.n,o.n,i))throw new Error("Nesting error: "+o.n+" vs. "+a.n);return o.end=a.i,l}"\n"==a.tag&&(a.last=0==t.length||"\n"==t[0].tag)}l.push(a)}if(r.length>0)throw new Error("missing closing tag: "+r.pop().n);return l}function d(e,t){for(var n=0,r=t.length;n<r;n++)if(t[n].o==e.n)return e.tag="#",!0}function h(e,t,n){for(var r=0,i=n.length;r<i;r++)if(n[r].c==e&&n[r].o==t)return!0}function p(e){var t=[];for(var n in e.partials)t.push('"'+m(n)+'":{name:"'+m(e.partials[n].name)+'", '+p(e.partials[n])+"}");return"partials: {"+t.join(",")+"}, subs: "+function(e){var t=[];for(var n in e)t.push('"'+m(n)+'": function(c,p,t,i) {'+e[n]+"}");return"{ "+t.join(",")+" }"}(e.subs)}e.stringify=function(t,n,r){return"{code: function (c,p,i) { "+e.wrapMain(t.code)+" },"+p(t)+"}"};var g=0;function m(e){return e.replace(s,"\\\\").replace(n,'\\"').replace(r,"\\n").replace(i,"\\r").replace(l,"\\u2028").replace(o,"\\u2029")}function b(e){return~e.indexOf(".")?"d":"f"}function v(e,t){var n="<"+(t.prefix||"")+e.n+g++;return t.partials[n]={name:e.n,partials:{}},t.code+='t.b(t.rp("'+m(n)+'",c,p,"'+(e.indent||"")+'"));',n}function y(e,t){t.code+="t.b(t.t(t."+b(e.n)+'("'+m(e.n)+'",c,p,0)));'}function w(e){return"t.b("+e+");"}e.generate=function(t,n,r){g=0;var i={code:"",subs:{},partials:{}};return e.walk(t,i),r.asString?this.stringify(i,n,r):this.makeTemplate(i,n,r)},e.wrapMain=function(e){return'var t=this;t.b(i=i||"");'+e+"return t.fl();"},e.template=e.Template,e.makeTemplate=function(e,t,n){var r=this.makePartials(e);return r.code=new Function("c","p","i",this.wrapMain(e.code)),new this.template(r,t,this,n)},e.makePartials=function(e){var t,n={subs:{},partials:e.partials,name:e.name};for(t in n.partials)n.partials[t]=this.makePartials(n.partials[t]);for(t in e.subs)n.subs[t]=new Function("c","p","t","i",e.subs[t]);return n},e.codegen={"#":function(t,n){n.code+="if(t.s(t."+b(t.n)+'("'+m(t.n)+'",c,p,1),c,p,0,'+t.i+","+t.end+',"'+t.otag+" "+t.ctag+'")){t.rs(c,p,function(c,p,t){',e.walk(t.nodes,n),n.code+="});c.pop();}"},"^":function(t,n){n.code+="if(!t.s(t."+b(t.n)+'("'+m(t.n)+'",c,p,1),c,p,1,0,0,"")){',e.walk(t.nodes,n),n.code+="};"},">":v,"<":function(t,n){var r={partials:{},code:"",subs:{},inPartial:!0};e.walk(t.nodes,r);var i=n.partials[v(t,n)];i.subs=r.subs,i.partials=r.partials},$:function(t,n){var r={subs:{},code:"",partials:n.partials,prefix:t.n};e.walk(t.nodes,r),n.subs[t.n]=r.code,n.inPartial||(n.code+='t.sub("'+m(t.n)+'",c,p,i);')},"\n":function(e,t){t.code+=w('"\\n"'+(e.last?"":" + i"))},_v:function(e,t){t.code+="t.b(t.v(t."+b(e.n)+'("'+m(e.n)+'",c,p,0)));'},_t:function(e,t){t.code+=w('"'+m(e.text)+'"')},"{":y,"&":y},e.walk=function(t,n){for(var r,i=0,s=t.length;i<s;i++)(r=e.codegen[t[i].tag])&&r(t[i],n);return n},e.parse=function(e,t,n){return f(e,0,[],(n=n||{}).sectionTags||[])},e.cache={},e.cacheKey=function(e,t){return[e,!!t.asString,!!t.disableLambda,t.delimiters,!!t.modelGet].join("||")},e.compile=function(t,n){n=n||{};var r=e.cacheKey(t,n),i=this.cache[r];if(i){var s=i.partials;for(var l in s)delete s[l].instance;return i}return i=this.generate(this.parse(this.scan(t,n.delimiters),t,n),t,n),this.cache[r]=i}}(t)},714:(e,t,n)=>{var r=n(110);r.Template=n(549).Template,r.template=r.Template,e.exports=r},549:(e,t)=>{!function(e){function t(e,t,n){var r;return t&&"object"==typeof t&&(void 0!==t[e]?r=t[e]:n&&t.get&&"function"==typeof t.get&&(r=t.get(e))),r}e.Template=function(e,t,n,r){e=e||{},this.r=e.code||this.r,this.c=n,this.options=r||{},this.text=t||"",this.partials=e.partials||{},this.subs=e.subs||{},this.buf=""},e.Template.prototype={r:function(e,t,n){return""},v:function(e){return e=a(e),o.test(e)?e.replace(n,"&amp;").replace(r,"&lt;").replace(i,"&gt;").replace(s,"&#39;").replace(l,"&quot;"):e},t:a,render:function(e,t,n){return this.ri([e],t||{},n)},ri:function(e,t,n){return this.r(e,t,n)},ep:function(e,t){var n=this.partials[e],r=t[n.name];if(n.instance&&n.base==r)return n.instance;if("string"==typeof r){if(!this.c)throw new Error("No compiler available.");r=this.c.compile(r,this.options)}if(!r)return null;if(this.partials[e].base=r,n.subs){for(key in t.stackText||(t.stackText={}),n.subs)t.stackText[key]||(t.stackText[key]=void 0!==this.activeSub&&t.stackText[this.activeSub]?t.stackText[this.activeSub]:this.text);r=function(e,t,n,r,i,s){function l(){}function o(){}var a;l.prototype=e,o.prototype=e.subs;var c=new l;for(a in c.subs=new o,c.subsText={},c.buf="",r=r||{},c.stackSubs=r,c.subsText=s,t)r[a]||(r[a]=t[a]);for(a in r)c.subs[a]=r[a];for(a in i=i||{},c.stackPartials=i,n)i[a]||(i[a]=n[a]);for(a in i)c.partials[a]=i[a];return c}(r,n.subs,n.partials,this.stackSubs,this.stackPartials,t.stackText)}return this.partials[e].instance=r,r},rp:function(e,t,n,r){var i=this.ep(e,n);return i?i.ri(t,n,r):""},rs:function(e,t,n){var r=e[e.length-1];if(c(r))for(var i=0;i<r.length;i++)e.push(r[i]),n(e,t,this),e.pop();else n(e,t,this)},s:function(e,t,n,r,i,s,l){var o;return(!c(e)||0!==e.length)&&("function"==typeof e&&(e=this.ms(e,t,n,r,i,s,l)),o=!!e,!r&&o&&t&&t.push("object"==typeof e?e:t[t.length-1]),o)},d:function(e,n,r,i){var s,l=e.split("."),o=this.f(l[0],n,r,i),a=this.options.modelGet,u=null;if("."===e&&c(n[n.length-2]))o=n[n.length-1];else for(var f=1;f<l.length;f++)void 0!==(s=t(l[f],o,a))?(u=o,o=s):o="";return!(i&&!o)&&(i||"function"!=typeof o||(n.push(u),o=this.mv(o,n,r),n.pop()),o)},f:function(e,n,r,i){for(var s=!1,l=!1,o=this.options.modelGet,a=n.length-1;a>=0;a--)if(void 0!==(s=t(e,n[a],o))){l=!0;break}return l?(i||"function"!=typeof s||(s=this.mv(s,n,r)),s):!i&&""},ls:function(e,t,n,r,i){var s=this.options.delimiters;return this.options.delimiters=i,this.b(this.ct(a(e.call(t,r)),t,n)),this.options.delimiters=s,!1},ct:function(e,t,n){if(this.options.disableLambda)throw new Error("Lambda features disabled.");return this.c.compile(e,this.options).render(t,n)},b:function(e){this.buf+=e},fl:function(){var e=this.buf;return this.buf="",e},ms:function(e,t,n,r,i,s,l){var o,a=t[t.length-1],c=e.call(a);return"function"==typeof c?!!r||(o=this.activeSub&&this.subsText&&this.subsText[this.activeSub]?this.subsText[this.activeSub]:this.text,this.ls(c,a,n,o.substring(i,s),l)):c},mv:function(e,t,n){var r=t[t.length-1],i=e.call(r);return"function"==typeof i?this.ct(a(i.call(r)),r,n):i},sub:function(e,t,n,r){var i=this.subs[e];i&&(this.activeSub=e,i(t,n,this,r),this.activeSub=!1)}};var n=/&/g,r=/</g,i=/>/g,s=/\'/g,l=/\"/g,o=/[&<>\"\']/;function a(e){return String(null==e?"":e)}var c=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)}}(t)},957:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.parse=function(e,t={}){const n=[];let i=null,o=null,c=null,u=null,f=null,d=null,h=null;const p="--- ",g="+++ ",m="@@",b=/^old mode (\d{6})/,v=/^new mode (\d{6})/,y=/^deleted file mode (\d{6})/,w=/^new file mode (\d{6})/,j=/^copy from "?(.+)"?/,x=/^copy to "?(.+)"?/,S=/^rename from "?(.+)"?/,O=/^rename to "?(.+)"?/,L=/^similarity index (\d+)%/,C=/^dissimilarity index (\d+)%/,T=/^index ([\da-z]+)\.\.([\da-z]+)\s*(\d{6})?/,P=/^Binary files (.*) and (.*) differ/,k=/^GIT binary patch/,N=/^index ([\da-z]+),([\da-z]+)\.\.([\da-z]+)/,_=/^mode (\d{6}),(\d{6})\.\.(\d{6})/,E=/^new file mode (\d{6})/,M=/^deleted file mode (\d{6}),(\d{6})/,H=e.replace(/\\ No newline at end of file/g,"").replace(/\r\n?/g,"\n").split("\n");function D(){null!==o&&null!==i&&(i.blocks.push(o),o=null)}function F(){null!==i&&(i.oldName||null===d||(i.oldName=d),i.newName||null===h||(i.newName=h),i.newName&&(n.push(i),i=null)),d=null,h=null}function I(){D(),F(),i={blocks:[],deletedLines:0,addedLines:0}}function A(e){let t;D(),null!==i&&((t=/^@@ -(\d+)(?:,\d+)? \+(\d+)(?:,\d+)? @@.*/.exec(e))?(i.isCombined=!1,c=parseInt(t[1],10),f=parseInt(t[2],10)):(t=/^@@@ -(\d+)(?:,\d+)? -(\d+)(?:,\d+)? \+(\d+)(?:,\d+)? @@@.*/.exec(e))?(i.isCombined=!0,c=parseInt(t[1],10),u=parseInt(t[2],10),f=parseInt(t[3],10)):(e.startsWith(m)&&console.error("Failed to parse lines, starting in 0!"),c=0,f=0,i.isCombined=!1)),o={lines:[],oldStartLine:c,oldStartLine2:u,newStartLine:f,header:e}}return H.forEach(((e,u)=>{if(!e||e.startsWith("*"))return;let D;const F=H[u-1],W=H[u+1],R=H[u+2];if(e.startsWith("diff --git")||e.startsWith("diff --combined")){if(I(),(D=/^diff --git "?([a-ciow]\/.+)"? "?([a-ciow]\/.+)"?/.exec(e))&&(d=a(D[1],void 0,t.dstPrefix),h=a(D[2],void 0,t.srcPrefix)),null===i)throw new Error("Where is my file !!!");return void(i.isGitDiff=!0)}if(e.startsWith("Binary files")&&!(null==i?void 0:i.isGitDiff)){if(I(),(D=/^Binary files "?([a-ciow]\/.+)"? and "?([a-ciow]\/.+)"? differ/.exec(e))&&(d=a(D[1],void 0,t.dstPrefix),h=a(D[2],void 0,t.srcPrefix)),null===i)throw new Error("Where is my file !!!");return void(i.isBinary=!0)}if((!i||!i.isGitDiff&&i&&e.startsWith(p)&&W.startsWith(g)&&R.startsWith(m))&&I(),null==i?void 0:i.isTooBig)return;if(i&&("number"==typeof t.diffMaxChanges&&i.addedLines+i.deletedLines>t.diffMaxChanges||"number"==typeof t.diffMaxLineLength&&e.length>t.diffMaxLineLength))return i.isTooBig=!0,i.addedLines=0,i.deletedLines=0,i.blocks=[],o=null,void A("function"==typeof t.diffTooBigMessage?t.diffTooBigMessage(n.length):"Diff too big to be displayed");if(e.startsWith(p)&&W.startsWith(g)||e.startsWith(g)&&F.startsWith(p)){if(i&&!i.oldName&&e.startsWith("--- ")&&(D=function(e,t){return a(e,"---",t)}(e,t.srcPrefix)))return i.oldName=D,void(i.language=s(i.oldName,i.language));if(i&&!i.newName&&e.startsWith("+++ ")&&(D=function(e,t){return a(e,"+++",t)}(e,t.dstPrefix)))return i.newName=D,void(i.language=s(i.newName,i.language))}if(i&&(e.startsWith(m)||i.isGitDiff&&i.oldName&&i.newName&&!o))return void A(e);if(o&&(e.startsWith("+")||e.startsWith("-")||e.startsWith(" ")))return void function(e){if(null===i||null===o||null===c||null===f)return;const t={content:e},n=i.isCombined?["+ "," +","++"]:["+"],s=i.isCombined?["- "," -","--"]:["-"];l(e,n)?(i.addedLines++,t.type=r.LineType.INSERT,t.oldNumber=void 0,t.newNumber=f++):l(e,s)?(i.deletedLines++,t.type=r.LineType.DELETE,t.oldNumber=c++,t.newNumber=void 0):(t.type=r.LineType.CONTEXT,t.oldNumber=c++,t.newNumber=f++),o.lines.push(t)}(e);const q=!function(e,t){let n=t;for(;n<H.length-3;){if(e.startsWith("diff"))return!1;if(H[n].startsWith(p)&&H[n+1].startsWith(g)&&H[n+2].startsWith(m))return!0;n++}return!1}(e,u);if(null===i)throw new Error("Where is my file !!!");(D=b.exec(e))?i.oldMode=D[1]:(D=v.exec(e))?i.newMode=D[1]:(D=y.exec(e))?(i.deletedFileMode=D[1],i.isDeleted=!0):(D=w.exec(e))?(i.newFileMode=D[1],i.isNew=!0):(D=j.exec(e))?(q&&(i.oldName=D[1]),i.isCopy=!0):(D=x.exec(e))?(q&&(i.newName=D[1]),i.isCopy=!0):(D=S.exec(e))?(q&&(i.oldName=D[1]),i.isRename=!0):(D=O.exec(e))?(q&&(i.newName=D[1]),i.isRename=!0):(D=P.exec(e))?(i.isBinary=!0,i.oldName=a(D[1],void 0,t.srcPrefix),i.newName=a(D[2],void 0,t.dstPrefix),A("Binary file")):k.test(e)?(i.isBinary=!0,A(e)):(D=L.exec(e))?i.unchangedPercentage=parseInt(D[1],10):(D=C.exec(e))?i.changedPercentage=parseInt(D[1],10):(D=T.exec(e))?(i.checksumBefore=D[1],i.checksumAfter=D[2],D[3]&&(i.mode=D[3])):(D=N.exec(e))?(i.checksumBefore=[D[2],D[3]],i.checksumAfter=D[1]):(D=_.exec(e))?(i.oldMode=[D[2],D[3]],i.newMode=D[1]):(D=E.exec(e))?(i.newFileMode=D[1],i.isNew=!0):(D=M.exec(e))&&(i.deletedFileMode=D[1],i.isDeleted=!0)})),D(),F(),n};const r=n(613),i=n(185);function s(e,t){const n=e.split(".");return n.length>1?n[n.length-1]:t}function l(e,t){return t.reduce(((t,n)=>t||e.startsWith(n)),!1)}const o=["a/","b/","i/","w/","c/","o/"];function a(e,t,n){const r=void 0!==n?[...o,n]:o,s=t?new RegExp(`^${(0,i.escapeForRegExp)(t)} "?(.+?)"?$`):new RegExp('^"?(.+?)"?$'),[,l=""]=s.exec(e)||[],a=r.find((e=>0===l.indexOf(e)));return(a?l.slice(a.length):l).replace(/\s+\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}(?:\.\d+)? [+-]\d{4}.*$/,"")}},488:function(e,t,n){"use strict";var r,i=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),s=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),l=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),l=0;l<n.length;l++)"default"!==n[l]&&i(t,e,n[l]);return s(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.defaultTemplates=void 0;const o=l(n(714));t.defaultTemplates={},t.defaultTemplates["file-summary-line"]=new o.Template({code:function(e,t,n){var r=this;return r.b(n=n||""),r.b('<li class="d2h-file-list-line">'),r.b("\n"+n),r.b('    <span class="d2h-file-name-wrapper">'),r.b("\n"+n),r.b(r.rp("<fileIcon0",e,t,"      ")),r.b('      <a href="#'),r.b(r.v(r.f("fileHtmlId",e,t,0))),r.b('" class="d2h-file-name">'),r.b(r.v(r.f("fileName",e,t,0))),r.b("</a>"),r.b("\n"+n),r.b('      <span class="d2h-file-stats">'),r.b("\n"+n),r.b('          <span class="d2h-lines-added">'),r.b(r.v(r.f("addedLines",e,t,0))),r.b("</span>"),r.b("\n"+n),r.b('          <span class="d2h-lines-deleted">'),r.b(r.v(r.f("deletedLines",e,t,0))),r.b("</span>"),r.b("\n"+n),r.b("      </span>"),r.b("\n"+n),r.b("    </span>"),r.b("\n"+n),r.b("</li>"),r.fl()},partials:{"<fileIcon0":{name:"fileIcon",partials:{},subs:{}}},subs:{}}),t.defaultTemplates["file-summary-wrapper"]=new o.Template({code:function(e,t,n){var r=this;return r.b(n=n||""),r.b('<div class="d2h-file-list-wrapper '),r.b(r.v(r.f("colorScheme",e,t,0))),r.b('">'),r.b("\n"+n),r.b('    <div class="d2h-file-list-header">'),r.b("\n"+n),r.b('        <span class="d2h-file-list-title">Files changed ('),r.b(r.v(r.f("filesNumber",e,t,0))),r.b(")</span>"),r.b("\n"+n),r.b('        <a class="d2h-file-switch d2h-hide">hide</a>'),r.b("\n"+n),r.b('        <a class="d2h-file-switch d2h-show">show</a>'),r.b("\n"+n),r.b("    </div>"),r.b("\n"+n),r.b('    <ol class="d2h-file-list">'),r.b("\n"+n),r.b("    "),r.b(r.t(r.f("files",e,t,0))),r.b("\n"+n),r.b("    </ol>"),r.b("\n"+n),r.b("</div>"),r.fl()},partials:{},subs:{}}),t.defaultTemplates["generic-block-header"]=new o.Template({code:function(e,t,n){var r=this;return r.b(n=n||""),r.b("<tr>"),r.b("\n"+n),r.b('    <td class="'),r.b(r.v(r.f("lineClass",e,t,0))),r.b(" "),r.b(r.v(r.d("CSSLineClass.INFO",e,t,0))),r.b('"></td>'),r.b("\n"+n),r.b('    <td class="'),r.b(r.v(r.d("CSSLineClass.INFO",e,t,0))),r.b('">'),r.b("\n"+n),r.b('        <div class="'),r.b(r.v(r.f("contentClass",e,t,0))),r.b('">'),r.s(r.f("blockHeader",e,t,1),e,t,0,156,173,"{{ }}")&&(r.rs(e,t,(function(e,t,n){n.b(n.t(n.f("blockHeader",e,t,0)))})),e.pop()),r.s(r.f("blockHeader",e,t,1),e,t,1,0,0,"")||r.b("&nbsp;"),r.b("</div>"),r.b("\n"+n),r.b("    </td>"),r.b("\n"+n),r.b("</tr>"),r.fl()},partials:{},subs:{}}),t.defaultTemplates["generic-empty-diff"]=new o.Template({code:function(e,t,n){var r=this;return r.b(n=n||""),r.b("<tr>"),r.b("\n"+n),r.b('    <td class="'),r.b(r.v(r.d("CSSLineClass.INFO",e,t,0))),r.b('">'),r.b("\n"+n),r.b('        <div class="'),r.b(r.v(r.f("contentClass",e,t,0))),r.b('">'),r.b("\n"+n),r.b("            File without changes"),r.b("\n"+n),r.b("        </div>"),r.b("\n"+n),r.b("    </td>"),r.b("\n"+n),r.b("</tr>"),r.fl()},partials:{},subs:{}}),t.defaultTemplates["generic-file-path"]=new o.Template({code:function(e,t,n){var r=this;return r.b(n=n||""),r.b('<span class="d2h-file-name-wrapper">'),r.b("\n"+n),r.b(r.rp("<fileIcon0",e,t,"    ")),r.b('    <span class="d2h-file-name">'),r.b(r.v(r.f("fileDiffName",e,t,0))),r.b("</span>"),r.b("\n"+n),r.b(r.rp("<fileTag1",e,t,"    ")),r.b("</span>"),r.b("\n"+n),r.b('<label class="d2h-file-collapse">'),r.b("\n"+n),r.b('    <input class="d2h-file-collapse-input" type="checkbox" name="viewed" value="viewed">'),r.b("\n"+n),r.b("    Viewed"),r.b("\n"+n),r.b("</label>"),r.fl()},partials:{"<fileIcon0":{name:"fileIcon",partials:{},subs:{}},"<fileTag1":{name:"fileTag",partials:{},subs:{}}},subs:{}}),t.defaultTemplates["generic-line"]=new o.Template({code:function(e,t,n){var r=this;return r.b(n=n||""),r.b("<tr>"),r.b("\n"+n),r.b('    <td class="'),r.b(r.v(r.f("lineClass",e,t,0))),r.b(" "),r.b(r.v(r.f("type",e,t,0))),r.b('">'),r.b("\n"+n),r.b("      "),r.b(r.t(r.f("lineNumber",e,t,0))),r.b("\n"+n),r.b("    </td>"),r.b("\n"+n),r.b('    <td class="'),r.b(r.v(r.f("type",e,t,0))),r.b('">'),r.b("\n"+n),r.b('        <div class="'),r.b(r.v(r.f("contentClass",e,t,0))),r.b('">'),r.b("\n"+n),r.s(r.f("prefix",e,t,1),e,t,0,162,238,"{{ }}")&&(r.rs(e,t,(function(e,t,r){r.b('            <span class="d2h-code-line-prefix">'),r.b(r.t(r.f("prefix",e,t,0))),r.b("</span>"),r.b("\n"+n)})),e.pop()),r.s(r.f("prefix",e,t,1),e,t,1,0,0,"")||(r.b('            <span class="d2h-code-line-prefix">&nbsp;</span>'),r.b("\n"+n)),r.s(r.f("content",e,t,1),e,t,0,371,445,"{{ }}")&&(r.rs(e,t,(function(e,t,r){r.b('            <span class="d2h-code-line-ctn">'),r.b(r.t(r.f("content",e,t,0))),r.b("</span>"),r.b("\n"+n)})),e.pop()),r.s(r.f("content",e,t,1),e,t,1,0,0,"")||(r.b('            <span class="d2h-code-line-ctn"><br></span>'),r.b("\n"+n)),r.b("        </div>"),r.b("\n"+n),r.b("    </td>"),r.b("\n"+n),r.b("</tr>"),r.fl()},partials:{},subs:{}}),t.defaultTemplates["generic-wrapper"]=new o.Template({code:function(e,t,n){var r=this;return r.b(n=n||""),r.b('<div class="d2h-wrapper '),r.b(r.v(r.f("colorScheme",e,t,0))),r.b('">'),r.b("\n"+n),r.b("    "),r.b(r.t(r.f("content",e,t,0))),r.b("\n"+n),r.b("</div>"),r.fl()},partials:{},subs:{}}),t.defaultTemplates["icon-file-added"]=new o.Template({code:function(e,t,n){var r=this;return r.b(n=n||""),r.b('<svg aria-hidden="true" class="d2h-icon d2h-added" height="16" title="added" version="1.1" viewBox="0 0 14 16"'),r.b("\n"+n),r.b('     width="14">'),r.b("\n"+n),r.b('    <path d="M13 1H1C0.45 1 0 1.45 0 2v12c0 0.55 0.45 1 1 1h12c0.55 0 1-0.45 1-1V2c0-0.55-0.45-1-1-1z m0 13H1V2h12v12zM6 9H3V7h3V4h2v3h3v2H8v3H6V9z"></path>'),r.b("\n"+n),r.b("</svg>"),r.fl()},partials:{},subs:{}}),t.defaultTemplates["icon-file-changed"]=new o.Template({code:function(e,t,n){var r=this;return r.b(n=n||""),r.b('<svg aria-hidden="true" class="d2h-icon d2h-changed" height="16" title="modified" version="1.1"'),r.b("\n"+n),r.b('     viewBox="0 0 14 16" width="14">'),r.b("\n"+n),r.b('    <path d="M13 1H1C0.45 1 0 1.45 0 2v12c0 0.55 0.45 1 1 1h12c0.55 0 1-0.45 1-1V2c0-0.55-0.45-1-1-1z m0 13H1V2h12v12zM4 8c0-1.66 1.34-3 3-3s3 1.34 3 3-1.34 3-3 3-3-1.34-3-3z"></path>'),r.b("\n"+n),r.b("</svg>"),r.fl()},partials:{},subs:{}}),t.defaultTemplates["icon-file-deleted"]=new o.Template({code:function(e,t,n){var r=this;return r.b(n=n||""),r.b('<svg aria-hidden="true" class="d2h-icon d2h-deleted" height="16" title="removed" version="1.1"'),r.b("\n"+n),r.b('     viewBox="0 0 14 16" width="14">'),r.b("\n"+n),r.b('    <path d="M13 1H1C0.45 1 0 1.45 0 2v12c0 0.55 0.45 1 1 1h12c0.55 0 1-0.45 1-1V2c0-0.55-0.45-1-1-1z m0 13H1V2h12v12zM11 9H3V7h8v2z"></path>'),r.b("\n"+n),r.b("</svg>"),r.fl()},partials:{},subs:{}}),t.defaultTemplates["icon-file-renamed"]=new o.Template({code:function(e,t,n){var r=this;return r.b(n=n||""),r.b('<svg aria-hidden="true" class="d2h-icon d2h-moved" height="16" title="renamed" version="1.1"'),r.b("\n"+n),r.b('     viewBox="0 0 14 16" width="14">'),r.b("\n"+n),r.b('    <path d="M6 9H3V7h3V4l5 4-5 4V9z m8-7v12c0 0.55-0.45 1-1 1H1c-0.55 0-1-0.45-1-1V2c0-0.55 0.45-1 1-1h12c0.55 0 1 0.45 1 1z m-1 0H1v12h12V2z"></path>'),r.b("\n"+n),r.b("</svg>"),r.fl()},partials:{},subs:{}}),t.defaultTemplates["icon-file"]=new o.Template({code:function(e,t,n){var r=this;return r.b(n=n||""),r.b('<svg aria-hidden="true" class="d2h-icon" height="16" version="1.1" viewBox="0 0 12 16" width="12">'),r.b("\n"+n),r.b('    <path d="M6 5H2v-1h4v1zM2 8h7v-1H2v1z m0 2h7v-1H2v1z m0 2h7v-1H2v1z m10-7.5v9.5c0 0.55-0.45 1-1 1H1c-0.55 0-1-0.45-1-1V2c0-0.55 0.45-1 1-1h7.5l3.5 3.5z m-1 0.5L8 2H1v12h10V5z"></path>'),r.b("\n"+n),r.b("</svg>"),r.fl()},partials:{},subs:{}}),t.defaultTemplates["line-by-line-file-diff"]=new o.Template({code:function(e,t,n){var r=this;return r.b(n=n||""),r.b('<div id="'),r.b(r.v(r.f("fileHtmlId",e,t,0))),r.b('" class="d2h-file-wrapper" data-lang="'),r.b(r.v(r.d("file.language",e,t,0))),r.b('">'),r.b("\n"+n),r.b('    <div class="d2h-file-header">'),r.b("\n"+n),r.b("    "),r.b(r.t(r.f("filePath",e,t,0))),r.b("\n"+n),r.b("    </div>"),r.b("\n"+n),r.b('    <div class="d2h-file-diff">'),r.b("\n"+n),r.b('        <div class="d2h-code-wrapper">'),r.b("\n"+n),r.b('            <table class="d2h-diff-table">'),r.b("\n"+n),r.b('                <tbody class="d2h-diff-tbody">'),r.b("\n"+n),r.b("                "),r.b(r.t(r.f("diffs",e,t,0))),r.b("\n"+n),r.b("                </tbody>"),r.b("\n"+n),r.b("            </table>"),r.b("\n"+n),r.b("        </div>"),r.b("\n"+n),r.b("    </div>"),r.b("\n"+n),r.b("</div>"),r.fl()},partials:{},subs:{}}),t.defaultTemplates["line-by-line-numbers"]=new o.Template({code:function(e,t,n){var r=this;return r.b(n=n||""),r.b('<div class="line-num1">'),r.b(r.v(r.f("oldNumber",e,t,0))),r.b("</div>"),r.b("\n"+n),r.b('<div class="line-num2">'),r.b(r.v(r.f("newNumber",e,t,0))),r.b("</div>"),r.fl()},partials:{},subs:{}}),t.defaultTemplates["side-by-side-file-diff"]=new o.Template({code:function(e,t,n){var r=this;return r.b(n=n||""),r.b('<div id="'),r.b(r.v(r.f("fileHtmlId",e,t,0))),r.b('" class="d2h-file-wrapper" data-lang="'),r.b(r.v(r.d("file.language",e,t,0))),r.b('">'),r.b("\n"+n),r.b('    <div class="d2h-file-header">'),r.b("\n"+n),r.b("      "),r.b(r.t(r.f("filePath",e,t,0))),r.b("\n"+n),r.b("    </div>"),r.b("\n"+n),r.b('    <div class="d2h-files-diff">'),r.b("\n"+n),r.b('        <div class="d2h-file-side-diff">'),r.b("\n"+n),r.b('            <div class="d2h-code-wrapper">'),r.b("\n"+n),r.b('                <table class="d2h-diff-table">'),r.b("\n"+n),r.b('                    <tbody class="d2h-diff-tbody">'),r.b("\n"+n),r.b("                    "),r.b(r.t(r.d("diffs.left",e,t,0))),r.b("\n"+n),r.b("                    </tbody>"),r.b("\n"+n),r.b("                </table>"),r.b("\n"+n),r.b("            </div>"),r.b("\n"+n),r.b("        </div>"),r.b("\n"+n),r.b('        <div class="d2h-file-side-diff">'),r.b("\n"+n),r.b('            <div class="d2h-code-wrapper">'),r.b("\n"+n),r.b('                <table class="d2h-diff-table">'),r.b("\n"+n),r.b('                    <tbody class="d2h-diff-tbody">'),r.b("\n"+n),r.b("                    "),r.b(r.t(r.d("diffs.right",e,t,0))),r.b("\n"+n),r.b("                    </tbody>"),r.b("\n"+n),r.b("                </table>"),r.b("\n"+n),r.b("            </div>"),r.b("\n"+n),r.b("        </div>"),r.b("\n"+n),r.b("    </div>"),r.b("\n"+n),r.b("</div>"),r.fl()},partials:{},subs:{}}),t.defaultTemplates["tag-file-added"]=new o.Template({code:function(e,t,n){var r=this;return r.b(n=n||""),r.b('<span class="d2h-tag d2h-added d2h-added-tag">ADDED</span>'),r.fl()},partials:{},subs:{}}),t.defaultTemplates["tag-file-changed"]=new o.Template({code:function(e,t,n){var r=this;return r.b(n=n||""),r.b('<span class="d2h-tag d2h-changed d2h-changed-tag">CHANGED</span>'),r.fl()},partials:{},subs:{}}),t.defaultTemplates["tag-file-deleted"]=new o.Template({code:function(e,t,n){var r=this;return r.b(n=n||""),r.b('<span class="d2h-tag d2h-deleted d2h-deleted-tag">DELETED</span>'),r.fl()},partials:{},subs:{}}),t.defaultTemplates["tag-file-renamed"]=new o.Template({code:function(e,t,n){var r=this;return r.b(n=n||""),r.b('<span class="d2h-tag d2h-moved d2h-moved-tag">RENAMED</span>'),r.fl()},partials:{},subs:{}})},166:function(e,t,n){"use strict";var r,i=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),s=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),l=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),l=0;l<n.length;l++)"default"!==n[l]&&i(t,e,n[l]);return s(t,e),t}),o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.defaultDiff2HtmlConfig=void 0,t.parse=function(e,n={}){return a.parse(e,Object.assign(Object.assign({},t.defaultDiff2HtmlConfig),n))},t.html=function(e,n={}){const r=Object.assign(Object.assign({},t.defaultDiff2HtmlConfig),n),i="string"==typeof e?a.parse(e,r):e,s=new h.default(r),{colorScheme:l}=r,o={colorScheme:l};return(r.drawFileList?new c.FileListRenderer(s,o).render(i):"")+("side-by-side"===r.outputFormat?new f.default(s,r).render(i):new u.default(s,r).render(i))};const a=l(n(957)),c=n(501),u=l(n(895)),f=l(n(151)),d=n(613),h=o(n(178));t.defaultDiff2HtmlConfig=Object.assign(Object.assign(Object.assign({},u.defaultLineByLineRendererConfig),f.defaultSideBySideRendererConfig),{outputFormat:d.OutputFormatType.LINE_BY_LINE,drawFileList:!0})},501:function(e,t,n){"use strict";var r,i=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),s=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),l=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),l=0;l<n.length;l++)"default"!==n[l]&&i(t,e,n[l]);return s(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.FileListRenderer=t.defaultFileListRendererConfig=void 0;const o=l(n(302)),a="file-summary";t.defaultFileListRendererConfig={colorScheme:o.defaultRenderConfig.colorScheme},t.FileListRenderer=class{constructor(e,n={}){this.hoganUtils=e,this.config=Object.assign(Object.assign({},t.defaultFileListRendererConfig),n)}render(e){const t=e.map((e=>this.hoganUtils.render(a,"line",{fileHtmlId:o.getHtmlId(e),oldName:e.oldName,newName:e.newName,fileName:o.filenameDiff(e),deletedLines:"-"+e.deletedLines,addedLines:"+"+e.addedLines},{fileIcon:this.hoganUtils.template("icon",o.getFileIcon(e))}))).join("\n");return this.hoganUtils.render(a,"wrapper",{colorScheme:o.colorSchemeToCss(this.config.colorScheme),filesNumber:e.length,files:t})}}},178:function(e,t,n){"use strict";var r,i=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),s=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),l=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),l=0;l<n.length;l++)"default"!==n[l]&&i(t,e,n[l]);return s(t,e),t});Object.defineProperty(t,"__esModule",{value:!0});const o=l(n(714)),a=n(488);t.default=class{constructor({compiledTemplates:e={},rawTemplates:t={}}){const n=Object.entries(t).reduce(((e,[t,n])=>{const r=o.compile(n,{asString:!1});return Object.assign(Object.assign({},e),{[t]:r})}),{});this.preCompiledTemplates=Object.assign(Object.assign(Object.assign({},a.defaultTemplates),e),n)}static compile(e){return o.compile(e,{asString:!1})}render(e,t,n,r,i){const s=this.templateKey(e,t);try{return this.preCompiledTemplates[s].render(n,r,i)}catch(e){throw new Error(`Could not find template to render '${s}'`)}}template(e,t){return this.preCompiledTemplates[this.templateKey(e,t)]}templateKey(e,t){return`${e}-${t}`}}},895:function(e,t,n){"use strict";var r,i=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),s=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),l=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),l=0;l<n.length;l++)"default"!==n[l]&&i(t,e,n[l]);return s(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.defaultLineByLineRendererConfig=void 0;const o=l(n(598)),a=l(n(302)),c=n(613);t.defaultLineByLineRendererConfig=Object.assign(Object.assign({},a.defaultRenderConfig),{renderNothingWhenEmpty:!1,matchingMaxComparisons:2500,maxLineSizeInBlockForComparison:200});const u="generic",f="line-by-line";t.default=class{constructor(e,n={}){this.hoganUtils=e,this.config=Object.assign(Object.assign({},t.defaultLineByLineRendererConfig),n)}render(e){const t=e.map((e=>{let t;return t=e.blocks.length?this.generateFileHtml(e):this.generateEmptyDiff(),this.makeFileDiffHtml(e,t)})).join("\n");return this.hoganUtils.render(u,"wrapper",{colorScheme:a.colorSchemeToCss(this.config.colorScheme),content:t})}makeFileDiffHtml(e,t){if(this.config.renderNothingWhenEmpty&&Array.isArray(e.blocks)&&0===e.blocks.length)return"";const n=this.hoganUtils.template(f,"file-diff"),r=this.hoganUtils.template(u,"file-path"),i=this.hoganUtils.template("icon","file"),s=this.hoganUtils.template("tag",a.getFileIcon(e));return n.render({file:e,fileHtmlId:a.getHtmlId(e),diffs:t,filePath:r.render({fileDiffName:a.filenameDiff(e)},{fileIcon:i,fileTag:s})})}generateEmptyDiff(){return this.hoganUtils.render(u,"empty-diff",{contentClass:"d2h-code-line",CSSLineClass:a.CSSLineClass})}generateFileHtml(e){const t=o.newMatcherFn(o.newDistanceFn((t=>a.deconstructLine(t.content,e.isCombined).content)));return e.blocks.map((n=>{let r=this.hoganUtils.render(u,"block-header",{CSSLineClass:a.CSSLineClass,blockHeader:e.isTooBig?n.header:a.escapeForHtml(n.header),lineClass:"d2h-code-linenumber",contentClass:"d2h-code-line"});return this.applyLineGroupping(n).forEach((([n,i,s])=>{if(i.length&&s.length&&!n.length)this.applyRematchMatching(i,s,t).map((([t,n])=>{const{left:i,right:s}=this.processChangedLines(e,e.isCombined,t,n);r+=i,r+=s}));else if(n.length)n.forEach((t=>{const{prefix:n,content:i}=a.deconstructLine(t.content,e.isCombined);r+=this.generateSingleLineHtml(e,{type:a.CSSLineClass.CONTEXT,prefix:n,content:i,oldNumber:t.oldNumber,newNumber:t.newNumber})}));else if(i.length||s.length){const{left:t,right:n}=this.processChangedLines(e,e.isCombined,i,s);r+=t,r+=n}else console.error("Unknown state reached while processing groups of lines",n,i,s)})),r})).join("\n")}applyLineGroupping(e){const t=[];let n=[],r=[];for(let i=0;i<e.lines.length;i++){const s=e.lines[i];(s.type!==c.LineType.INSERT&&r.length||s.type===c.LineType.CONTEXT&&n.length>0)&&(t.push([[],n,r]),n=[],r=[]),s.type===c.LineType.CONTEXT?t.push([[s],[],[]]):s.type===c.LineType.INSERT&&0===n.length?t.push([[],[],[s]]):s.type===c.LineType.INSERT&&n.length>0?r.push(s):s.type===c.LineType.DELETE&&n.push(s)}return(n.length||r.length)&&(t.push([[],n,r]),n=[],r=[]),t}applyRematchMatching(e,t,n){const r=e.length*t.length,i=Math.max.apply(null,[0].concat(e.concat(t).map((e=>e.content.length))));return r<this.config.matchingMaxComparisons&&i<this.config.maxLineSizeInBlockForComparison&&("lines"===this.config.matching||"words"===this.config.matching)?n(e,t):[[e,t]]}processChangedLines(e,t,n,r){const i={right:"",left:""},s=Math.max(n.length,r.length);for(let l=0;l<s;l++){const s=n[l],o=r[l],c=void 0!==s&&void 0!==o?a.diffHighlight(s.content,o.content,t,this.config):void 0,u=void 0!==s&&void 0!==s.oldNumber?Object.assign(Object.assign({},void 0!==c?{prefix:c.oldLine.prefix,content:c.oldLine.content,type:a.CSSLineClass.DELETE_CHANGES}:Object.assign(Object.assign({},a.deconstructLine(s.content,t)),{type:a.toCSSClass(s.type)})),{oldNumber:s.oldNumber,newNumber:s.newNumber}):void 0,f=void 0!==o&&void 0!==o.newNumber?Object.assign(Object.assign({},void 0!==c?{prefix:c.newLine.prefix,content:c.newLine.content,type:a.CSSLineClass.INSERT_CHANGES}:Object.assign(Object.assign({},a.deconstructLine(o.content,t)),{type:a.toCSSClass(o.type)})),{oldNumber:o.oldNumber,newNumber:o.newNumber}):void 0,{left:d,right:h}=this.generateLineHtml(e,u,f);i.left+=d,i.right+=h}return i}generateLineHtml(e,t,n){return{left:this.generateSingleLineHtml(e,t),right:this.generateSingleLineHtml(e,n)}}generateSingleLineHtml(e,t){if(void 0===t)return"";const n=this.hoganUtils.render(f,"numbers",{oldNumber:t.oldNumber||"",newNumber:t.newNumber||""});return this.hoganUtils.render(u,"line",{type:t.type,lineClass:"d2h-code-linenumber",contentClass:"d2h-code-line",prefix:" "===t.prefix?"&nbsp;":t.prefix,content:t.content,lineNumber:n,line:t,file:e})}}},598:(e,t)=>{"use strict";function n(e,t){if(0===e.length)return t.length;if(0===t.length)return e.length;const n=[];let r,i;for(r=0;r<=t.length;r++)n[r]=[r];for(i=0;i<=e.length;i++)n[0][i]=i;for(r=1;r<=t.length;r++)for(i=1;i<=e.length;i++)t.charAt(r-1)===e.charAt(i-1)?n[r][i]=n[r-1][i-1]:n[r][i]=Math.min(n[r-1][i-1]+1,Math.min(n[r][i-1]+1,n[r-1][i]+1));return n[t.length][e.length]}Object.defineProperty(t,"__esModule",{value:!0}),t.levenshtein=n,t.newDistanceFn=function(e){return(t,r)=>{const i=e(t).trim(),s=e(r).trim();return n(i,s)/(i.length+s.length)}},t.newMatcherFn=function(e){return function t(n,r,i=0,s=new Map){const l=function(t,n,r=new Map){let i,s=1/0;for(let l=0;l<t.length;++l)for(let o=0;o<n.length;++o){const a=JSON.stringify([t[l],n[o]]);let c;r.has(a)&&(c=r.get(a))||(c=e(t[l],n[o]),r.set(a,c)),c<s&&(s=c,i={indexA:l,indexB:o,score:s})}return i}(n,r,s);if(!l||n.length+r.length<3)return[[n,r]];const o=n.slice(0,l.indexA),a=r.slice(0,l.indexB),c=[n[l.indexA]],u=[r[l.indexB]],f=l.indexA+1,d=l.indexB+1,h=n.slice(f),p=r.slice(d),g=t(o,a,i+1,s),m=t(c,u,i+1,s),b=t(h,p,i+1,s);let v=m;return(l.indexA>0||l.indexB>0)&&(v=g.concat(v)),(n.length>f||r.length>d)&&(v=v.concat(b)),v}}},302:function(e,t,n){"use strict";var r,i=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),s=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),l=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),l=0;l<n.length;l++)"default"!==n[l]&&i(t,e,n[l]);return s(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.defaultRenderConfig=t.CSSLineClass=void 0,t.toCSSClass=function(e){switch(e){case u.LineType.CONTEXT:return t.CSSLineClass.CONTEXT;case u.LineType.INSERT:return t.CSSLineClass.INSERTS;case u.LineType.DELETE:return t.CSSLineClass.DELETES}},t.colorSchemeToCss=function(e){switch(e){case u.ColorSchemeType.DARK:return"d2h-dark-color-scheme";case u.ColorSchemeType.AUTO:return"d2h-auto-color-scheme";case u.ColorSchemeType.LIGHT:default:return"d2h-light-color-scheme"}},t.escapeForHtml=m,t.deconstructLine=b,t.filenameDiff=v,t.getHtmlId=function(e){return`d2h-${(0,a.hashCode)(v(e)).toString().slice(-6)}`},t.getFileIcon=function(e){let t="file-changed";return e.isRename||e.isCopy?t="file-renamed":e.isNew?t="file-added":e.isDeleted?t="file-deleted":e.newName!==e.oldName&&(t="file-renamed"),t},t.diffHighlight=function(e,n,r,i={}){const{matching:s,maxLineLengthHighlight:l,matchWordsThreshold:a,diffStyle:c}=Object.assign(Object.assign({},t.defaultRenderConfig),i),u=b(e,r,!1),f=b(n,r,!1);if(u.content.length>l||f.content.length>l)return{oldLine:{prefix:u.prefix,content:m(u.content)},newLine:{prefix:f.prefix,content:m(f.content)}};const p="char"===c?o.diffChars(u.content,f.content):o.diffWordsWithSpace(u.content,f.content),v=[];if("word"===c&&"words"===s){const e=p.filter((e=>e.removed)),t=p.filter((e=>e.added));h(t,e).forEach((e=>{1===e[0].length&&1===e[1].length&&d(e[0][0],e[1][0])<a&&(v.push(e[0][0]),v.push(e[1][0]))}))}const y=p.reduce(((e,t)=>{const n=t.added?"ins":t.removed?"del":null,r=v.indexOf(t)>-1?' class="d2h-change"':"",i=m(t.value);return null!==n?`${e}<${n}${r}>${i}</${n}>`:`${e}${i}`}),"");return{oldLine:{prefix:u.prefix,content:(w=y,w.replace(/(<ins[^>]*>((.|\n)*?)<\/ins>)/g,""))},newLine:{prefix:f.prefix,content:g(y)}};var w};const o=l(n(546)),a=n(185),c=l(n(598)),u=n(613);t.CSSLineClass={INSERTS:"d2h-ins",DELETES:"d2h-del",CONTEXT:"d2h-cntx",INFO:"d2h-info",INSERT_CHANGES:"d2h-ins d2h-change",DELETE_CHANGES:"d2h-del d2h-change"},t.defaultRenderConfig={matching:u.LineMatchingType.NONE,matchWordsThreshold:.25,maxLineLengthHighlight:1e4,diffStyle:u.DiffStyleType.WORD,colorScheme:u.ColorSchemeType.LIGHT};const f="/",d=c.newDistanceFn((e=>e.value)),h=c.newMatcherFn(d);function p(e){return-1!==e.indexOf("dev/null")}function g(e){return e.replace(/(<del[^>]*>((.|\n)*?)<\/del>)/g,"")}function m(e){return e.slice(0).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;").replace(/\//g,"&#x2F;")}function b(e,t,n=!0){const r=function(e){return e?2:1}(t);return{prefix:e.substring(0,r),content:n?m(e.substring(r)):e.substring(r)}}function v(e){const t=(0,a.unifyPath)(e.oldName),n=(0,a.unifyPath)(e.newName);if(t===n||p(t)||p(n))return p(n)?t:n;{const e=[],r=[],i=t.split(f),s=n.split(f);let l=0,o=i.length-1,a=s.length-1;for(;l<o&&l<a&&i[l]===s[l];)e.push(s[l]),l+=1;for(;o>l&&a>l&&i[o]===s[a];)r.unshift(s[a]),o-=1,a-=1;const c=e.join(f),u=r.join(f),d=i.slice(l,o+1).join(f),h=s.slice(l,a+1).join(f);return c.length&&u.length?c+f+"{"+d+" → "+h+"}"+f+u:c.length?c+f+"{"+d+" → "+h+"}":u.length?"{"+d+" → "+h+"}"+f+u:t+" → "+n}}},151:function(e,t,n){"use strict";var r,i=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),s=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),l=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),l=0;l<n.length;l++)"default"!==n[l]&&i(t,e,n[l]);return s(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.defaultSideBySideRendererConfig=void 0;const o=l(n(598)),a=l(n(302)),c=n(613);t.defaultSideBySideRendererConfig=Object.assign(Object.assign({},a.defaultRenderConfig),{renderNothingWhenEmpty:!1,matchingMaxComparisons:2500,maxLineSizeInBlockForComparison:200});const u="generic";t.default=class{constructor(e,n={}){this.hoganUtils=e,this.config=Object.assign(Object.assign({},t.defaultSideBySideRendererConfig),n)}render(e){const t=e.map((e=>{let t;return t=e.blocks.length?this.generateFileHtml(e):this.generateEmptyDiff(),this.makeFileDiffHtml(e,t)})).join("\n");return this.hoganUtils.render(u,"wrapper",{colorScheme:a.colorSchemeToCss(this.config.colorScheme),content:t})}makeFileDiffHtml(e,t){if(this.config.renderNothingWhenEmpty&&Array.isArray(e.blocks)&&0===e.blocks.length)return"";const n=this.hoganUtils.template("side-by-side","file-diff"),r=this.hoganUtils.template(u,"file-path"),i=this.hoganUtils.template("icon","file"),s=this.hoganUtils.template("tag",a.getFileIcon(e));return n.render({file:e,fileHtmlId:a.getHtmlId(e),diffs:t,filePath:r.render({fileDiffName:a.filenameDiff(e)},{fileIcon:i,fileTag:s})})}generateEmptyDiff(){return{right:"",left:this.hoganUtils.render(u,"empty-diff",{contentClass:"d2h-code-side-line",CSSLineClass:a.CSSLineClass})}}generateFileHtml(e){const t=o.newMatcherFn(o.newDistanceFn((t=>a.deconstructLine(t.content,e.isCombined).content)));return e.blocks.map((n=>{const r={left:this.makeHeaderHtml(n.header,e),right:this.makeHeaderHtml("")};return this.applyLineGroupping(n).forEach((([n,i,s])=>{if(i.length&&s.length&&!n.length)this.applyRematchMatching(i,s,t).map((([t,n])=>{const{left:i,right:s}=this.processChangedLines(e.isCombined,t,n);r.left+=i,r.right+=s}));else if(n.length)n.forEach((t=>{const{prefix:n,content:i}=a.deconstructLine(t.content,e.isCombined),{left:s,right:l}=this.generateLineHtml({type:a.CSSLineClass.CONTEXT,prefix:n,content:i,number:t.oldNumber},{type:a.CSSLineClass.CONTEXT,prefix:n,content:i,number:t.newNumber});r.left+=s,r.right+=l}));else if(i.length||s.length){const{left:t,right:n}=this.processChangedLines(e.isCombined,i,s);r.left+=t,r.right+=n}else console.error("Unknown state reached while processing groups of lines",n,i,s)})),r})).reduce(((e,t)=>({left:e.left+t.left,right:e.right+t.right})),{left:"",right:""})}applyLineGroupping(e){const t=[];let n=[],r=[];for(let i=0;i<e.lines.length;i++){const s=e.lines[i];(s.type!==c.LineType.INSERT&&r.length||s.type===c.LineType.CONTEXT&&n.length>0)&&(t.push([[],n,r]),n=[],r=[]),s.type===c.LineType.CONTEXT?t.push([[s],[],[]]):s.type===c.LineType.INSERT&&0===n.length?t.push([[],[],[s]]):s.type===c.LineType.INSERT&&n.length>0?r.push(s):s.type===c.LineType.DELETE&&n.push(s)}return(n.length||r.length)&&(t.push([[],n,r]),n=[],r=[]),t}applyRematchMatching(e,t,n){const r=e.length*t.length,i=Math.max.apply(null,[0].concat(e.concat(t).map((e=>e.content.length))));return r<this.config.matchingMaxComparisons&&i<this.config.maxLineSizeInBlockForComparison&&("lines"===this.config.matching||"words"===this.config.matching)?n(e,t):[[e,t]]}makeHeaderHtml(e,t){return this.hoganUtils.render(u,"block-header",{CSSLineClass:a.CSSLineClass,blockHeader:(null==t?void 0:t.isTooBig)?e:a.escapeForHtml(e),lineClass:"d2h-code-side-linenumber",contentClass:"d2h-code-side-line"})}processChangedLines(e,t,n){const r={right:"",left:""},i=Math.max(t.length,n.length);for(let s=0;s<i;s++){const i=t[s],l=n[s],o=void 0!==i&&void 0!==l?a.diffHighlight(i.content,l.content,e,this.config):void 0,c=void 0!==i&&void 0!==i.oldNumber?Object.assign(Object.assign({},void 0!==o?{prefix:o.oldLine.prefix,content:o.oldLine.content,type:a.CSSLineClass.DELETE_CHANGES}:Object.assign(Object.assign({},a.deconstructLine(i.content,e)),{type:a.toCSSClass(i.type)})),{number:i.oldNumber}):void 0,u=void 0!==l&&void 0!==l.newNumber?Object.assign(Object.assign({},void 0!==o?{prefix:o.newLine.prefix,content:o.newLine.content,type:a.CSSLineClass.INSERT_CHANGES}:Object.assign(Object.assign({},a.deconstructLine(l.content,e)),{type:a.toCSSClass(l.type)})),{number:l.newNumber}):void 0,{left:f,right:d}=this.generateLineHtml(c,u);r.left+=f,r.right+=d}return r}generateLineHtml(e,t){return{left:this.generateSingleHtml(e),right:this.generateSingleHtml(t)}}generateSingleHtml(e){const t="d2h-code-side-linenumber",n="d2h-code-side-line";return this.hoganUtils.render(u,"line",{type:(null==e?void 0:e.type)||`${a.CSSLineClass.CONTEXT} d2h-emptyplaceholder`,lineClass:void 0!==e?t:`${t} d2h-code-side-emptyplaceholder`,contentClass:void 0!==e?n:`${n} d2h-code-side-emptyplaceholder`,prefix:" "===(null==e?void 0:e.prefix)?"&nbsp;":null==e?void 0:e.prefix,content:null==e?void 0:e.content,lineNumber:null==e?void 0:e.number})}}},613:(e,t)=>{"use strict";var n,r;Object.defineProperty(t,"__esModule",{value:!0}),t.ColorSchemeType=t.DiffStyleType=t.LineMatchingType=t.OutputFormatType=t.LineType=void 0,function(e){e.INSERT="insert",e.DELETE="delete",e.CONTEXT="context"}(n||(t.LineType=n={})),t.OutputFormatType={LINE_BY_LINE:"line-by-line",SIDE_BY_SIDE:"side-by-side"},t.LineMatchingType={LINES:"lines",WORDS:"words",NONE:"none"},t.DiffStyleType={WORD:"word",CHAR:"char"},function(e){e.AUTO="auto",e.DARK="dark",e.LIGHT="light"}(r||(t.ColorSchemeType=r={}))},92:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Diff2HtmlUI=t.defaultDiff2HtmlUIConfig=void 0;const r=n(206),i=n(166);t.defaultDiff2HtmlUIConfig=Object.assign(Object.assign({},i.defaultDiff2HtmlConfig),{synchronisedScroll:!0,highlight:!0,fileListToggle:!0,fileListStartVisible:!1,highlightLanguages:new Map,smartSelection:!0,fileContentToggle:!0,stickyFileHeaders:!0}),t.Diff2HtmlUI=class{constructor(e,n,r={},s){this.hljs=null,this.currentSelectionColumnId=-1,this.config=Object.assign(Object.assign({},t.defaultDiff2HtmlUIConfig),r),this.diffHtml=void 0!==n?(0,i.html)(n,this.config):e.innerHTML,this.targetElement=e,void 0!==s&&(this.hljs=s)}draw(){this.targetElement.innerHTML=this.diffHtml,this.config.synchronisedScroll&&this.synchronisedScroll(),this.config.highlight&&this.highlightCode(),this.config.fileListToggle&&this.fileListToggle(this.config.fileListStartVisible),this.config.fileContentToggle&&this.fileContentToggle(),this.config.stickyFileHeaders&&this.stickyFileHeaders()}synchronisedScroll(){this.targetElement.querySelectorAll(".d2h-file-wrapper").forEach((e=>{const[t,n]=Array().slice.call(e.querySelectorAll(".d2h-file-side-diff"));if(void 0===t||void 0===n)return;const r=e=>{null!==e&&null!==e.target&&(e.target===t?(n.scrollTop=t.scrollTop,n.scrollLeft=t.scrollLeft):(t.scrollTop=n.scrollTop,t.scrollLeft=n.scrollLeft))};t.addEventListener("scroll",r),n.addEventListener("scroll",r)}))}fileListToggle(e){const t=this.targetElement.querySelector(".d2h-show"),n=this.targetElement.querySelector(".d2h-hide"),r=this.targetElement.querySelector(".d2h-file-list");if(null===t||null===n||null===r)return;const i=()=>{t.style.display="none",n.style.display="inline",r.style.display="block"},s=()=>{t.style.display="inline",n.style.display="none",r.style.display="none"};t.addEventListener("click",(()=>i())),n.addEventListener("click",(()=>s()));const l=this.getHashTag();"files-summary-show"===l?i():"files-summary-hide"===l?s():e?i():s()}fileContentToggle(){this.targetElement.querySelectorAll(".d2h-file-collapse").forEach((e=>{e.style.display="flex";const t=t=>{var n;const r=null===(n=e.closest(".d2h-file-wrapper"))||void 0===n?void 0:n.querySelector(t);null!=r&&(e.classList.toggle("d2h-selected"),r.classList.toggle("d2h-d-none"))};e.addEventListener("click",(n=>(n=>{e!==n.target&&(t(".d2h-file-diff"),t(".d2h-files-diff"))})(n)))}))}highlightCode(){const e=this.hljs;if(null===e)throw new Error("Missing a `highlight.js` implementation. Please provide one when instantiating Diff2HtmlUI.");this.targetElement.querySelectorAll(".d2h-file-wrapper").forEach((t=>{const n=t.getAttribute("data-lang");this.config.highlightLanguages instanceof Map||(this.config.highlightLanguages=new Map(Object.entries(this.config.highlightLanguages)));let i=n&&this.config.highlightLanguages.has(n)?this.config.highlightLanguages.get(n):n?(0,r.getLanguage)(n):"plaintext";void 0===e.getLanguage(i)&&(i="plaintext"),t.querySelectorAll(".d2h-code-line-ctn").forEach((t=>{const n=t.textContent,s=t.parentNode;if(null===n||null===s||!this.isElement(s))return;const l=(0,r.closeTags)(e.highlight(n,{language:i,ignoreIllegals:!0})),o=(0,r.nodeStream)(t);if(o.length){const e=document.createElementNS("http://www.w3.org/1999/xhtml","div");e.innerHTML=l.value,l.value=(0,r.mergeStreams)(o,(0,r.nodeStream)(e),n)}t.classList.add("hljs"),l.language&&t.classList.add(l.language),t.innerHTML=l.value}))}))}stickyFileHeaders(){this.targetElement.querySelectorAll(".d2h-file-header").forEach((e=>{e.classList.add("d2h-sticky-header")}))}smartSelection(){console.warn("Smart selection is now enabled by default with CSS. No need to call this method anymore.")}getHashTag(){const e=document.URL,t=e.indexOf("#");let n=null;return-1!==t&&(n=e.substr(t+1)),n}isElement(e){return null!==e&&void 0!==(null==e?void 0:e.classList)}}},206:(e,t)=>{"use strict";function n(e){return e.replace(/&/gm,"&amp;").replace(/</gm,"&lt;").replace(/>/gm,"&gt;")}function r(e){return e.nodeName.toLowerCase()}Object.defineProperty(t,"__esModule",{value:!0}),t.nodeStream=function(e){const t=[],n=(e,i)=>{for(let s=e.firstChild;s;s=s.nextSibling)3===s.nodeType&&null!==s.nodeValue?i+=s.nodeValue.length:1===s.nodeType&&(t.push({event:"start",offset:i,node:s}),i=n(s,i),r(s).match(/br|hr|img|input/)||t.push({event:"stop",offset:i,node:s}));return i};return n(e,0),t},t.mergeStreams=function(e,t,i){let s=0,l="";const o=[];function a(){return e.length&&t.length?e[0].offset!==t[0].offset?e[0].offset<t[0].offset?e:t:"start"===t[0].event?e:t:e.length?e:t}function c(e){if(null===(t=e)||void 0===(null==t?void 0:t.attributes))throw new Error("Node is not an Element");var t;l+=`<${r(e)} ${Array().map.call(e.attributes,(e=>`${e.nodeName}="${n(e.value).replace(/"/g,"&quot;")}"`)).join(" ")}>`}function u(e){l+="</"+r(e)+">"}function f(e){("start"===e.event?c:u)(e.node)}for(;e.length||t.length;){let t=a();if(l+=n(i.substring(s,t[0].offset)),s=t[0].offset,t===e){o.reverse().forEach(u);do{f(t.splice(0,1)[0]),t=a()}while(t===e&&t.length&&t[0].offset===s);o.reverse().forEach(c)}else"start"===t[0].event?o.push(t[0].node):o.pop(),f(t.splice(0,1)[0])}return l+n(i.substr(s))},t.closeTags=function(e){const t=new Array;return e.value=e.value.split("\n").map((e=>{const n=t.map((e=>`<span class="${e}">`)).join(""),r=e.matchAll(/(<span class="(.*?)">|<\/span>)/g);return Array.from(r).forEach((e=>{"</span>"===e[0]?t.shift():t.unshift(e[2])})),n+e+"</span>".repeat(t.length)})).join("\n"),e},t.getLanguage=function(e){var t;return null!==(t=i[e])&&void 0!==t?t:"plaintext"};const i={"1c":"1c",abnf:"abnf",accesslog:"accesslog",as:"actionscript",adb:"ada",ada:"ada",ads:"ada",angelscript:"angelscript",apache:"apache",applescript:"applescript",scpt:"applescript",arcade:"arcade",cpp:"cpp",hpp:"cpp",arduino:"arduino",ino:"arduino",armasm:"armasm",arm:"armasm",xml:"xml",html:"xml",xhtml:"xml",rss:"xml",atom:"xml",xjb:"xml",xsd:"xml",xsl:"xml",plist:"xml",svg:"xml",asciidoc:"asciidoc",adoc:"asciidoc",asc:"asciidoc",aspectj:"aspectj",ahk:"autohotkey",ahkl:"autohotkey",au3:"autoit",avrasm:"avrasm",awk:"awk",axapta:"axapta","x++":"axapta",bash:"bash",sh:"bash",zsh:"bash",b:"basic",bnf:"bnf",bf:"brainfuck",c:"c",h:"c",cats:"c",idc:"c",cal:"cal",capnproto:"capnproto",capnp:"capnproto",ceylon:"ceylon",clean:"clean",clj:"clojure",boot:"clojure",cl2:"clojure",cljc:"clojure",cljs:"clojure","cljs.hl":"clojure",cljscm:"clojure",cljx:"clojure",hic:"clojure","clojure-repl":"clojure-repl",cmake:"cmake","cmake.in":"cmake",coffee:"coffeescript",_coffee:"coffeescript",cake:"coffeescript",cjsx:"coffeescript",iced:"coffeescript",cson:"coffeescript",coq:"coq",cos:"cos",cls:"cos",crmsh:"crmsh",crm:"crmsh",pcmk:"crmsh",cr:"crystal",cs:"csharp",csx:"csharp",csp:"csp",css:"css",d:"d",di:"d",md:"markdown",markdown:"markdown",mdown:"markdown",mdwn:"markdown",mkd:"markdown",mkdn:"markdown",mkdown:"markdown",ronn:"markdown",workbook:"markdown",dart:"dart",dpr:"delphi",dfm:"delphi",pas:"delphi",pascal:"delphi",diff:"diff",patch:"diff",django:"django",jinja:"django",dns:"dns",zone:"dns",bind:"dns",dockerfile:"dockerfile",docker:"dockerfile",dos:"dos",bat:"dos",cmd:"dos",dsconfig:"dsconfig",dts:"dts",dust:"dust",dst:"dust",ebnf:"ebnf",ex:"elixir",exs:"elixir",elm:"elm",rb:"ruby",builder:"ruby",eye:"ruby",gemspec:"ruby",god:"ruby",jbuilder:"ruby",mspec:"ruby",pluginspec:"ruby",podspec:"ruby",rabl:"ruby",rake:"ruby",rbuild:"ruby",rbw:"ruby",rbx:"ruby",ru:"ruby",ruby:"ruby",spec:"ruby",thor:"ruby",watchr:"ruby",erb:"erb","erlang-repl":"erlang-repl",erl:"erlang","app.src":"erlang",escript:"erlang",hrl:"erlang",xrl:"erlang",yrl:"erlang",excel:"excel",xls:"excel",xlsx:"excel",fix:"fix",flix:"flix",f90:"fortran",f:"fortran",f03:"fortran",f08:"fortran",f77:"fortran",f95:"fortran",for:"fortran",fpp:"fortran",fs:"fsharp",fsx:"fsharp",gams:"gams",gms:"gams",gauss:"gauss",gss:"gauss",gcode:"gcode",nc:"gcode",gherkin:"gherkin",glsl:"glsl",fp:"glsl",frag:"glsl",frg:"glsl",fsh:"glsl",fshader:"glsl",geo:"glsl",geom:"glsl",glslv:"glsl",gshader:"glsl",shader:"glsl",tesc:"glsl",tese:"glsl",vert:"glsl",vrx:"glsl",vsh:"glsl",vshader:"glsl",gml:"gml",go:"go",bal:"go",golo:"golo",gololang:"golo",gradle:"gradle",groovy:"groovy",grt:"groovy",gtpl:"groovy",gvy:"groovy",haml:"haml","haml.deface":"haml",handlebars:"handlebars",hbs:"handlebars","html.hbs":"handlebars","html.handlebars":"handlebars",hs:"haskell",hsc:"haskell",idr:"haskell",purs:"haskell",hx:"haxe",hxsl:"haxe",hsp:"hsp",htmlbars:"htmlbars",http:"http",https:"http",hy:"hy",inform7:"inform7",i7:"inform7",ini:"ini",toml:"ini",cfg:"ini",prefs:"ini",irpf90:"irpf90",isbl:"isbl",java:"java",jsp:"java",js:"javascript",jsx:"javascript",_js:"javascript",bones:"javascript",es:"javascript",es6:"javascript",gs:"javascript",jake:"javascript",jsb:"javascript",jscad:"javascript",jsfl:"javascript",jsm:"javascript",jss:"javascript",mjs:"javascript",njs:"javascript",pac:"javascript",sjs:"javascript",ssjs:"javascript",xsjs:"javascript",xsjslib:"javascript",cfc:"javascript","jboss-cli":"jboss-cli",json:"json",avsc:"json",geojson:"json",gltf:"json","JSON-tmLanguage":"json",jsonl:"json",tfstate:"json","tfstate.backup":"json",topojson:"json",webapp:"json",webmanifest:"json",jl:"julia","julia-repl":"julia-repl",kt:"kotlin",ktm:"kotlin",kts:"kotlin",lasso:"lasso",lassoscript:"lasso",tex:"latex",ldif:"ldif",leaf:"leaf",less:"less",lisp:"lisp",factor:"lisp",livecodeserver:"livecodeserver",ls:"livescript",_ls:"livescript",llvm:"llvm",lsl:"lsl",lua:"lua",nse:"lua",p8:"lua",pd_lua:"lua",rbxs:"lua",wlua:"lua",mak:"makefile",make:"makefile",mk:"makefile",mkfile:"makefile",mathematica:"mathematica",mma:"mathematica",wl:"mathematica",matlab:"matlab",maxima:"maxima",mel:"mel",mercury:"mercury",mipsasm:"mipsasm",miz:"mizar",voc:"mizar",al:"perl",cgi:"perl",fcgi:"perl",perl:"perl",ph:"perl",plx:"perl",pl:"perl",pm:"perl",psgi:"perl",t:"perl",mojolicious:"mojolicious",monkey:"monkey",monkey2:"monkey",moonscript:"moonscript",moon:"moonscript",n1ql:"n1ql",nginxconf:"nginx",nim:"nim",nimrod:"nim",nix:"nix",nsi:"nsis",nsh:"nsis",m:"objectivec",objc:"objectivec",mm:"objectivec","obj-c":"objectivec","obj-c++":"objectivec","objective-c++":"objectivec",fun:"ocaml",sig:"ocaml",ml:"ocaml",mli:"ocaml",eliom:"ocaml",eliomi:"ocaml",ml4:"ocaml",mll:"ocaml",mly:"ocaml",openscad:"openscad",oxygene:"oxygene",parser3:"parser3",pf:"pf","pf.conf":"pf",pgsql:"pgsql",postgres:"pgsql",postgresql:"pgsql",php:"php",aw:"php",ctp:"php",inc:"php",php3:"php",php4:"php",php5:"php",phps:"php",phpt:"php","php-template":"php-template",plaintext:"plaintext",txt:"plaintext",text:"plaintext",pony:"pony",ps:"powershell",ps1:"powershell",psd1:"powershell",psm1:"powershell",pde:"processing",profile:"profile",pro:"prolog",prolog:"prolog",yap:"prolog",properties:"properties",proto:"protobuf",puppet:"puppet",pp:"puppet",purebasic:"purebasic",py:"python",bzl:"python",gyp:"python",gypi:"python",lmi:"python",py3:"python",pyde:"python",pyi:"python",pyp:"python",pyt:"python",pyw:"python",rpy:"python",tac:"python",wsgi:"python",xpy:"python","python-repl":"python-repl",pycon:"python-repl",q:"q",k:"q",kdb:"q",qml:"qml",r:"r",rd:"r",rsx:"r",reasonml:"reasonml",re:"reasonml",rib:"rib",roboconf:"roboconf",graph:"roboconf",instances:"roboconf",routeros:"routeros",rsl:"rsl",ruleslanguage:"ruleslanguage",rs:"rust","rs.in":"rust",sas:"sas",scala:"scala",kojo:"scala",sbt:"scala",sc:"scala",scm:"scheme",sch:"scheme",sld:"scheme",sls:"scheme",sps:"scheme",ss:"scheme",rkt:"scheme",scilab:"scilab",scss:"scss",shell:"shell",smali:"smali",st:"smalltalk",sml:"sml",sqf:"sqf",sql:"sql",cql:"sql",ddl:"sql",mysql:"sql",prc:"sql",tab:"sql",udf:"sql",viw:"sql",stan:"stan",stanfuncs:"stan",stata:"stata",step21:"step21",step:"step21",stp:"step21",styl:"stylus",subunit:"subunit",swift:"swift",taggerscript:"taggerscript",yml:"yaml",mir:"yaml",reek:"yaml",rviz:"yaml","sublime-syntax":"yaml",syntax:"yaml",yaml:"yaml","yaml-tmlanguage":"yaml","yml.mysql":"yaml",tap:"tap",tcl:"tcl",adp:"tcl",tm:"tcl",thrift:"thrift",tp:"tp",twig:"twig",craftcms:"twig",ts:"typescript",tsx:"typescript",vala:"vala",vbnet:"vbnet",vb:"vbnet",vbscript:"vbscript",vbs:"vbscript","vbscript-html":"vbscript-html",v:"verilog",veo:"verilog",vhdl:"vhdl",vhd:"vhdl",vhf:"vhdl",vhi:"vhdl",vho:"vhdl",vhs:"vhdl",vht:"vhdl",vhw:"vhdl",vim:"vim",x86asm:"x86asm",xl:"xl",xquery:"xquery",xpath:"xquery",xq:"xquery",zephir:"zephir",zep:"zephir"}},185:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.escapeForRegExp=function(e){return e.replace(n,"\\$&")},t.unifyPath=function(e){return e?e.replace(/\\/g,"/"):e},t.hashCode=function(e){let t,n,r,i=0;for(t=0,r=e.length;t<r;t++)n=e.charCodeAt(t),i=(i<<5)-i+n,i|=0;return i};const n=RegExp("["+["-","[","]","/","{","}","(",")","*","+","?",".","\\","^","$","|"].join("\\")+"]","g")}},t={},function n(r){var i=t[r];if(void 0!==i)return i.exports;var s=t[r]={exports:{}};return e[r].call(s.exports,s,s.exports,n),s.exports}(92);var e,t}));