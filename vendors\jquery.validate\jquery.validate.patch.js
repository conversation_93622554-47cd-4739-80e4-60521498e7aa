/* 设置 jQuery Validation 默认值 */
$.validator.setDefaults({
  errorElement: "div",
  errorPlacement: function (error, element) {
    error.addClass("invalid-feedback");
    if (element.parents('.form-group')) {
      error.appendTo(element.parents('.form-group'))
    } else {
      error.insertAfter(element);
    }
  },
  highlight: function (element, errorClass, validClass) {
    if ($(element).prop("type") !== "checkbox" && $(element).prop("type") !== "radio") {
      $(element).addClass("is-invalid").removeClass("is-valid");
    }
  },
  unhighlight: function (element, errorClass, validClass) {
    $(element).removeClass("is-invalid");
  }
});
