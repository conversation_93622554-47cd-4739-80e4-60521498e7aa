/*
  扩充日期格式化方法
  对Date的扩展，将 Date 转化为指定格式的String
  月(M)、日(d)、12小时(h)、24小时(H)、分(m)、秒(s)、周(E)、季度(q) 可以用 1-2 个占位符
  年(y) 可以用 1-4 个占位符，毫秒(S) 只能用 1 个占位符 (是 1-3 位的数字)
  例子：
    (new Date()).format("yyyy-MM-dd hh:mm:ss.S")==> 2006-07-02 08:09:04.423
    (new Date()).format("yyyy-MM-dd E HH:mm:ss") ==> 2009-03-10 二 20:09:04
    (new Date()).format("yyyy-MM-dd EE hh:mm:ss") ==> 2009-03-10 周二 08:09:04
    (new Date()).format("yyyy-MM-dd EEE hh:mm:ss") ==> 2009-03-10 星期二 08:09:04
    (new Date()).format("yyyy-M-d h:m:s.S") ==> 2006-7-2 8:9:4.18
*/
Date.prototype.format = function (fmt) {
  var o = {
    "M+": this.getMonth() + 1, //月份
    "d+": this.getDate(), //日
    "h+": this.getHours() % 12 == 0 ? 12 : this.getHours() % 12, //小时
    "H+": this.getHours(), //小时
    "m+": this.getMinutes(), //分
    "s+": this.getSeconds(), //秒
    "q+": Math.floor((this.getMonth() + 3) / 3), //季度
    S: this.getMilliseconds(), //毫秒
  };
  var week = {
    0: "/u65e5",
    1: "/u4e00",
    2: "/u4e8c",
    3: "/u4e09",
    4: "/u56db",
    5: "/u4e94",
    6: "/u516d",
  };
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(
      RegExp.$1,
      (this.getFullYear() + "").substr(4 - RegExp.$1.length)
    );
  }
  if (/(E+)/.test(fmt)) {
    fmt = fmt.replace(
      RegExp.$1,
      (RegExp.$1.length > 1
        ? RegExp.$1.length > 2
          ? "/u661f/u671f"
          : "/u5468"
        : "") + week[this.getDay() + ""]
    );
  }
  for (var k in o) {
    if (new RegExp("(" + k + ")").test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length)
      );
    }
  }
  return fmt;
};

/* 设置 Ajax 通用通用参数 */
$.ajaxSetup({
  complete: function (jqXHR, textStatus) {
    var ajaxStatus = jqXHR.getResponseHeader("AJAX_STATUS");
    if (ajaxStatus === "NOT_LOGIN") {
      var loginUrl = jqXHR.getResponseHeader("LOGIN_URL");
      PNotify.error({
        title: "会话已过期",
        text:
          '<a href="' +
          loginUrl +
          '" class="btn btn-danger">点击此处重新登录</a>',
        hide: false,
        textTrusted: true,
      });
    } else if (ajaxStatus === "NOT_ALLOW") {
      PNotify.error("没有访问权限");
    }
  },
  statusCode: {
    404: function () {
      PNotify.error("404: 请求的地址不存在");
    },
    500: function () {
      PNotify.error("500: 系统发生异常");
    },
  },
});

/* 对表单进行 Ajax 提交 */
function formAjaxSubmit(form, beforeFunc, afterFunc) {
  $(form).ajaxSubmit({
    method: "POST",
    dataType: "json",
    beforeSubmit: beforeFunc,
    success: getAjaxResultFunc(afterFunc),
  });
}

/* 关闭最后打开的一个模态框 */
function closeModal() {
  $(".ekko-lightbox:last").modal("hide");
}

/* 关闭全部模态框 */
function closeAllModal() {
  $(".ekko-lightbox").modal("hide");
}

/* 获取 Ajax 操作后处理 JSON 结果的函数 */
function getAjaxResultFunc(customFunc) {
  return function (data) {
    // 如果有错误则提示信息且终止后续操作
    if (data.code !== 0) {
      PNotify.error(data.message);
      return;
    }

    // 优先执行自定义处理逻辑
    if (customFunc) {
      customFunc(data);
      return;
    }

    // 自动处理（此方式已不推荐使用，使用 autoAction 替代）
    if (data.auto) {
      if (data.auto === "@reload") {
        // 刷新本页
        location.reload();
        return;
      } else if (data.auto === "@reloadTop") {
        // 刷新整页
        top.location.reload();
        return;
      } else if (data.auto === "@closeModal") {
        // 关闭最后打开的一个模态框（并继续执行后续处理）
        closeModal();
      } else if (data.auto === "@closeAllModal") {
        // 关闭全部模态框（并继续执行后续处理）
        closeAllModal();
      } else {
        // 默认进行页面跳转
        location.href = data.auto;
        return;
      }
    }

    // 如果有内容则提示操作信息
    if ($.trim(data.message)) {
      PNotify.success(data.message);
    }
  };
}

/* 常用 Action 响应操作 */
function autoAction(autoName, showMessage, appFunc) {
  if (showMessage === undefined) {
    showMessage = true; // 默认显示提示信息
  }
  return function (data) {
    // 必要时使用 Flash Message 保存操作结果
    if (
      autoName !== "@closeModal" &&
      autoName !== "@closeAllModal" &&
      showMessage &&
      data &&
      data.message
    ) {
      var messageResult = $.ajax({
        url: base + "/common/common_action.jsp",
        method: "POST",
        data: {
          action: "setFlashMessage",
          message: data.message,
        },
        async: false,
      }).responseText;
    }

    // 根据 autoName 执行不同操作
    if (autoName === "@reload") {
      location.reload(); // 刷新本页
      return;
    } else if (autoName === "@reloadTop") {
      // 刷新整页
      top.location.reload();
      return;
    } else if (autoName === "@closeModal") {
      // 关闭最后打开的一个模态框（并继续执行后续处理）
      closeModal();
    } else if (autoName === "@closeAllModal") {
      // 关闭全部模态框（并继续执行后续处理）
      closeAllModal();
    } else {
      // 默认进行页面跳转，如果有需要替换的变量，可以用 {} 标记替换
      var autoUrl = autoName;
      var hasError = false;
      var readyParams = autoUrl.match(/\{[^\}]+\}/g);
      if (readyParams) {
        var paramValues = [];
        // 判断在返回结果中是否存在对应数据
        for (var i = 0; i < readyParams.length; i++) {
          var param = readyParams[i].slice(1, -1);
          if (data[param]) {
            paramValues.push(data[param]);
          } else {
            PNotify.error("变量 " + param + " 未找到数据");
            hasError = true;
            break;
          }
        }
        // 全部匹配则进行替换
        if (!hasError && readyParams.length === paramValues.length) {
          for (var i = 0; i < readyParams.length; i++) {
            autoUrl = autoUrl.replace(
              readyParams[i],
              encodeURIComponent(paramValues[i])
            );
          }
        }
      }
      // 无错误执行跳转操作
      if (!hasError) {
        location.href = autoUrl;
      }
      return;
    }

    // 如果有内容则提示操作信息
    if (showMessage && data && data.message) {
      PNotify.success(data.message);
    }

    // 如果有 appFunc，则执行
    if (appFunc) {
      appFunc();
    }
  };
}

/* 发起 Ajax Action 操作 */
function ajaxAction(url, params, customFunc) {
  $.ajax({
    dataType: "json",
    method: "POST",
    url: url,
    contentType: "application/json",
    data: JSON.stringify(params),
    success: getAjaxResultFunc(customFunc),
  });
}

/* 激活 Lightbox */
$(document).on("click", '[data-toggle="lightbox"]', function (event) {
  event.preventDefault();
  $(this).ekkoLightbox();
});

/* 激活超链接发起 Ajax 请求 */
$(document).on("click", 'a[data-ajax-submit="true"]', function (event) {
  event.preventDefault();
  var $a = $(this);
  var href = $a.attr("href");
  var ajaxBeforeFunc = $a.data("ajaxBeforeFunc");
  var ajaxAfterFunc = $a.data("ajaxAfterFunc");
  var allowSubmit = true;
  if (href) {
    if (ajaxBeforeFunc) {
      allowSubmit = eval(ajaxBeforeFunc + "()");
    }
    if (allowSubmit) {
      $.post(
        href,
        getAjaxResultFunc(ajaxAfterFunc ? eval(ajaxAfterFunc) : null),
        "json"
      );
    }
  }
});

/* 侧边导航菜单激活 */
$(document).ready(function () {
  $(".navbar-vertical .dropdown-toggle").click(function () {
    let $dropdownMenu = $(this).next(".dropdown-menu");
    if ($dropdownMenu.hasClass("show")) {
      $dropdownMenu.removeClass("show");
    } else {
      // 关闭其他打开的菜单，目前暂时不需要
      $(".navbar-vertical  .dropdown-menu").removeClass("show");
      $dropdownMenu.addClass("show");
    }
  });
});

/* jQuery Validation 补充验证方法 */
jQuery.validator.addMethod("lnutext", function(value, element) {
  return this.optional(element) || /^[A-Za-z0-9_]+$/.test(value);
}, "只允许包含：字母、数字、下划线");
