/*!
   Copyright 2010-2018 SpryMedia Ltd.

 This source file is free software, available under the following license:
   MIT license - http://datatables.net/license/mit

 This source file is distributed in the hope that it will be useful, but
 WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
 or FITNESS FOR A PARTICULAR PURPOSE. See the license files for details.

 For details please refer to: http://www.datatables.net
 FixedColumns 3.3.0
 ©2010-2018 SpryMedia Ltd - datatables.net/license
*/
var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.findInternal=function(c,g,e){c instanceof String&&(c=String(c));for(var q=c.length,l=0;l<q;l++){var u=c[l];if(g.call(e,u,l,c))return{i:l,v:u}}return{i:-1,v:void 0}};$jscomp.ASSUME_ES5=!1;$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.SIMPLE_FROUND_POLYFILL=!1;
$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(c,g,e){c!=Array.prototype&&c!=Object.prototype&&(c[g]=e.value)};$jscomp.getGlobal=function(c){return"undefined"!=typeof window&&window===c?c:"undefined"!=typeof global&&null!=global?global:c};$jscomp.global=$jscomp.getGlobal(this);
$jscomp.polyfill=function(c,g,e,q){if(g){e=$jscomp.global;c=c.split(".");for(q=0;q<c.length-1;q++){var l=c[q];l in e||(e[l]={});e=e[l]}c=c[c.length-1];q=e[c];g=g(q);g!=q&&null!=g&&$jscomp.defineProperty(e,c,{configurable:!0,writable:!0,value:g})}};$jscomp.polyfill("Array.prototype.find",function(c){return c?c:function(c,e){return $jscomp.findInternal(this,c,e).v}},"es6","es3");
(function(c){"function"===typeof define&&define.amd?define(["jquery","datatables.net"],function(g){return c(g,window,document)}):"object"===typeof exports?module.exports=function(g,e){g||(g=window);e&&e.fn.dataTable||(e=require("datatables.net")(g,e).$);return c(e,g,g.document)}:c(jQuery,window,document)})(function(c,g,e,q){var l=c.fn.dataTable,u,p=function(a,b){var d=this;if(this instanceof p){if(b===q||!0===b)b={};var h=c.fn.dataTable.camelToHungarian;h&&(h(p.defaults,p.defaults,!0),h(p.defaults,
b));a=(new c.fn.dataTable.Api(a)).settings()[0];this.s={dt:a,iTableColumns:a.aoColumns.length,aiOuterWidths:[],aiInnerWidths:[],rtl:"rtl"===c(a.nTable).css("direction")};this.dom={scroller:null,header:null,body:null,footer:null,grid:{wrapper:null,dt:null,left:{wrapper:null,head:null,body:null,foot:null},right:{wrapper:null,head:null,body:null,foot:null}},clone:{left:{header:null,body:null,footer:null},right:{header:null,body:null,footer:null}}};if(a._oFixedColumns)throw"FixedColumns already initialised on this table";
a._oFixedColumns=this;a._bInitComplete?this._fnConstruct(b):a.oApi._fnCallbackReg(a,"aoInitComplete",function(){d._fnConstruct(b)},"FixedColumns")}else alert("FixedColumns warning: FixedColumns must be initialised with the 'new' keyword.")};c.extend(p.prototype,{fnUpdate:function(){this._fnDraw(!0)},fnRedrawLayout:function(){this._fnColCalc();this._fnGridLayout();this.fnUpdate()},fnRecalculateHeight:function(a){delete a._DTTC_iHeight;a.style.height="auto"},fnSetRowHeight:function(a,b){a.style.height=
b+"px"},fnGetPosition:function(a){var b=this.s.dt.oInstance;if(c(a).parents(".DTFC_Cloned").length){if("tr"===a.nodeName.toLowerCase())return a=c(a).index(),b.fnGetPosition(c("tr",this.s.dt.nTBody)[a]);var d=c(a).index();a=c(a.parentNode).index();return[b.fnGetPosition(c("tr",this.s.dt.nTBody)[a]),d,b.oApi._fnVisibleToColumnIndex(this.s.dt,d)]}return b.fnGetPosition(a)},fnToFixedNode:function(a,b){var d;b<this.s.iLeftColumns?d=c(this.dom.clone.left.body).find("[data-dt-row="+a+"][data-dt-column="+
b+"]"):b>=this.s.iRightColumns&&(d=c(this.dom.clone.right.body).find("[data-dt-row="+a+"][data-dt-column="+b+"]"));return d&&d.length?d[0]:(new c.fn.dataTable.Api(this.s.dt)).cell(a,b).node()},_fnConstruct:function(a){var b=this;if("function"!=typeof this.s.dt.oInstance.fnVersionCheck||!0!==this.s.dt.oInstance.fnVersionCheck("1.8.0"))alert("FixedColumns "+p.VERSION+" required DataTables 1.8.0 or later. Please upgrade your DataTables installation");else if(""===this.s.dt.oScroll.sX)this.s.dt.oInstance.oApi._fnLog(this.s.dt,
1,"FixedColumns is not needed (no x-scrolling in DataTables enabled), so no action will be taken. Use 'FixedHeader' for column fixing when scrolling is not enabled");else{this.s=c.extend(!0,this.s,p.defaults,a);a=this.s.dt.oClasses;this.dom.grid.dt=c(this.s.dt.nTable).parents("div."+a.sScrollWrapper)[0];this.dom.scroller=c("div."+a.sScrollBody,this.dom.grid.dt)[0];this._fnColCalc();this._fnGridSetup();var d,h=!1;c(this.s.dt.nTableWrapper).on("mousedown.DTFC",function(a){0===a.button&&(h=!0,c(e).one("mouseup",
function(){h=!1}))});c(this.dom.scroller).on("mouseover.DTFC touchstart.DTFC",function(){h||(d="main")}).on("scroll.DTFC",function(a){!d&&a.originalEvent&&(d="main");"main"===d&&(0<b.s.iLeftColumns&&(b.dom.grid.left.liner.scrollTop=b.dom.scroller.scrollTop),0<b.s.iRightColumns&&(b.dom.grid.right.liner.scrollTop=b.dom.scroller.scrollTop))});var f="onwheel"in e.createElement("div")?"wheel.DTFC":"mousewheel.DTFC";if(0<b.s.iLeftColumns)c(b.dom.grid.left.liner).on("mouseover.DTFC touchstart.DTFC",function(){h||
(d="left")}).on("scroll.DTFC",function(a){!d&&a.originalEvent&&(d="left");"left"===d&&(b.dom.scroller.scrollTop=b.dom.grid.left.liner.scrollTop,0<b.s.iRightColumns&&(b.dom.grid.right.liner.scrollTop=b.dom.grid.left.liner.scrollTop))}).on(f,function(a){b.dom.scroller.scrollLeft-="wheel"===a.type?-a.originalEvent.deltaX:a.originalEvent.wheelDeltaX});if(0<b.s.iRightColumns)c(b.dom.grid.right.liner).on("mouseover.DTFC touchstart.DTFC",function(){h||(d="right")}).on("scroll.DTFC",function(a){!d&&a.originalEvent&&
(d="right");"right"===d&&(b.dom.scroller.scrollTop=b.dom.grid.right.liner.scrollTop,0<b.s.iLeftColumns&&(b.dom.grid.left.liner.scrollTop=b.dom.grid.right.liner.scrollTop))}).on(f,function(a){b.dom.scroller.scrollLeft-="wheel"===a.type?-a.originalEvent.deltaX:a.originalEvent.wheelDeltaX});c(g).on("resize.DTFC",function(){b._fnGridLayout.call(b)});var m=!0,k=c(this.s.dt.nTable);k.on("draw.dt.DTFC",function(){b._fnColCalc();b._fnDraw.call(b,m);m=!1}).on("column-sizing.dt.DTFC",function(){b._fnColCalc();
b._fnGridLayout(b)}).on("column-visibility.dt.DTFC",function(a,c,d,f,h){if(h===q||h)b._fnColCalc(),b._fnGridLayout(b),b._fnDraw(!0)}).on("select.dt.DTFC deselect.dt.DTFC",function(a,c,d,f){"dt"===a.namespace&&b._fnDraw(!1)}).on("destroy.dt.DTFC",function(){k.off(".DTFC");c(b.dom.scroller).off(".DTFC");c(g).off(".DTFC");c(b.s.dt.nTableWrapper).off(".DTFC");c(b.dom.grid.left.liner).off(".DTFC "+f);c(b.dom.grid.left.wrapper).remove();c(b.dom.grid.right.liner).off(".DTFC "+f);c(b.dom.grid.right.wrapper).remove()});
this._fnGridLayout();this.s.dt.oInstance.fnDraw(!1)}},_fnColCalc:function(){var a=this,b=0,d=0;this.s.aiInnerWidths=[];this.s.aiOuterWidths=[];c.each(this.s.dt.aoColumns,function(h,f){f=c(f.nTh);if(f.filter(":visible").length){var m=f.outerWidth();if(0===a.s.aiOuterWidths.length){var k=c(a.s.dt.nTable).css("border-left-width");m+="string"===typeof k&&-1===k.indexOf("px")?1:parseInt(k,10)}a.s.aiOuterWidths.length===a.s.dt.aoColumns.length-1&&(k=c(a.s.dt.nTable).css("border-right-width"),m+="string"===
typeof k&&-1===k.indexOf("px")?1:parseInt(k,10));a.s.aiOuterWidths.push(m);a.s.aiInnerWidths.push(f.width());h<a.s.iLeftColumns&&(b+=m);a.s.iTableColumns-a.s.iRightColumns<=h&&(d+=m)}else a.s.aiInnerWidths.push(0),a.s.aiOuterWidths.push(0)});this.s.iLeftWidth=b;this.s.iRightWidth=d},_fnGridSetup:function(){var a=this._fnDTOverflow();this.dom.body=this.s.dt.nTable;this.dom.header=this.s.dt.nTHead.parentNode;this.dom.header.parentNode.parentNode.style.position="relative";var b=c('<div class="DTFC_ScrollWrapper" style="position:relative; clear:both;"><div class="DTFC_LeftWrapper" style="position:absolute; top:0; left:0;" aria-hidden="true"><div class="DTFC_LeftHeadWrapper" style="position:relative; top:0; left:0; overflow:hidden;"></div><div class="DTFC_LeftBodyWrapper" style="position:relative; top:0; left:0; height:0; overflow:hidden;"><div class="DTFC_LeftBodyLiner" style="position:relative; top:0; left:0; overflow-y:scroll;"></div></div><div class="DTFC_LeftFootWrapper" style="position:relative; top:0; left:0; overflow:hidden;"></div></div><div class="DTFC_RightWrapper" style="position:absolute; top:0; right:0;" aria-hidden="true"><div class="DTFC_RightHeadWrapper" style="position:relative; top:0; left:0;"><div class="DTFC_RightHeadBlocker DTFC_Blocker" style="position:absolute; top:0; bottom:0;"></div></div><div class="DTFC_RightBodyWrapper" style="position:relative; top:0; left:0; height:0; overflow:hidden;"><div class="DTFC_RightBodyLiner" style="position:relative; top:0; left:0; overflow-y:scroll;"></div></div><div class="DTFC_RightFootWrapper" style="position:relative; top:0; left:0;"><div class="DTFC_RightFootBlocker DTFC_Blocker" style="position:absolute; top:0; bottom:0;"></div></div></div></div>')[0],
d=b.childNodes[0],h=b.childNodes[1];this.dom.grid.dt.parentNode.insertBefore(b,this.dom.grid.dt);b.appendChild(this.dom.grid.dt);this.dom.grid.wrapper=b;0<this.s.iLeftColumns&&(this.dom.grid.left.wrapper=d,this.dom.grid.left.head=d.childNodes[0],this.dom.grid.left.body=d.childNodes[1],this.dom.grid.left.liner=c("div.DTFC_LeftBodyLiner",b)[0],b.appendChild(d));if(0<this.s.iRightColumns){this.dom.grid.right.wrapper=h;this.dom.grid.right.head=h.childNodes[0];this.dom.grid.right.body=h.childNodes[1];
this.dom.grid.right.liner=c("div.DTFC_RightBodyLiner",b)[0];h.style.right=a.bar+"px";var f=c("div.DTFC_RightHeadBlocker",b)[0];f.style.width=a.bar+"px";f.style.right=-a.bar+"px";this.dom.grid.right.headBlock=f;f=c("div.DTFC_RightFootBlocker",b)[0];f.style.width=a.bar+"px";f.style.right=-a.bar+"px";this.dom.grid.right.footBlock=f;b.appendChild(h)}this.s.dt.nTFoot&&(this.dom.footer=this.s.dt.nTFoot.parentNode,0<this.s.iLeftColumns&&(this.dom.grid.left.foot=d.childNodes[2]),0<this.s.iRightColumns&&(this.dom.grid.right.foot=
h.childNodes[2]));this.s.rtl&&c("div.DTFC_RightHeadBlocker",b).css({left:-a.bar+"px",right:""})},_fnGridLayout:function(){var a=this,b=this.dom.grid;c(b.wrapper).width();var d=this.s.dt.nTable.parentNode.offsetHeight,h=this.s.dt.nTable.parentNode.parentNode.offsetHeight,f=this._fnDTOverflow(),m=this.s.iLeftWidth,k=this.s.iRightWidth,x="rtl"===c(this.dom.body).css("direction"),w=function(b,d){f.bar?a._firefoxScrollError()?34<c(b).height()&&(b.style.width=d+f.bar+"px"):b.style.width=d+f.bar+"px":(b.style.width=
d+20+"px",b.style.paddingRight="20px",b.style.boxSizing="border-box")};f.x&&(d-=f.bar);b.wrapper.style.height=h+"px";0<this.s.iLeftColumns&&(h=b.left.wrapper,h.style.width=m+"px",h.style.height="1px",x?(h.style.left="",h.style.right=0):(h.style.left=0,h.style.right=""),b.left.body.style.height=d+"px",b.left.foot&&(b.left.foot.style.top=(f.x?f.bar:0)+"px"),w(b.left.liner,m),b.left.liner.style.height=d+"px",b.left.liner.style.maxHeight=d+"px");0<this.s.iRightColumns&&(h=b.right.wrapper,h.style.width=
k+"px",h.style.height="1px",this.s.rtl?(h.style.left=f.y?f.bar+"px":0,h.style.right=""):(h.style.left="",h.style.right=f.y?f.bar+"px":0),b.right.body.style.height=d+"px",b.right.foot&&(b.right.foot.style.top=(f.x?f.bar:0)+"px"),w(b.right.liner,k),b.right.liner.style.height=d+"px",b.right.liner.style.maxHeight=d+"px",b.right.headBlock.style.display=f.y?"block":"none",b.right.footBlock.style.display=f.y?"block":"none")},_fnDTOverflow:function(){var a=this.s.dt.nTable,b=a.parentNode,c={x:!1,y:!1,bar:this.s.dt.oScroll.iBarWidth};
a.offsetWidth>b.clientWidth&&(c.x=!0);a.offsetHeight>b.clientHeight&&(c.y=!0);return c},_fnDraw:function(a){this._fnGridLayout();this._fnCloneLeft(a);this._fnCloneRight(a);null!==this.s.fnDrawCallback&&this.s.fnDrawCallback.call(this,this.dom.clone.left,this.dom.clone.right);c(this).trigger("draw.dtfc",{leftClone:this.dom.clone.left,rightClone:this.dom.clone.right})},_fnCloneRight:function(a){if(!(0>=this.s.iRightColumns)){var b,c=[];for(b=this.s.iTableColumns-this.s.iRightColumns;b<this.s.iTableColumns;b++)this.s.dt.aoColumns[b].bVisible&&
c.push(b);this._fnClone(this.dom.clone.right,this.dom.grid.right,c,a)}},_fnCloneLeft:function(a){if(!(0>=this.s.iLeftColumns)){var b,c=[];for(b=0;b<this.s.iLeftColumns;b++)this.s.dt.aoColumns[b].bVisible&&c.push(b);this._fnClone(this.dom.clone.left,this.dom.grid.left,c,a)}},_fnCopyLayout:function(a,b,d){for(var h=[],f=[],m=[],k=0,x=a.length;k<x;k++){var w=[];w.nTr=c(a[k].nTr).clone(d,!1)[0];for(var e=0,n=this.s.iTableColumns;e<n;e++)if(-1!==c.inArray(e,b)){var g=c.inArray(a[k][e].cell,m);-1===g?(g=
c(a[k][e].cell).clone(d,!1)[0],f.push(g),m.push(a[k][e].cell),w.push({cell:g,unique:a[k][e].unique})):w.push({cell:f[g],unique:a[k][e].unique})}h.push(w)}return h},_fnClone:function(a,b,d,h){var f=this,m,k,e=this.s.dt;if(h){c(a.header).remove();a.header=c(this.dom.header).clone(!0,!1)[0];a.header.className+=" DTFC_Cloned";a.header.style.width="100%";b.head.appendChild(a.header);var g=this._fnCopyLayout(e.aoHeader,d,!0);var l=c(">thead",a.header);l.empty();var n=0;for(m=g.length;n<m;n++)l[0].appendChild(g[n].nTr);
e.oApi._fnDrawHead(e,g,!0)}else{g=this._fnCopyLayout(e.aoHeader,d,!1);var p=[];e.oApi._fnDetectHeader(p,c(">thead",a.header)[0]);n=0;for(m=g.length;n<m;n++){var t=0;for(l=g[n].length;t<l;t++)p[n][t].cell.className=g[n][t].cell.className,c("span.DataTables_sort_icon",p[n][t].cell).each(function(){this.className=c("span.DataTables_sort_icon",g[n][t].cell)[0].className})}}this._fnEqualiseHeights("thead",this.dom.header,a.header);"auto"==this.s.sHeightMatch&&c(">tbody>tr",f.dom.body).css("height","auto");
null!==a.body&&(c(a.body).remove(),a.body=null);a.body=c(this.dom.body).clone(!0)[0];a.body.className+=" DTFC_Cloned";a.body.style.paddingBottom=e.oScroll.iBarWidth+"px";a.body.style.marginBottom=2*e.oScroll.iBarWidth+"px";null!==a.body.getAttribute("id")&&a.body.removeAttribute("id");c(">thead>tr",a.body).empty();c(">tfoot",a.body).remove();var u=c("tbody",a.body)[0];c(u).empty();if(0<e.aiDisplay.length){m=c(">thead>tr",a.body)[0];for(k=0;k<d.length;k++){var v=d[k];var r=c(e.aoColumns[v].nTh).clone(!0)[0];
r.innerHTML="";l=r.style;l.paddingTop="0";l.paddingBottom="0";l.borderTopWidth="0";l.borderBottomWidth="0";l.height=0;l.width=f.s.aiInnerWidths[v]+"px";m.appendChild(r)}c(">tbody>tr",f.dom.body).each(function(a){a=!1===f.s.dt.oFeatures.bServerSide?f.s.dt.aiDisplay[f.s.dt._iDisplayStart+a]:a;var b=f.s.dt.aoData[a].anCells||c(this).children("td, th"),e=this.cloneNode(!1);e.removeAttribute("id");e.setAttribute("data-dt-row",a);for(k=0;k<d.length;k++)v=d[k],0<b.length&&(r=c(b[v]).clone(!0,!0)[0],r.removeAttribute("id"),
r.setAttribute("data-dt-row",a),r.setAttribute("data-dt-column",v),e.appendChild(r));u.appendChild(e)})}else c(">tbody>tr",f.dom.body).each(function(a){r=this.cloneNode(!0);r.className+=" DTFC_NoData";c("td",r).html("");u.appendChild(r)});a.body.style.width="100%";a.body.style.margin="0";a.body.style.padding="0";e.oScroller!==q&&(m=e.oScroller.dom.force,b.forcer?b.forcer.style.height=m.style.height:(b.forcer=m.cloneNode(!0),b.liner.appendChild(b.forcer)));b.liner.appendChild(a.body);this._fnEqualiseHeights("tbody",
f.dom.body,a.body);if(null!==e.nTFoot){if(h){null!==a.footer&&a.footer.parentNode.removeChild(a.footer);a.footer=c(this.dom.footer).clone(!0,!0)[0];a.footer.className+=" DTFC_Cloned";a.footer.style.width="100%";b.foot.appendChild(a.footer);g=this._fnCopyLayout(e.aoFooter,d,!0);b=c(">tfoot",a.footer);b.empty();n=0;for(m=g.length;n<m;n++)b[0].appendChild(g[n].nTr);e.oApi._fnDrawHead(e,g,!0)}else for(g=this._fnCopyLayout(e.aoFooter,d,!1),b=[],e.oApi._fnDetectHeader(b,c(">tfoot",a.footer)[0]),n=0,m=g.length;n<
m;n++)for(t=0,l=g[n].length;t<l;t++)b[n][t].cell.className=g[n][t].cell.className;this._fnEqualiseHeights("tfoot",this.dom.footer,a.footer)}b=e.oApi._fnGetUniqueThs(e,c(">thead",a.header)[0]);c(b).each(function(a){v=d[a];this.style.width=f.s.aiInnerWidths[v]+"px"});null!==f.s.dt.nTFoot&&(b=e.oApi._fnGetUniqueThs(e,c(">tfoot",a.footer)[0]),c(b).each(function(a){v=d[a];this.style.width=f.s.aiInnerWidths[v]+"px"}))},_fnGetTrNodes:function(a){for(var b=[],c=0,e=a.childNodes.length;c<e;c++)"TR"==a.childNodes[c].nodeName.toUpperCase()&&
b.push(a.childNodes[c]);return b},_fnEqualiseHeights:function(a,b,d){if("none"!=this.s.sHeightMatch||"thead"===a||"tfoot"===a){var e=b.getElementsByTagName(a)[0];d=d.getElementsByTagName(a)[0];a=c(">"+a+">tr:eq(0)",b).children(":first");a.outerHeight();a.height();e=this._fnGetTrNodes(e);b=this._fnGetTrNodes(d);var f=[];d=0;for(a=b.length;d<a;d++){var g=e[d].offsetHeight;var k=b[d].offsetHeight;g=k>g?k:g;"semiauto"==this.s.sHeightMatch&&(e[d]._DTTC_iHeight=g);f.push(g)}d=0;for(a=b.length;d<a;d++)b[d].style.height=
f[d]+"px",e[d].style.height=f[d]+"px"}},_firefoxScrollError:function(){if(u===q){var a=c("<div/>").css({position:"absolute",top:0,left:0,height:10,width:50,overflow:"scroll"}).appendTo("body");u=a[0].clientWidth===a[0].offsetWidth&&0!==this._fnDTOverflow().bar;a.remove()}return u}});p.defaults={iLeftColumns:1,iRightColumns:0,fnDrawCallback:null,sHeightMatch:"semiauto"};p.version="3.3.0";l.Api.register("fixedColumns()",function(){return this});l.Api.register("fixedColumns().update()",function(){return this.iterator("table",
function(a){a._oFixedColumns&&a._oFixedColumns.fnUpdate()})});l.Api.register("fixedColumns().relayout()",function(){return this.iterator("table",function(a){a._oFixedColumns&&a._oFixedColumns.fnRedrawLayout()})});l.Api.register("rows().recalcHeight()",function(){return this.iterator("row",function(a,b){a._oFixedColumns&&a._oFixedColumns.fnRecalculateHeight(this.row(b).node())})});l.Api.register("fixedColumns().rowIndex()",function(a){a=c(a);return a.parents(".DTFC_Cloned").length?this.rows({page:"current"}).indexes()[a.index()]:
this.row(a).index()});l.Api.register("fixedColumns().cellIndex()",function(a){a=c(a);if(a.parents(".DTFC_Cloned").length){var b=a.parent().index();b=this.rows({page:"current"}).indexes()[b];a=a.parents(".DTFC_LeftWrapper").length?a.index():this.columns().flatten().length-this.context[0]._oFixedColumns.s.iRightColumns+a.index();return{row:b,column:this.column.index("toData",a),columnVisible:a}}return this.cell(a).index()});l.Api.registerPlural("cells().fixedNodes()","cell().fixedNode()",function(){return this.iterator("cell",
function(a,b,c){return a._oFixedColumns?a._oFixedColumns.fnToFixedNode(b,c):this.node()},1)});c(e).on("init.dt.fixedColumns",function(a,b){if("dt"===a.namespace){a=b.oInit.fixedColumns;var d=l.defaults.fixedColumns;if(a||d)d=c.extend({},a,d),!1!==a&&new p(b,d)}});c.fn.dataTable.FixedColumns=p;return c.fn.DataTable.FixedColumns=p});
/*!
 Bootstrap 4 styling wrapper for FixedColumns
 ©2018 SpryMedia Ltd - datatables.net/license
*/
(function(c){"function"===typeof define&&define.amd?define(["jquery","datatables.net-bs4","datatables.net-fixedcolumns"],function(a){return c(a,window,document)}):"object"===typeof exports?module.exports=function(a,b){a||(a=window);b&&b.fn.dataTable||(b=require("datatables.net-bs4")(a,b).$);b.fn.dataTable.FixedColumns||require("datatables.net-fixedcolumns")(a,b);return c(b,a,a.document)}:c(jQuery,window,document)})(function(c,a,b,d){return c.fn.dataTable});
/*!
   Copyright 2009-2019 SpryMedia Ltd.

 This source file is free software, available under the following license:
   MIT license - http://datatables.net/license/mit

 This source file is distributed in the hope that it will be useful, but
 WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
 or FITNESS FOR A PARTICULAR PURPOSE. See the license files for details.

 For details please refer to: http://www.datatables.net
 FixedHeader 3.1.6
 ©2009-2019 SpryMedia Ltd - datatables.net/license
*/
var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.findInternal=function(c,f,g){c instanceof String&&(c=String(c));for(var l=c.length,h=0;h<l;h++){var n=c[h];if(f.call(g,n,h,c))return{i:h,v:n}}return{i:-1,v:void 0}};$jscomp.ASSUME_ES5=!1;$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.SIMPLE_FROUND_POLYFILL=!1;
$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(c,f,g){c!=Array.prototype&&c!=Object.prototype&&(c[f]=g.value)};$jscomp.getGlobal=function(c){return"undefined"!=typeof window&&window===c?c:"undefined"!=typeof global&&null!=global?global:c};$jscomp.global=$jscomp.getGlobal(this);
$jscomp.polyfill=function(c,f,g,l){if(f){g=$jscomp.global;c=c.split(".");for(l=0;l<c.length-1;l++){var h=c[l];h in g||(g[h]={});g=g[h]}c=c[c.length-1];l=g[c];f=f(l);f!=l&&null!=f&&$jscomp.defineProperty(g,c,{configurable:!0,writable:!0,value:f})}};$jscomp.polyfill("Array.prototype.find",function(c){return c?c:function(c,g){return $jscomp.findInternal(this,c,g).v}},"es6","es3");
(function(c){"function"===typeof define&&define.amd?define(["jquery","datatables.net"],function(f){return c(f,window,document)}):"object"===typeof exports?module.exports=function(f,g){f||(f=window);g&&g.fn.dataTable||(g=require("datatables.net")(f,g).$);return c(g,f,f.document)}:c(jQuery,window,document)})(function(c,f,g,l){var h=c.fn.dataTable,n=0,m=function(a,b){if(!(this instanceof m))throw"FixedHeader must be initialised with the 'new' keyword.";!0===b&&(b={});a=new h.Api(a);this.c=c.extend(!0,
{},m.defaults,b);this.s={dt:a,position:{theadTop:0,tbodyTop:0,tfootTop:0,tfootBottom:0,width:0,left:0,tfootHeight:0,theadHeight:0,windowHeight:c(f).height(),visible:!0},headerMode:null,footerMode:null,autoWidth:a.settings()[0].oFeatures.bAutoWidth,namespace:".dtfc"+n++,scrollLeft:{header:-1,footer:-1},enable:!0};this.dom={floatingHeader:null,thead:c(a.table().header()),tbody:c(a.table().body()),tfoot:c(a.table().footer()),header:{host:null,floating:null,placeholder:null},footer:{host:null,floating:null,
placeholder:null}};this.dom.header.host=this.dom.thead.parent();this.dom.footer.host=this.dom.tfoot.parent();a=a.settings()[0];if(a._fixedHeader)throw"FixedHeader already initialised on table "+a.nTable.id;a._fixedHeader=this;this._constructor()};c.extend(m.prototype,{destroy:function(){this.s.dt.off(".dtfc");c(f).off(this.s.namespace);this.c.header&&this._modeChange("in-place","header",!0);this.c.footer&&this.dom.tfoot.length&&this._modeChange("in-place","footer",!0)},enable:function(a,b){this.s.enable=
a;if(b||b===l)this._positions(),this._scroll(!0)},enabled:function(){return this.s.enable},headerOffset:function(a){a!==l&&(this.c.headerOffset=a,this.update());return this.c.headerOffset},footerOffset:function(a){a!==l&&(this.c.footerOffset=a,this.update());return this.c.footerOffset},update:function(){var a=this.s.dt.table().node();c(a).is(":visible")?this.enable(!0,!1):this.enable(!1,!1);this._positions();this._scroll(!0)},_constructor:function(){var a=this,b=this.s.dt;c(f).on("scroll"+this.s.namespace,
function(){a._scroll()}).on("resize"+this.s.namespace,h.util.throttle(function(){a.s.position.windowHeight=c(f).height();a.update()},50));var e=c(".fh-fixedHeader");!this.c.headerOffset&&e.length&&(this.c.headerOffset=e.outerHeight());e=c(".fh-fixedFooter");!this.c.footerOffset&&e.length&&(this.c.footerOffset=e.outerHeight());b.on("column-reorder.dt.dtfc column-visibility.dt.dtfc draw.dt.dtfc column-sizing.dt.dtfc responsive-display.dt.dtfc",function(){a.update()});b.on("destroy.dtfc",function(){a.destroy()});
this._positions();this._scroll()},_clone:function(a,b){var e=this.s.dt,d=this.dom[a],k="header"===a?this.dom.thead:this.dom.tfoot;!b&&d.floating?d.floating.removeClass("fixedHeader-floating fixedHeader-locked"):(d.floating&&(d.placeholder.remove(),this._unsize(a),d.floating.children().detach(),d.floating.remove()),d.floating=c(e.table().node().cloneNode(!1)).css("table-layout","fixed").attr("aria-hidden","true").removeAttr("id").append(k).appendTo("body"),d.placeholder=k.clone(!1),d.placeholder.find("*[id]").removeAttr("id"),
d.host.prepend(d.placeholder),this._matchWidths(d.placeholder,d.floating))},_matchWidths:function(a,b){var e=function(b){return c(b,a).map(function(){return c(this).width()}).toArray()},d=function(a,d){c(a,b).each(function(a){c(this).css({width:d[a],minWidth:d[a]})})},k=e("th");e=e("td");d("th",k);d("td",e)},_unsize:function(a){var b=this.dom[a].floating;b&&("footer"===a||"header"===a&&!this.s.autoWidth)?c("th, td",b).css({width:"",minWidth:""}):b&&"header"===a&&c("th, td",b).css("min-width","")},
_horizontal:function(a,b){var c=this.dom[a],d=this.s.position,k=this.s.scrollLeft;c.floating&&k[a]!==b&&(c.floating.css("left",d.left-b),k[a]=b)},_modeChange:function(a,b,e){var d=this.dom[b],k=this.s.position,f=this.dom["footer"===b?"tfoot":"thead"],h=c.contains(f[0],g.activeElement)?g.activeElement:null;h&&h.blur();"in-place"===a?(d.placeholder&&(d.placeholder.remove(),d.placeholder=null),this._unsize(b),"header"===b?d.host.prepend(f):d.host.append(f),d.floating&&(d.floating.remove(),d.floating=
null)):"in"===a?(this._clone(b,e),d.floating.addClass("fixedHeader-floating").css("header"===b?"top":"bottom",this.c[b+"Offset"]).css("left",k.left+"px").css("width",k.width+"px"),"footer"===b&&d.floating.css("top","")):"below"===a?(this._clone(b,e),d.floating.addClass("fixedHeader-locked").css("top",k.tfootTop-k.theadHeight).css("left",k.left+"px").css("width",k.width+"px")):"above"===a&&(this._clone(b,e),d.floating.addClass("fixedHeader-locked").css("top",k.tbodyTop).css("left",k.left+"px").css("width",
k.width+"px"));h&&h!==g.activeElement&&setTimeout(function(){h.focus()},10);this.s.scrollLeft.header=-1;this.s.scrollLeft.footer=-1;this.s[b+"Mode"]=a},_positions:function(){var a=this.s.dt.table(),b=this.s.position,e=this.dom;a=c(a.node());var d=a.children("thead"),k=a.children("tfoot");e=e.tbody;b.visible=a.is(":visible");b.width=a.outerWidth();b.left=a.offset().left;b.theadTop=d.offset().top;b.tbodyTop=e.offset().top;b.tbodyHeight=e.outerHeight();b.theadHeight=b.tbodyTop-b.theadTop;k.length?(b.tfootTop=
k.offset().top,b.tfootBottom=b.tfootTop+k.outerHeight(),b.tfootHeight=b.tfootBottom-b.tfootTop):(b.tfootTop=b.tbodyTop+e.outerHeight(),b.tfootBottom=b.tfootTop,b.tfootHeight=b.tfootTop)},_scroll:function(a){var b=c(g).scrollTop(),e=c(g).scrollLeft(),d=this.s.position,k;if(this.c.header){var f=this.s.enable?!d.visible||b<=d.theadTop-this.c.headerOffset?"in-place":b<=d.tfootTop-d.theadHeight-this.c.headerOffset?"in":"below":"in-place";(a||f!==this.s.headerMode)&&this._modeChange(f,"header",a);this._horizontal("header",
e)}this.c.footer&&this.dom.tfoot.length&&(this.s.enable&&(k=!d.visible||b+d.windowHeight>=d.tfootBottom+this.c.footerOffset?"in-place":d.windowHeight+b>d.tbodyTop+d.tfootHeight+this.c.footerOffset?"in":"above"),(a||k!==this.s.footerMode)&&this._modeChange(k,"footer",a),this._horizontal("footer",e))}});m.version="3.1.6";m.defaults={header:!0,footer:!1,headerOffset:0,footerOffset:0};c.fn.dataTable.FixedHeader=m;c.fn.DataTable.FixedHeader=m;c(g).on("init.dt.dtfh",function(a,b,e){"dt"===a.namespace&&
(a=b.oInit.fixedHeader,e=h.defaults.fixedHeader,!a&&!e||b._fixedHeader||(e=c.extend({},e,a),!1!==a&&new m(b,e)))});h.Api.register("fixedHeader()",function(){});h.Api.register("fixedHeader.adjust()",function(){return this.iterator("table",function(a){(a=a._fixedHeader)&&a.update()})});h.Api.register("fixedHeader.enable()",function(a){return this.iterator("table",function(b){b=b._fixedHeader;a=a!==l?a:!0;b&&a!==b.enabled()&&b.enable(a)})});h.Api.register("fixedHeader.enabled()",function(){return this.context.length&&
fh?fh.enabled():!1});h.Api.register("fixedHeader.disable()",function(){return this.iterator("table",function(a){(a=a._fixedHeader)&&a.enabled()&&a.enable(!1)})});c.each(["header","footer"],function(a,b){h.Api.register("fixedHeader."+b+"Offset()",function(a){var c=this.context;return a===l?c.length&&c[0]._fixedHeader?c[0]._fixedHeader[b+"Offset"]():l:this.iterator("table",function(c){if(c=c._fixedHeader)c[b+"Offset"](a)})})});return m});
/*!
 Bootstrap 4 styling wrapper for FixedHeader
 ©2018 SpryMedia Ltd - datatables.net/license
*/
(function(c){"function"===typeof define&&define.amd?define(["jquery","datatables.net-bs4","datatables.net-fixedheader"],function(a){return c(a,window,document)}):"object"===typeof exports?module.exports=function(a,b){a||(a=window);b&&b.fn.dataTable||(b=require("datatables.net-bs4")(a,b).$);b.fn.dataTable.FixedHeader||require("datatables.net-fixedheader")(a,b);return c(b,a,a.document)}:c(jQuery,window,document)})(function(c,a,b,d){return c.fn.dataTable});
/*!
   Copyright 2010-2019 SpryMedia Ltd.

 This source file is free software, available under the following license:
   MIT license - http://datatables.net/license/mit

 This source file is distributed in the hope that it will be useful, but
 WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
 or FITNESS FOR A PARTICULAR PURPOSE. See the license files for details.

 For details please refer to: http://www.datatables.net
 ColReorder 1.5.2
 ©2010-2019 SpryMedia Ltd - datatables.net/license
*/
(function(d){"function"===typeof define&&define.amd?define(["jquery","datatables.net"],function(t){return d(t,window,document)}):"object"===typeof exports?module.exports=function(t,r){t||(t=window);r&&r.fn.dataTable||(r=require("datatables.net")(t,r).$);return d(r,t,t.document)}:d(jQuery,window,document)})(function(d,t,r,w){function v(a){for(var b=[],c=0,d=a.length;c<d;c++)b[a[c]]=c;return b}function u(a,b,c){b=a.splice(b,1)[0];a.splice(c,0,b)}function x(a,b,c){for(var d=[],h=0,f=a.childNodes.length;h<
f;h++)1==a.childNodes[h].nodeType&&d.push(a.childNodes[h]);b=d[b];null!==c?a.insertBefore(b,d[c]):a.appendChild(b)}var y=d.fn.dataTable;d.fn.dataTableExt.oApi.fnColReorder=function(a,b,c,g,h){var f,p,n=a.aoColumns.length;var q=function(a,b,c){if(a[b]&&"function"!==typeof a[b]){var e=a[b].split("."),d=e.shift();isNaN(1*d)||(a[b]=c[1*d]+"."+e.join("."))}};if(b!=c)if(0>b||b>=n)this.oApi._fnLog(a,1,"ColReorder 'from' index is out of bounds: "+b);else if(0>c||c>=n)this.oApi._fnLog(a,1,"ColReorder 'to' index is out of bounds: "+
c);else{var l=[];var e=0;for(f=n;e<f;e++)l[e]=e;u(l,b,c);var k=v(l);e=0;for(f=a.aaSorting.length;e<f;e++)a.aaSorting[e][0]=k[a.aaSorting[e][0]];if(null!==a.aaSortingFixed)for(e=0,f=a.aaSortingFixed.length;e<f;e++)a.aaSortingFixed[e][0]=k[a.aaSortingFixed[e][0]];e=0;for(f=n;e<f;e++){var m=a.aoColumns[e];l=0;for(p=m.aDataSort.length;l<p;l++)m.aDataSort[l]=k[m.aDataSort[l]];m.idx=k[m.idx]}d.each(a.aLastSort,function(b,c){a.aLastSort[b].src=k[c.src]});e=0;for(f=n;e<f;e++)m=a.aoColumns[e],"number"==typeof m.mData?
m.mData=k[m.mData]:d.isPlainObject(m.mData)&&(q(m.mData,"_",k),q(m.mData,"filter",k),q(m.mData,"sort",k),q(m.mData,"type",k));if(a.aoColumns[b].bVisible){q=this.oApi._fnColumnIndexToVisible(a,b);p=null;for(e=c<b?c:c+1;null===p&&e<n;)p=this.oApi._fnColumnIndexToVisible(a,e),e++;l=a.nTHead.getElementsByTagName("tr");e=0;for(f=l.length;e<f;e++)x(l[e],q,p);if(null!==a.nTFoot)for(l=a.nTFoot.getElementsByTagName("tr"),e=0,f=l.length;e<f;e++)x(l[e],q,p);e=0;for(f=a.aoData.length;e<f;e++)null!==a.aoData[e].nTr&&
x(a.aoData[e].nTr,q,p)}u(a.aoColumns,b,c);e=0;for(f=n;e<f;e++)a.oApi._fnColumnOptions(a,e,{});u(a.aoPreSearchCols,b,c);e=0;for(f=a.aoData.length;e<f;e++){p=a.aoData[e];if(m=p.anCells)for(u(m,b,c),l=0,q=m.length;l<q;l++)m[l]&&m[l]._DT_CellIndex&&(m[l]._DT_CellIndex.column=l);"dom"!==p.src&&d.isArray(p._aData)&&u(p._aData,b,c)}e=0;for(f=a.aoHeader.length;e<f;e++)u(a.aoHeader[e],b,c);if(null!==a.aoFooter)for(e=0,f=a.aoFooter.length;e<f;e++)u(a.aoFooter[e],b,c);(h||h===w)&&d.fn.dataTable.Api(a).rows().invalidate();
e=0;for(f=n;e<f;e++)d(a.aoColumns[e].nTh).off(".DT"),this.oApi._fnSortAttachListener(a,a.aoColumns[e].nTh,e);d(a.oInstance).trigger("column-reorder.dt",[a,{from:b,to:c,mapping:k,drop:g,iFrom:b,iTo:c,aiInvertMapping:k}])}};var k=function(a,b){a=(new d.fn.dataTable.Api(a)).settings()[0];if(a._colReorder)return a._colReorder;!0===b&&(b={});var c=d.fn.dataTable.camelToHungarian;c&&(c(k.defaults,k.defaults,!0),c(k.defaults,b||{}));this.s={dt:null,enable:null,init:d.extend(!0,{},k.defaults,b),fixed:0,fixedRight:0,
reorderCallback:null,mouse:{startX:-1,startY:-1,offsetX:-1,offsetY:-1,target:-1,targetIndex:-1,fromIndex:-1},aoTargets:[]};this.dom={drag:null,pointer:null};this.s.enable=this.s.init.bEnable;this.s.dt=a;this.s.dt._colReorder=this;this._fnConstruct();return this};d.extend(k.prototype,{fnEnable:function(a){if(!1===a)return fnDisable();this.s.enable=!0},fnDisable:function(){this.s.enable=!1},fnReset:function(){this._fnOrderColumns(this.fnOrder());return this},fnGetCurrentOrder:function(){return this.fnOrder()},
fnOrder:function(a,b){var c=[],g,h=this.s.dt.aoColumns;if(a===w){b=0;for(g=h.length;b<g;b++)c.push(h[b]._ColReorder_iOrigCol);return c}if(b){h=this.fnOrder();b=0;for(g=a.length;b<g;b++)c.push(d.inArray(a[b],h));a=c}this._fnOrderColumns(v(a));return this},fnTranspose:function(a,b){b||(b="toCurrent");var c=this.fnOrder(),g=this.s.dt.aoColumns;return"toCurrent"===b?d.isArray(a)?d.map(a,function(a){return d.inArray(a,c)}):d.inArray(a,c):d.isArray(a)?d.map(a,function(a){return g[a]._ColReorder_iOrigCol}):
g[a]._ColReorder_iOrigCol},_fnConstruct:function(){var a=this,b=this.s.dt.aoColumns.length,c=this.s.dt.nTable,g;this.s.init.iFixedColumns&&(this.s.fixed=this.s.init.iFixedColumns);this.s.init.iFixedColumnsLeft&&(this.s.fixed=this.s.init.iFixedColumnsLeft);this.s.fixedRight=this.s.init.iFixedColumnsRight?this.s.init.iFixedColumnsRight:0;this.s.init.fnReorderCallback&&(this.s.reorderCallback=this.s.init.fnReorderCallback);for(g=0;g<b;g++)g>this.s.fixed-1&&g<b-this.s.fixedRight&&this._fnMouseListener(g,
this.s.dt.aoColumns[g].nTh),this.s.dt.aoColumns[g]._ColReorder_iOrigCol=g;this.s.dt.oApi._fnCallbackReg(this.s.dt,"aoStateSaveParams",function(b,c){a._fnStateSave.call(a,c)},"ColReorder_State");var h=null;this.s.init.aiOrder&&(h=this.s.init.aiOrder.slice());this.s.dt.oLoadedState&&"undefined"!=typeof this.s.dt.oLoadedState.ColReorder&&this.s.dt.oLoadedState.ColReorder.length==this.s.dt.aoColumns.length&&(h=this.s.dt.oLoadedState.ColReorder);if(h)if(a.s.dt._bInitComplete)b=v(h),a._fnOrderColumns.call(a,
b);else{var f=!1;d(c).on("draw.dt.colReorder",function(){if(!a.s.dt._bInitComplete&&!f){f=!0;var b=v(h);a._fnOrderColumns.call(a,b)}})}else this._fnSetColumnIndexes();d(c).on("destroy.dt.colReorder",function(){d(c).off("destroy.dt.colReorder draw.dt.colReorder");d.each(a.s.dt.aoColumns,function(a,b){d(b.nTh).off(".ColReorder");d(b.nTh).removeAttr("data-column-index")});a.s.dt._colReorder=null;a.s=null})},_fnOrderColumns:function(a){var b=!1;if(a.length!=this.s.dt.aoColumns.length)this.s.dt.oInstance.oApi._fnLog(this.s.dt,
1,"ColReorder - array reorder does not match known number of columns. Skipping.");else{for(var c=0,g=a.length;c<g;c++){var h=d.inArray(c,a);c!=h&&(u(a,h,c),this.s.dt.oInstance.fnColReorder(h,c,!0,!1),b=!0)}this._fnSetColumnIndexes();b&&(d.fn.dataTable.Api(this.s.dt).rows().invalidate(),""===this.s.dt.oScroll.sX&&""===this.s.dt.oScroll.sY||this.s.dt.oInstance.fnAdjustColumnSizing(!1),this.s.dt.oInstance.oApi._fnSaveState(this.s.dt),null!==this.s.reorderCallback&&this.s.reorderCallback.call(this))}},
_fnStateSave:function(a){var b,c,g=this.s.dt.aoColumns;a.ColReorder=[];if(a.aaSorting){for(b=0;b<a.aaSorting.length;b++)a.aaSorting[b][0]=g[a.aaSorting[b][0]]._ColReorder_iOrigCol;var h=d.extend(!0,[],a.aoSearchCols);b=0;for(c=g.length;b<c;b++){var f=g[b]._ColReorder_iOrigCol;a.aoSearchCols[f]=h[b];a.abVisCols[f]=g[b].bVisible;a.ColReorder.push(f)}}else if(a.order){for(b=0;b<a.order.length;b++)a.order[b][0]=g[a.order[b][0]]._ColReorder_iOrigCol;h=d.extend(!0,[],a.columns);b=0;for(c=g.length;b<c;b++)f=
g[b]._ColReorder_iOrigCol,a.columns[f]=h[b],a.ColReorder.push(f)}},_fnMouseListener:function(a,b){var c=this;d(b).on("mousedown.ColReorder",function(a){c.s.enable&&1===a.which&&c._fnMouseDown.call(c,a,b)}).on("touchstart.ColReorder",function(a){c.s.enable&&c._fnMouseDown.call(c,a,b)})},_fnMouseDown:function(a,b){var c=this,g=d(a.target).closest("th, td").offset();b=parseInt(d(b).attr("data-column-index"),10);b!==w&&(this.s.mouse.startX=this._fnCursorPosition(a,"pageX"),this.s.mouse.startY=this._fnCursorPosition(a,
"pageY"),this.s.mouse.offsetX=this._fnCursorPosition(a,"pageX")-g.left,this.s.mouse.offsetY=this._fnCursorPosition(a,"pageY")-g.top,this.s.mouse.target=this.s.dt.aoColumns[b].nTh,this.s.mouse.targetIndex=b,this.s.mouse.fromIndex=b,this._fnRegions(),d(r).on("mousemove.ColReorder touchmove.ColReorder",function(a){c._fnMouseMove.call(c,a)}).on("mouseup.ColReorder touchend.ColReorder",function(a){c._fnMouseUp.call(c,a)}))},_fnMouseMove:function(a){var b=this;if(null===this.dom.drag){if(5>Math.pow(Math.pow(this._fnCursorPosition(a,
"pageX")-this.s.mouse.startX,2)+Math.pow(this._fnCursorPosition(a,"pageY")-this.s.mouse.startY,2),.5))return;this._fnCreateDragNode()}this.dom.drag.css({left:this._fnCursorPosition(a,"pageX")-this.s.mouse.offsetX,top:this._fnCursorPosition(a,"pageY")-this.s.mouse.offsetY});var c=this.s.mouse.toIndex;a=this._fnCursorPosition(a,"pageX");for(var d=function(a){for(;0<=a;){a--;if(0>=a)return null;if(b.s.aoTargets[a+1].x!==b.s.aoTargets[a].x)return b.s.aoTargets[a]}},h=function(){for(var a=0;a<b.s.aoTargets.length-
1;a++)if(b.s.aoTargets[a].x!==b.s.aoTargets[a+1].x)return b.s.aoTargets[a]},f=function(){for(var a=b.s.aoTargets.length-1;0<a;a--)if(b.s.aoTargets[a].x!==b.s.aoTargets[a-1].x)return b.s.aoTargets[a]},k=1;k<this.s.aoTargets.length;k++){var n=d(k);n||(n=h());var q=n.x+(this.s.aoTargets[k].x-n.x)/2;if(this._fnIsLtr()){if(a<q){var l=n;break}}else if(a>q){l=n;break}}l?(this.dom.pointer.css("left",l.x),this.s.mouse.toIndex=l.to):(this.dom.pointer.css("left",f().x),this.s.mouse.toIndex=f().to);this.s.init.bRealtime&&
c!==this.s.mouse.toIndex&&(this.s.dt.oInstance.fnColReorder(this.s.mouse.fromIndex,this.s.mouse.toIndex),this.s.mouse.fromIndex=this.s.mouse.toIndex,""===this.s.dt.oScroll.sX&&""===this.s.dt.oScroll.sY||this.s.dt.oInstance.fnAdjustColumnSizing(!1),this._fnRegions())},_fnMouseUp:function(a){d(r).off(".ColReorder");null!==this.dom.drag&&(this.dom.drag.remove(),this.dom.pointer.remove(),this.dom.drag=null,this.dom.pointer=null,this.s.dt.oInstance.fnColReorder(this.s.mouse.fromIndex,this.s.mouse.toIndex,
!0),this._fnSetColumnIndexes(),""===this.s.dt.oScroll.sX&&""===this.s.dt.oScroll.sY||this.s.dt.oInstance.fnAdjustColumnSizing(!1),this.s.dt.oInstance.oApi._fnSaveState(this.s.dt),null!==this.s.reorderCallback&&this.s.reorderCallback.call(this))},_fnRegions:function(){var a=this.s.dt.aoColumns,b=this._fnIsLtr();this.s.aoTargets.splice(0,this.s.aoTargets.length);var c=d(this.s.dt.nTable).offset().left,g=[];d.each(a,function(a,f){if(f.bVisible&&"none"!==f.nTh.style.display){f=d(f.nTh);var h=f.offset().left;
b&&(h+=f.outerWidth());g.push({index:a,bound:h});c=h}else g.push({index:a,bound:c})});var h=g[0];a=d(a[h.index].nTh).outerWidth();this.s.aoTargets.push({to:0,x:h.bound-a});for(h=0;h<g.length;h++){a=g[h];var f=a.index;a.index<this.s.mouse.fromIndex&&f++;this.s.aoTargets.push({to:f,x:a.bound})}0!==this.s.fixedRight&&this.s.aoTargets.splice(this.s.aoTargets.length-this.s.fixedRight);0!==this.s.fixed&&this.s.aoTargets.splice(0,this.s.fixed)},_fnCreateDragNode:function(){var a=""!==this.s.dt.oScroll.sX||
""!==this.s.dt.oScroll.sY,b=this.s.dt.aoColumns[this.s.mouse.targetIndex].nTh,c=b.parentNode,g=c.parentNode,h=g.parentNode,f=d(b).clone();this.dom.drag=d(h.cloneNode(!1)).addClass("DTCR_clonedTable").append(d(g.cloneNode(!1)).append(d(c.cloneNode(!1)).append(f[0]))).css({position:"absolute",top:0,left:0,width:d(b).outerWidth(),height:d(b).outerHeight()}).appendTo("body");this.dom.pointer=d("<div></div>").addClass("DTCR_pointer").css({position:"absolute",top:a?d("div.dataTables_scroll",this.s.dt.nTableWrapper).offset().top:
d(this.s.dt.nTable).offset().top,height:a?d("div.dataTables_scroll",this.s.dt.nTableWrapper).height():d(this.s.dt.nTable).height()}).appendTo("body")},_fnSetColumnIndexes:function(){d.each(this.s.dt.aoColumns,function(a,b){d(b.nTh).attr("data-column-index",a)})},_fnCursorPosition:function(a,b){return-1!==a.type.indexOf("touch")?a.originalEvent.touches[0][b]:a[b]},_fnIsLtr:function(){return"rtl"!==d(this.s.dt.nTable).css("direction")}});k.defaults={aiOrder:null,bEnable:!0,bRealtime:!0,iFixedColumnsLeft:0,
iFixedColumnsRight:0,fnReorderCallback:null};k.version="1.5.2";d.fn.dataTable.ColReorder=k;d.fn.DataTable.ColReorder=k;"function"==typeof d.fn.dataTable&&"function"==typeof d.fn.dataTableExt.fnVersionCheck&&d.fn.dataTableExt.fnVersionCheck("1.10.8")?d.fn.dataTableExt.aoFeatures.push({fnInit:function(a){var b=a.oInstance;a._colReorder?b.oApi._fnLog(a,1,"ColReorder attempted to initialise twice. Ignoring second"):(b=a.oInit,new k(a,b.colReorder||b.oColReorder||{}));return null},cFeature:"R",sFeature:"ColReorder"}):
alert("Warning: ColReorder requires DataTables 1.10.8 or greater - www.datatables.net/download");d(r).on("preInit.dt.colReorder",function(a,b){if("dt"===a.namespace){a=b.oInit.colReorder;var c=y.defaults.colReorder;if(a||c)c=d.extend({},a,c),!1!==a&&new k(b,c)}});d.fn.dataTable.Api.register("colReorder.reset()",function(){return this.iterator("table",function(a){a._colReorder.fnReset()})});d.fn.dataTable.Api.register("colReorder.order()",function(a,b){return a?this.iterator("table",function(c){c._colReorder.fnOrder(a,
b)}):this.context.length?this.context[0]._colReorder.fnOrder():null});d.fn.dataTable.Api.register("colReorder.transpose()",function(a,b){return this.context.length&&this.context[0]._colReorder?this.context[0]._colReorder.fnTranspose(a,b):a});d.fn.dataTable.Api.register("colReorder.move()",function(a,b,c,d){this.context.length&&(this.context[0]._colReorder.s.dt.oInstance.fnColReorder(a,b,c,d),this.context[0]._colReorder._fnSetColumnIndexes());return this});d.fn.dataTable.Api.register("colReorder.enable()",
function(a){return this.iterator("table",function(b){b._colReorder&&b._colReorder.fnEnable(a)})});d.fn.dataTable.Api.register("colReorder.disable()",function(){return this.iterator("table",function(a){a._colReorder&&a._colReorder.fnDisable()})});return k});
/*!
 Bootstrap 4 styling wrapper for ColReorder
 ©2018 SpryMedia Ltd - datatables.net/license
*/
(function(c){"function"===typeof define&&define.amd?define(["jquery","datatables.net-bs4","datatables.net-colreorder"],function(a){return c(a,window,document)}):"object"===typeof exports?module.exports=function(a,b){a||(a=window);b&&b.fn.dataTable||(b=require("datatables.net-bs4")(a,b).$);b.fn.dataTable.ColReorder||require("datatables.net-colreorder")(a,b);return c(b,a,a.document)}:c(jQuery,window,document)})(function(c,a,b,d){return c.fn.dataTable});
/*!
 Buttons for DataTables 1.6.1
 ©2016-2019 SpryMedia Ltd - datatables.net/license
*/
(function(d){"function"===typeof define&&define.amd?define(["jquery","datatables.net"],function(u){return d(u,window,document)}):"object"===typeof exports?module.exports=function(u,t){u||(u=window);t&&t.fn.dataTable||(t=require("datatables.net")(u,t).$);return d(t,u,u.document)}:d(jQuery,window,document)})(function(d,u,t,p){function y(a){a=new m.Api(a);var b=a.init().buttons||m.defaults.buttons;return(new n(a,b)).container()}var m=d.fn.dataTable,B=0,C=0,q=m.ext.buttons,n=function(a,b){if(!(this instanceof
n))return function(b){return(new n(b,a)).container()};"undefined"===typeof b&&(b={});!0===b&&(b={});d.isArray(b)&&(b={buttons:b});this.c=d.extend(!0,{},n.defaults,b);b.buttons&&(this.c.buttons=b.buttons);this.s={dt:new m.Api(a),buttons:[],listenKeys:"",namespace:"dtb"+B++};this.dom={container:d("<"+this.c.dom.container.tag+"/>").addClass(this.c.dom.container.className)};this._constructor()};d.extend(n.prototype,{action:function(a,b){a=this._nodeToButton(a);if(b===p)return a.conf.action;a.conf.action=
b;return this},active:function(a,b){var c=this._nodeToButton(a);a=this.c.dom.button.active;c=d(c.node);if(b===p)return c.hasClass(a);c.toggleClass(a,b===p?!0:b);return this},add:function(a,b){var c=this.s.buttons;if("string"===typeof b){b=b.split("-");var e=this.s;c=0;for(var d=b.length-1;c<d;c++)e=e.buttons[1*b[c]];c=e.buttons;b=1*b[b.length-1]}this._expandButton(c,a,e!==p,b);this._draw();return this},container:function(){return this.dom.container},disable:function(a){a=this._nodeToButton(a);d(a.node).addClass(this.c.dom.button.disabled);
return this},destroy:function(){d("body").off("keyup."+this.s.namespace);var a=this.s.buttons.slice(),b;var c=0;for(b=a.length;c<b;c++)this.remove(a[c].node);this.dom.container.remove();a=this.s.dt.settings()[0];c=0;for(b=a.length;c<b;c++)if(a.inst===this){a.splice(c,1);break}return this},enable:function(a,b){if(!1===b)return this.disable(a);a=this._nodeToButton(a);d(a.node).removeClass(this.c.dom.button.disabled);return this},name:function(){return this.c.name},node:function(a){if(!a)return this.dom.container;
a=this._nodeToButton(a);return d(a.node)},processing:function(a,b){var c=this.s.dt,e=this._nodeToButton(a);if(b===p)return d(e.node).hasClass("processing");d(e.node).toggleClass("processing",b);d(c.table().node()).triggerHandler("buttons-processing.dt",[b,c.button(a),c,d(a),e.conf]);return this},remove:function(a){var b=this._nodeToButton(a),c=this._nodeToHost(a),e=this.s.dt;if(b.buttons.length)for(var g=b.buttons.length-1;0<=g;g--)this.remove(b.buttons[g].node);b.conf.destroy&&b.conf.destroy.call(e.button(a),
e,d(a),b.conf);this._removeKey(b.conf);d(b.node).remove();a=d.inArray(b,c);c.splice(a,1);return this},text:function(a,b){var c=this._nodeToButton(a);a=this.c.dom.collection.buttonLiner;a=c.inCollection&&a&&a.tag?a.tag:this.c.dom.buttonLiner.tag;var e=this.s.dt,g=d(c.node),f=function(a){return"function"===typeof a?a(e,g,c.conf):a};if(b===p)return f(c.conf.text);c.conf.text=b;a?g.children(a).html(f(b)):g.html(f(b));return this},_constructor:function(){var a=this,b=this.s.dt,c=b.settings()[0],e=this.c.buttons;
c._buttons||(c._buttons=[]);c._buttons.push({inst:this,name:this.c.name});for(var g=0,f=e.length;g<f;g++)this.add(e[g]);b.on("destroy",function(b,e){e===c&&a.destroy()});d("body").on("keyup."+this.s.namespace,function(b){if(!t.activeElement||t.activeElement===t.body){var c=String.fromCharCode(b.keyCode).toLowerCase();-1!==a.s.listenKeys.toLowerCase().indexOf(c)&&a._keypress(c,b)}})},_addKey:function(a){a.key&&(this.s.listenKeys+=d.isPlainObject(a.key)?a.key.key:a.key)},_draw:function(a,b){a||(a=this.dom.container,
b=this.s.buttons);a.children().detach();for(var c=0,e=b.length;c<e;c++)a.append(b[c].inserter),a.append(" "),b[c].buttons&&b[c].buttons.length&&this._draw(b[c].collection,b[c].buttons)},_expandButton:function(a,b,c,e){var g=this.s.dt,f=0;b=d.isArray(b)?b:[b];for(var h=0,k=b.length;h<k;h++){var r=this._resolveExtends(b[h]);if(r)if(d.isArray(r))this._expandButton(a,r,c,e);else{var l=this._buildButton(r,c);l&&(e!==p?(a.splice(e,0,l),e++):a.push(l),l.conf.buttons&&(l.collection=d("<"+this.c.dom.collection.tag+
"/>"),l.conf._collection=l.collection,this._expandButton(l.buttons,l.conf.buttons,!0,e)),r.init&&r.init.call(g.button(l.node),g,d(l.node),r),f++)}}},_buildButton:function(a,b){var c=this.c.dom.button,e=this.c.dom.buttonLiner,g=this.c.dom.collection,f=this.s.dt,h=function(b){return"function"===typeof b?b(f,l,a):b};b&&g.button&&(c=g.button);b&&g.buttonLiner&&(e=g.buttonLiner);if(a.available&&!a.available(f,a))return!1;var k=function(a,b,c,e){e.action.call(b.button(c),a,b,c,e);d(b.table().node()).triggerHandler("buttons-action.dt",
[b.button(c),b,c,e])};g=a.tag||c.tag;var r=a.clickBlurs===p?!0:a.clickBlurs,l=d("<"+g+"/>").addClass(c.className).attr("tabindex",this.s.dt.settings()[0].iTabIndex).attr("aria-controls",this.s.dt.table().node().id).on("click.dtb",function(b){b.preventDefault();!l.hasClass(c.disabled)&&a.action&&k(b,f,l,a);r&&l.blur()}).on("keyup.dtb",function(b){13===b.keyCode&&!l.hasClass(c.disabled)&&a.action&&k(b,f,l,a)});"a"===g.toLowerCase()&&l.attr("href","#");"button"===g.toLowerCase()&&l.attr("type","button");
e.tag?(g=d("<"+e.tag+"/>").html(h(a.text)).addClass(e.className),"a"===e.tag.toLowerCase()&&g.attr("href","#"),l.append(g)):l.html(h(a.text));!1===a.enabled&&l.addClass(c.disabled);a.className&&l.addClass(a.className);a.titleAttr&&l.attr("title",h(a.titleAttr));a.attr&&l.attr(a.attr);a.namespace||(a.namespace=".dt-button-"+C++);e=(e=this.c.dom.buttonContainer)&&e.tag?d("<"+e.tag+"/>").addClass(e.className).append(l):l;this._addKey(a);this.c.buttonCreated&&(e=this.c.buttonCreated(a,e));return{conf:a,
node:l.get(0),inserter:e,buttons:[],inCollection:b,collection:null}},_nodeToButton:function(a,b){b||(b=this.s.buttons);for(var c=0,e=b.length;c<e;c++){if(b[c].node===a)return b[c];if(b[c].buttons.length){var d=this._nodeToButton(a,b[c].buttons);if(d)return d}}},_nodeToHost:function(a,b){b||(b=this.s.buttons);for(var c=0,e=b.length;c<e;c++){if(b[c].node===a)return b;if(b[c].buttons.length){var d=this._nodeToHost(a,b[c].buttons);if(d)return d}}},_keypress:function(a,b){if(!b._buttonsHandled){var c=
function(e){for(var g=0,f=e.length;g<f;g++){var h=e[g].conf,k=e[g].node;h.key&&(h.key===a?(b._buttonsHandled=!0,d(k).click()):!d.isPlainObject(h.key)||h.key.key!==a||h.key.shiftKey&&!b.shiftKey||h.key.altKey&&!b.altKey||h.key.ctrlKey&&!b.ctrlKey||h.key.metaKey&&!b.metaKey||(b._buttonsHandled=!0,d(k).click()));e[g].buttons.length&&c(e[g].buttons)}};c(this.s.buttons)}},_removeKey:function(a){if(a.key){var b=d.isPlainObject(a.key)?a.key.key:a.key;a=this.s.listenKeys.split("");b=d.inArray(b,a);a.splice(b,
1);this.s.listenKeys=a.join("")}},_resolveExtends:function(a){var b=this.s.dt,c,e=function(c){for(var e=0;!d.isPlainObject(c)&&!d.isArray(c);){if(c===p)return;if("function"===typeof c){if(c=c(b,a),!c)return!1}else if("string"===typeof c){if(!q[c])throw"Unknown button type: "+c;c=q[c]}e++;if(30<e)throw"Buttons: Too many iterations";}return d.isArray(c)?c:d.extend({},c)};for(a=e(a);a&&a.extend;){if(!q[a.extend])throw"Cannot extend unknown button type: "+a.extend;var g=e(q[a.extend]);if(d.isArray(g))return g;
if(!g)return!1;var f=g.className;a=d.extend({},g,a);f&&a.className!==f&&(a.className=f+" "+a.className);var h=a.postfixButtons;if(h){a.buttons||(a.buttons=[]);f=0;for(c=h.length;f<c;f++)a.buttons.push(h[f]);a.postfixButtons=null}if(h=a.prefixButtons){a.buttons||(a.buttons=[]);f=0;for(c=h.length;f<c;f++)a.buttons.splice(f,0,h[f]);a.prefixButtons=null}a.extend=g.extend}return a},_popover:function(a,b,c){var e=this.c,g=d.extend({align:"button-left",autoClose:!1,background:!0,backgroundClassName:"dt-button-background",
contentClassName:e.dom.collection.className,collectionLayout:"",collectionTitle:"",dropup:!1,fade:400,rightAlignClassName:"dt-button-right",tag:e.dom.collection.tag},c),f=b.node(),h=function(){d(".dt-button-collection").stop().fadeOut(g.fade,function(){d(this).detach()});d(b.buttons('[aria-haspopup="true"][aria-expanded="true"]').nodes()).attr("aria-expanded","false");d("div.dt-button-background").off("click.dtb-collection");n.background(!1,g.backgroundClassName,g.fade,f);d("body").off(".dtb-collection");
b.off("buttons-action.b-internal")};!1===a&&h();c=d(b.buttons('[aria-haspopup="true"][aria-expanded="true"]').nodes());c.length&&(f=c.eq(0),h());c=d("<div/>").addClass("dt-button-collection").addClass(g.collectionLayout).css("display","none");a=d(a).addClass(g.contentClassName).attr("role","menu").appendTo(c);f.attr("aria-expanded","true");f.parents("body")[0]!==t.body&&(f=t.body.lastChild);g.collectionTitle&&c.prepend('<div class="dt-button-collection-title">'+g.collectionTitle+"</div>");c.insertAfter(f).fadeIn(g.fade);
var k=d(b.table().container());e=c.css("position");"dt-container"===g.align&&(f=f.parent(),c.css("width",k.width()));if("absolute"===e){e=f.position();c.css({top:e.top+f.outerHeight(),left:e.left});var r=c.outerHeight(),l=c.outerWidth(),w=k.offset().top+k.height();w=e.top+f.outerHeight()+r-w;var D=e.top-r,m=k.offset().top;r=e.top-r-5;(w>m-D||g.dropup)&&-r<m&&c.css("top",r);(c.hasClass(g.rightAlignClassName)||"button-right"===g.align)&&c.css("left",e.left+f.outerWidth()-l);r=e.left+l;k=k.offset().left+
k.width();r>k&&c.css("left",e.left-(r-k));k=f.offset().left+l;k>d(u).width()&&c.css("left",e.left-(k-d(u).width()))}else e=c.height()/2,e>d(u).height()/2&&(e=d(u).height()/2),c.css("marginTop",-1*e);g.background&&n.background(!0,g.backgroundClassName,g.fade,f);d("div.dt-button-background").on("click.dtb-collection",function(){});d("body").on("click.dtb-collection",function(b){var c=d.fn.addBack?"addBack":"andSelf";d(b.target).parents()[c]().filter(a).length||h()}).on("keyup.dtb-collection",function(a){27===
a.keyCode&&h()});g.autoClose&&setTimeout(function(){b.on("buttons-action.b-internal",function(a,b,c,e){e[0]!==f[0]&&h()})},0)}});n.background=function(a,b,c,e){c===p&&(c=400);e||(e=t.body);a?d("<div/>").addClass(b).css("display","none").insertAfter(e).stop().fadeIn(c):d("div."+b).stop().fadeOut(c,function(){d(this).removeClass(b).remove()})};n.instanceSelector=function(a,b){if(a===p||null===a)return d.map(b,function(a){return a.inst});var c=[],e=d.map(b,function(a){return a.name}),g=function(a){if(d.isArray(a))for(var f=
0,k=a.length;f<k;f++)g(a[f]);else"string"===typeof a?-1!==a.indexOf(",")?g(a.split(",")):(a=d.inArray(d.trim(a),e),-1!==a&&c.push(b[a].inst)):"number"===typeof a&&c.push(b[a].inst)};g(a);return c};n.buttonSelector=function(a,b){for(var c=[],e=function(a,b,c){for(var d,g,f=0,k=b.length;f<k;f++)if(d=b[f])g=c!==p?c+f:f+"",a.push({node:d.node,name:d.conf.name,idx:g}),d.buttons&&e(a,d.buttons,g+"-")},g=function(a,b){var f,h=[];e(h,b.s.buttons);var k=d.map(h,function(a){return a.node});if(d.isArray(a)||
a instanceof d)for(k=0,f=a.length;k<f;k++)g(a[k],b);else if(null===a||a===p||"*"===a)for(k=0,f=h.length;k<f;k++)c.push({inst:b,node:h[k].node});else if("number"===typeof a)c.push({inst:b,node:b.s.buttons[a].node});else if("string"===typeof a)if(-1!==a.indexOf(","))for(h=a.split(","),k=0,f=h.length;k<f;k++)g(d.trim(h[k]),b);else if(a.match(/^\d+(\-\d+)*$/))k=d.map(h,function(a){return a.idx}),c.push({inst:b,node:h[d.inArray(a,k)].node});else if(-1!==a.indexOf(":name"))for(a=a.replace(":name",""),k=
0,f=h.length;k<f;k++)h[k].name===a&&c.push({inst:b,node:h[k].node});else d(k).filter(a).each(function(){c.push({inst:b,node:this})});else"object"===typeof a&&a.nodeName&&(h=d.inArray(a,k),-1!==h&&c.push({inst:b,node:k[h]}))},f=0,h=a.length;f<h;f++)g(b,a[f]);return c};n.defaults={buttons:["copy","excel","csv","pdf","print"],name:"main",tabIndex:0,dom:{container:{tag:"div",className:"dt-buttons"},collection:{tag:"div",className:""},button:{tag:"ActiveXObject"in u?"a":"button",className:"dt-button",
active:"active",disabled:"disabled"},buttonLiner:{tag:"span",className:""}}};n.version="1.6.1";d.extend(q,{collection:{text:function(a){return a.i18n("buttons.collection","Collection")},className:"buttons-collection",init:function(a,b,c){b.attr("aria-expanded",!1)},action:function(a,b,c,e){a.stopPropagation();e._collection.parents("body").length?this.popover(!1,e):this.popover(e._collection,e)},attr:{"aria-haspopup":!0}},copy:function(a,b){if(q.copyHtml5)return"copyHtml5";if(q.copyFlash&&q.copyFlash.available(a,
b))return"copyFlash"},csv:function(a,b){if(q.csvHtml5&&q.csvHtml5.available(a,b))return"csvHtml5";if(q.csvFlash&&q.csvFlash.available(a,b))return"csvFlash"},excel:function(a,b){if(q.excelHtml5&&q.excelHtml5.available(a,b))return"excelHtml5";if(q.excelFlash&&q.excelFlash.available(a,b))return"excelFlash"},pdf:function(a,b){if(q.pdfHtml5&&q.pdfHtml5.available(a,b))return"pdfHtml5";if(q.pdfFlash&&q.pdfFlash.available(a,b))return"pdfFlash"},pageLength:function(a){a=a.settings()[0].aLengthMenu;var b=d.isArray(a[0])?
a[0]:a,c=d.isArray(a[0])?a[1]:a;return{extend:"collection",text:function(a){return a.i18n("buttons.pageLength",{"-1":"Show all rows",_:"Show %d rows"},a.page.len())},className:"buttons-page-length",autoClose:!0,buttons:d.map(b,function(a,b){return{text:c[b],className:"button-page-length",action:function(b,c){c.page.len(a).draw()},init:function(b,c,d){var e=this;c=function(){e.active(b.page.len()===a)};b.on("length.dt"+d.namespace,c);c()},destroy:function(a,b,c){a.off("length.dt"+c.namespace)}}}),
init:function(a,b,c){var d=this;a.on("length.dt"+c.namespace,function(){d.text(c.text)})},destroy:function(a,b,c){a.off("length.dt"+c.namespace)}}}});m.Api.register("buttons()",function(a,b){b===p&&(b=a,a=p);this.selector.buttonGroup=a;var c=this.iterator(!0,"table",function(c){if(c._buttons)return n.buttonSelector(n.instanceSelector(a,c._buttons),b)},!0);c._groupSelector=a;return c});m.Api.register("button()",function(a,b){a=this.buttons(a,b);1<a.length&&a.splice(1,a.length);return a});m.Api.registerPlural("buttons().active()",
"button().active()",function(a){return a===p?this.map(function(a){return a.inst.active(a.node)}):this.each(function(b){b.inst.active(b.node,a)})});m.Api.registerPlural("buttons().action()","button().action()",function(a){return a===p?this.map(function(a){return a.inst.action(a.node)}):this.each(function(b){b.inst.action(b.node,a)})});m.Api.register(["buttons().enable()","button().enable()"],function(a){return this.each(function(b){b.inst.enable(b.node,a)})});m.Api.register(["buttons().disable()",
"button().disable()"],function(){return this.each(function(a){a.inst.disable(a.node)})});m.Api.registerPlural("buttons().nodes()","button().node()",function(){var a=d();d(this.each(function(b){a=a.add(b.inst.node(b.node))}));return a});m.Api.registerPlural("buttons().processing()","button().processing()",function(a){return a===p?this.map(function(a){return a.inst.processing(a.node)}):this.each(function(b){b.inst.processing(b.node,a)})});m.Api.registerPlural("buttons().text()","button().text()",function(a){return a===
p?this.map(function(a){return a.inst.text(a.node)}):this.each(function(b){b.inst.text(b.node,a)})});m.Api.registerPlural("buttons().trigger()","button().trigger()",function(){return this.each(function(a){a.inst.node(a.node).trigger("click")})});m.Api.register("button().popover()",function(a,b){return this.map(function(c){return c.inst._popover(a,this.button(this[0].node),b)})});m.Api.register("buttons().containers()",function(){var a=d(),b=this._groupSelector;this.iterator(!0,"table",function(c){if(c._buttons){c=
n.instanceSelector(b,c._buttons);for(var d=0,g=c.length;d<g;d++)a=a.add(c[d].container())}});return a});m.Api.register("buttons().container()",function(){return this.containers().eq(0)});m.Api.register("button().add()",function(a,b){var c=this.context;c.length&&(c=n.instanceSelector(this._groupSelector,c[0]._buttons),c.length&&c[0].add(b,a));return this.button(this._groupSelector,a)});m.Api.register("buttons().destroy()",function(){this.pluck("inst").unique().each(function(a){a.destroy()});return this});
m.Api.registerPlural("buttons().remove()","buttons().remove()",function(){this.each(function(a){a.inst.remove(a.node)});return this});var v;m.Api.register("buttons.info()",function(a,b,c){var e=this;if(!1===a)return this.off("destroy.btn-info"),d("#datatables_buttons_info").fadeOut(function(){d(this).remove()}),clearTimeout(v),v=null,this;v&&clearTimeout(v);d("#datatables_buttons_info").length&&d("#datatables_buttons_info").remove();a=a?"<h2>"+a+"</h2>":"";d('<div id="datatables_buttons_info" class="dt-button-info"/>').html(a).append(d("<div/>")["string"===
typeof b?"html":"append"](b)).css("display","none").appendTo("body").fadeIn();c!==p&&0!==c&&(v=setTimeout(function(){e.buttons.info(!1)},c));this.on("destroy.btn-info",function(){e.buttons.info(!1)});return this});m.Api.register("buttons.exportData()",function(a){if(this.context.length)return E(new m.Api(this.context[0]),a)});m.Api.register("buttons.exportInfo()",function(a){a||(a={});var b=a;var c="*"===b.filename&&"*"!==b.title&&b.title!==p&&null!==b.title&&""!==b.title?b.title:b.filename;"function"===
typeof c&&(c=c());c===p||null===c?c=null:(-1!==c.indexOf("*")&&(c=d.trim(c.replace("*",d("head > title").text()))),c=c.replace(/[^a-zA-Z0-9_\u00A1-\uFFFF\.,\-_ !\(\)]/g,""),(b=x(b.extension))||(b=""),c+=b);b=x(a.title);b=null===b?null:-1!==b.indexOf("*")?b.replace("*",d("head > title").text()||"Exported data"):b;return{filename:c,title:b,messageTop:z(this,a.message||a.messageTop,"top"),messageBottom:z(this,a.messageBottom,"bottom")}});var x=function(a){return null===a||a===p?null:"function"===typeof a?
a():a},z=function(a,b,c){b=x(b);if(null===b)return null;a=d("caption",a.table().container()).eq(0);return"*"===b?a.css("caption-side")!==c?null:a.length?a.text():"":b},A=d("<textarea/>")[0],E=function(a,b){var c=d.extend(!0,{},{rows:null,columns:"",modifier:{search:"applied",order:"applied"},orthogonal:"display",stripHtml:!0,stripNewlines:!0,decodeEntities:!0,trim:!0,format:{header:function(a){return e(a)},footer:function(a){return e(a)},body:function(a){return e(a)}},customizeData:null},b),e=function(a){if("string"!==
typeof a)return a;a=a.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,"");a=a.replace(/<!\-\-.*?\-\->/g,"");c.stripHtml&&(a=a.replace(/<[^>]*>/g,""));c.trim&&(a=a.replace(/^\s+|\s+$/g,""));c.stripNewlines&&(a=a.replace(/\n/g," "));c.decodeEntities&&(A.innerHTML=a,a=A.value);return a};b=a.columns(c.columns).indexes().map(function(b){var d=a.column(b).header();return c.format.header(d.innerHTML,b,d)}).toArray();var g=a.table().footer()?a.columns(c.columns).indexes().map(function(b){var d=
a.column(b).footer();return c.format.footer(d?d.innerHTML:"",b,d)}).toArray():null,f=d.extend({},c.modifier);a.select&&"function"===typeof a.select.info&&f.selected===p&&a.rows(c.rows,d.extend({selected:!0},f)).any()&&d.extend(f,{selected:!0});f=a.rows(c.rows,f).indexes().toArray();var h=a.cells(f,c.columns);f=h.render(c.orthogonal).toArray();h=h.nodes().toArray();for(var k=b.length,m=[],l=0,n=0,q=0<k?f.length/k:0;n<q;n++){for(var u=[k],t=0;t<k;t++)u[t]=c.format.body(f[l],n,t,h[l]),l++;m[n]=u}b={header:b,
footer:g,body:m};c.customizeData&&c.customizeData(b);return b};d.fn.dataTable.Buttons=n;d.fn.DataTable.Buttons=n;d(t).on("init.dt plugin-init.dt",function(a,b){"dt"===a.namespace&&(a=b.oInit.buttons||m.defaults.buttons)&&!b._buttons&&(new n(b,a)).container()});m.ext.feature.push({fnInit:y,cFeature:"B"});m.ext.features&&m.ext.features.register("buttons",y);return n});
/*!
 Bootstrap integration for DataTables' Buttons
 ©2016 SpryMedia Ltd - datatables.net/license
*/
(function(c){"function"===typeof define&&define.amd?define(["jquery","datatables.net-bs4","datatables.net-buttons"],function(a){return c(a,window,document)}):"object"===typeof exports?module.exports=function(a,b){a||(a=window);b&&b.fn.dataTable||(b=require("datatables.net-bs4")(a,b).$);b.fn.dataTable.Buttons||require("datatables.net-buttons")(a,b);return c(b,a,a.document)}:c(jQuery,window,document)})(function(c,a,b,d){a=c.fn.dataTable;c.extend(!0,a.Buttons.defaults,{dom:{container:{className:"dt-buttons btn-group flex-wrap"},
button:{className:"btn btn-secondary"},collection:{tag:"div",className:"dropdown-menu",button:{tag:"a",className:"dt-button dropdown-item",active:"active",disabled:"disabled"}}},buttonCreated:function(a,b){return a.buttons?c('<div class="btn-group"/>').append(b):b}});a.ext.buttons.collection.className+=" dropdown-toggle";a.ext.buttons.collection.rightAlignClassName="dropdown-menu-right";return a.Buttons});
/*!
 HTML5 export buttons for Buttons and DataTables.
 2016 SpryMedia Ltd - datatables.net/license

 FileSaver.js (1.3.3) - MIT license
 Copyright © 2016 Eli Grey - http://eligrey.com
*/
(function(r){if(typeof define==="function"&&define.amd){define(["jquery","datatables.net","datatables.net-buttons"],function(e){return r(e,window,document)})}else if(typeof exports==="object"){module.exports=function(e,t,l,o){if(!e){e=window}if(!t||!t.fn.dataTable){t=require("datatables.net")(e,t).$}if(!t.fn.dataTable.Buttons){require("datatables.net-buttons")(e,t)}return r(t,e,e.document,l,o)}}else{r(jQuery,window,document)}})(function(T,e,y,t,l,S){"use strict";var o=T.fn.dataTable;function N(){return t||e.JSZip}function m(){return l||e.pdfMake}o.Buttons.pdfMake=function(e){if(!e){return m()}l=m_ake};o.Buttons.jszip=function(e){if(!e){return N()}t=e};var O=function(i){"use strict";if(typeof i==="undefined"||typeof navigator!=="undefined"&&/MSIE [1-9]\./.test(navigator.userAgent)){return}var e=i.document,f=function(){return i.URL||i.webkitURL||i},s=e.createElementNS("http://www.w3.org/1999/xhtml","a"),m="download"in s,u=function(e){var t=new MouseEvent("click");e.dispatchEvent(t)},c=/constructor/i.test(i.HTMLElement)||i.safari,y=/CriOS\/[\d]+/.test(navigator.userAgent),n=function(e){(i.setImmediate||i.setTimeout)(function(){throw e},0)},I="application/octet-stream",l=1e3*40,F=function(e){var t=function(){if(typeof e==="string"){f().revokeObjectURL(e)}else{e.remove()}};setTimeout(t,l)},x=function(e,t,l){t=[].concat(t);var o=t.length;while(o--){var r=e["on"+t[o]];if(typeof r==="function"){try{r.call(e,l||e)}catch(e){n(e)}}}},h=function(e){if(/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)){return new Blob([String.fromCharCode(65279),e],{type:e.type})}return e},o=function(t,e,l){if(!l){t=h(t)}var o=this,r=t.type,n=r===I,a,p=function(){x(o,"writestart progress write writeend".split(" "))},d=function(){if((y||n&&c)&&i.FileReader){var l=new FileReader;l.onloadend=function(){var e=y?l.result:l.result.replace(/^data:[^;]*;/,"data:attachment/file;");var t=i.open(e,"_blank");if(!t)i.location.href=e;e=S;o.readyState=o.DONE;p()};l.readAsDataURL(t);o.readyState=o.INIT;return}if(!a){a=f().createObjectURL(t)}if(n){i.location.href=a}else{var e=i.open(a,"_blank");if(!e){i.location.href=a}}o.readyState=o.DONE;p();F(a)};o.readyState=o.INIT;if(m){a=f().createObjectURL(t);setTimeout(function(){s.href=a;s.download=e;u(s);p();F(a);o.readyState=o.DONE});return}d()},t=o.prototype,r=function(e,t,l){return new o(e,t||e.name||"download",l)};if(typeof navigator!=="undefined"&&navigator.msSaveOrOpenBlob){return function(e,t,l){t=t||e.name||"download";if(!l){e=h(e)}return navigator.msSaveOrOpenBlob(e,t)}}t.abort=function(){};t.readyState=t.INIT=0;t.WRITING=1;t.DONE=2;t.error=t.onwritestart=t.onprogress=t.onwrite=t.onabort=t.onerror=t.onwriteend=null;return r}(typeof self!=="undefined"&&self||typeof e!=="undefined"&&e||this.content);o.fileSave=O;var z=function(e){var t="Sheet1";if(e.sheetName){t=e.sheetName.replace(/[\[\]\*\/\\\?\:]/g,"")}return t};var I=function(e){return e.newline?e.newline:navigator.userAgent.match(/Windows/)?"\r\n":"\n"};var F=function(e,t){var l=I(t);var o=e.buttons.exportData(t.exportOptions);var r=t.fieldBoundary;var n=t.fieldSeparator;var a=new RegExp(r,"g");var p=t.escapeChar!==S?t.escapeChar:"\\";var d=function(e){var t="";for(var l=0,o=e.length;l<o;l++){if(l>0){t+=n}t+=r?r+(""+e[l]).replace(a,p+r)+r:e[l]}return t};var i=t.header?d(o.header)+l:"";var f=t.footer&&o.footer?l+d(o.footer):"";var s=[];for(var m=0,u=o.body.length;m<u;m++){s.push(d(o.body[m]))}return{str:i+s.join(l)+f,rows:s.length}};var u=function(){var e=navigator.userAgent.indexOf("Safari")!==-1&&navigator.userAgent.indexOf("Chrome")===-1&&navigator.userAgent.indexOf("Opera")===-1;if(!e){return false}var t=navigator.userAgent.match(/AppleWebKit\/(\d+\.\d+)/);if(t&&t.length>1&&t[1]*1<603.1){return true}return false};function A(e){var t="A".charCodeAt(0);var l="Z".charCodeAt(0);var o=l-t+1;var r="";while(e>=0){r=String.fromCharCode(e%o+t)+r;e=Math.floor(e/o)-1}return r}try{var c=new XMLSerializer;var x}catch(e){}function D(s,e){if(x===S){x=c.serializeToString(T.parseXML(R["xl/worksheets/sheet1.xml"])).indexOf("xmlns:r")===-1}T.each(e,function(e,t){if(T.isPlainObject(t)){var l=s.folder(e);D(l,t)}else{if(x){var o=t.childNodes[0];var r,n;var a=[];for(r=o.attributes.length-1;r>=0;r--){var p=o.attributes[r].nodeName;var d=o.attributes[r].nodeValue;if(p.indexOf(":")!==-1){a.push({name:p,value:d});o.removeAttribute(p)}}for(r=0,n=a.length;r<n;r++){var i=t.createAttribute(a[r].name.replace(":","_dt_b_namespace_token_"));i.value=a[r].value;o.setAttributeNode(i)}}var f=c.serializeToString(t);if(x){if(f.indexOf("<?xml")===-1){f='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>'+f}f=f.replace(/_dt_b_namespace_token_/g,":");f=f.replace(/xmlns:NS[\d]+="" NS[\d]+:/g,"")}f=f.replace(/<([^<>]*?) xmlns=""([^<>]*?)>/g,"<$1 $2>");s.file(e,f)}})}function _(e,t,l){var o=e.createElement(t);if(l){if(l.attr){T(o).attr(l.attr)}if(l.children){T.each(l.children,function(e,t){o.appendChild(t)})}if(l.text!==null&&l.text!==S){o.appendChild(e.createTextNode(l.text))}}return o}function E(e,t){var l=e.header[t].length;var o,r,n;if(e.footer&&e.footer[t].length>l){l=e.footer[t].length}for(var a=0,p=e.body.length;a<p;a++){var d=e.body[a][t];n=d!==null&&d!==S?d.toString():"";if(n.indexOf("\n")!==-1){r=n.split("\n");r.sort(function(e,t){return t.length-e.length});o=r[0].length}else{o=n.length}if(o>l){l=o}if(l>40){return 54}}l*=1.35;return l>6?l:6}var R={"_rels/.rels":'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>'+'<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">'+'<Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument" Target="xl/workbook.xml"/>'+"</Relationships>","xl/_rels/workbook.xml.rels":'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>'+'<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">'+'<Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet" Target="worksheets/sheet1.xml"/>'+'<Relationship Id="rId2" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles" Target="styles.xml"/>'+"</Relationships>","[Content_Types].xml":'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>'+'<Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types">'+'<Default Extension="xml" ContentType="application/xml" />'+'<Default Extension="rels" ContentType="application/vnd.openxmlformats-package.relationships+xml" />'+'<Default Extension="jpeg" ContentType="image/jpeg" />'+'<Override PartName="/xl/workbook.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml" />'+'<Override PartName="/xl/worksheets/sheet1.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml" />'+'<Override PartName="/xl/styles.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml" />'+"</Types>","xl/workbook.xml":'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>'+'<workbook xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships">'+'<fileVersion appName="xl" lastEdited="5" lowestEdited="5" rupBuild="24816"/>'+'<workbookPr showInkAnnotation="0" autoCompressPictures="0"/>'+"<bookViews>"+'<workbookView xWindow="0" yWindow="0" windowWidth="25600" windowHeight="19020" tabRatio="500"/>'+"</bookViews>"+"<sheets>"+'<sheet name="Sheet1" sheetId="1" r:id="rId1"/>'+"</sheets>"+"<definedNames/>"+"</workbook>","xl/worksheets/sheet1.xml":'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>'+'<worksheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="x14ac" xmlns:x14ac="http://schemas.microsoft.com/office/spreadsheetml/2009/9/ac">'+"<sheetData/>"+'<mergeCells count="0"/>'+"</worksheet>","xl/styles.xml":'<?xml version="1.0" encoding="UTF-8"?>'+'<styleSheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="x14ac" xmlns:x14ac="http://schemas.microsoft.com/office/spreadsheetml/2009/9/ac">'+'<numFmts count="6">'+'<numFmt numFmtId="164" formatCode="#,##0.00_- [$$-45C]"/>'+'<numFmt numFmtId="165" formatCode="&quot;£&quot;#,##0.00"/>'+'<numFmt numFmtId="166" formatCode="[$€-2] #,##0.00"/>'+'<numFmt numFmtId="167" formatCode="0.0%"/>'+'<numFmt numFmtId="168" formatCode="#,##0;(#,##0)"/>'+'<numFmt numFmtId="169" formatCode="#,##0.00;(#,##0.00)"/>'+"</numFmts>"+'<fonts count="5" x14ac:knownFonts="1">'+"<font>"+'<sz val="9" />'+'<name val="微软雅黑" />'+"</font>"+"<font>"+'<sz val="9" />'+'<name val="微软雅黑" />'+'<color rgb="FFFFFFFF" />'+"</font>"+"<font>"+'<sz val="9" />'+'<name val="微软雅黑" />'+"<b />"+"</font>"+"<font>"+'<sz val="9" />'+'<name val="微软雅黑" />'+"<i />"+"</font>"+"<font>"+'<sz val="9" />'+'<name val="微软雅黑" />'+"<u />"+"</font>"+"</fonts>"+'<fills count="6">'+"<fill>"+'<patternFill patternType="none" />'+"</fill>"+"<fill>"+'<patternFill patternType="none" />'+"</fill>"+"<fill>"+'<patternFill patternType="solid">'+'<fgColor rgb="FFD9D9D9" />'+'<bgColor indexed="64" />'+"</patternFill>"+"</fill>"+"<fill>"+'<patternFill patternType="solid">'+'<fgColor rgb="FFD99795" />'+'<bgColor indexed="64" />'+"</patternFill>"+"</fill>"+"<fill>"+'<patternFill patternType="solid">'+'<fgColor rgb="ffc6efce" />'+'<bgColor indexed="64" />'+"</patternFill>"+"</fill>"+"<fill>"+'<patternFill patternType="solid">'+'<fgColor rgb="ffc6cfef" />'+'<bgColor indexed="64" />'+"</patternFill>"+"</fill>"+"</fills>"+'<borders count="2">'+"<border>"+"<left />"+"<right />"+"<top />"+"<bottom />"+"<diagonal />"+"</border>"+'<border diagonalUp="false" diagonalDown="false">'+'<left style="thin">'+'<color auto="1" />'+"</left>"+'<right style="thin">'+'<color auto="1" />'+"</right>"+'<top style="thin">'+'<color auto="1" />'+"</top>"+'<bottom style="thin">'+'<color auto="1" />'+"</bottom>"+"<diagonal />"+"</border>"+"</borders>"+'<cellStyleXfs count="1">'+'<xf numFmtId="0" fontId="0" fillId="0" borderId="0" />'+"</cellStyleXfs>"+'<cellXfs count="67">'+'<xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="1" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="2" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="3" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="4" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="0" fillId="2" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="1" fillId="2" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="2" fillId="2" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="3" fillId="2" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="4" fillId="2" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="0" fillId="3" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="1" fillId="3" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="2" fillId="3" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="3" fillId="3" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="4" fillId="3" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="0" fillId="4" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="1" fillId="4" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="2" fillId="4" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="3" fillId="4" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="4" fillId="4" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="0" fillId="5" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="1" fillId="5" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="2" fillId="5" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="3" fillId="5" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="4" fillId="5" borderId="0" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="0" fillId="0" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="1" fillId="0" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="2" fillId="0" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="3" fillId="0" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="4" fillId="0" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="0" fillId="2" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="1" fillId="2" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="2" fillId="2" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="3" fillId="2" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="4" fillId="2" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="0" fillId="3" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="1" fillId="3" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="2" fillId="3" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="3" fillId="3" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="4" fillId="3" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="0" fillId="4" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="1" fillId="4" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="2" fillId="4" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="3" fillId="4" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="4" fillId="4" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="0" fillId="5" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="1" fillId="5" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="2" fillId="5" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="3" fillId="5" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="4" fillId="5" borderId="1" applyFont="1" applyFill="1" applyBorder="1"/>'+'<xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyAlignment="1">'+'<alignment horizontal="left"/>'+"</xf>"+'<xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyAlignment="1">'+'<alignment horizontal="center"/>'+"</xf>"+'<xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyAlignment="1">'+'<alignment horizontal="right"/>'+"</xf>"+'<xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyAlignment="1">'+'<alignment horizontal="fill"/>'+"</xf>"+'<xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyAlignment="1">'+'<alignment textRotation="90"/>'+"</xf>"+'<xf numFmtId="0" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyAlignment="1">'+'<alignment wrapText="1"/>'+"</xf>"+'<xf numFmtId="9"   fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/>'+'<xf numFmtId="164" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/>'+'<xf numFmtId="165" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/>'+'<xf numFmtId="166" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/>'+'<xf numFmtId="167" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/>'+'<xf numFmtId="168" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/>'+'<xf numFmtId="169" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/>'+'<xf numFmtId="3" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/>'+'<xf numFmtId="4" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/>'+'<xf numFmtId="1" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/>'+'<xf numFmtId="2" fontId="0" fillId="0" borderId="0" applyFont="1" applyFill="1" applyBorder="1" xfId="0" applyNumberFormat="1"/>'+"</cellXfs>"+'<cellStyles count="1">'+'<cellStyle name="Normal" xfId="0" builtinId="0" />'+"</cellStyles>"+'<dxfs count="0" />'+'<tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4" />'+"</styleSheet>"};var $=[{match:/^\-?\d+\.\d%$/,style:60,fmt:function(e){return e/100}},{match:/^\-?\d+\.?\d*%$/,style:56,fmt:function(e){return e/100}},{match:/^\-?\$[\d,]+.?\d*$/,style:57},{match:/^\-?£[\d,]+.?\d*$/,style:58},{match:/^\-?€[\d,]+.?\d*$/,style:59},{match:/^\-?\d+$/,style:65},{match:/^\-?\d+\.\d{2}$/,style:66},{match:/^\([\d,]+\)$/,style:61,fmt:function(e){return-1*e.replace(/[\(\)]/g,"")}},{match:/^\([\d,]+\.\d{2}\)$/,style:62,fmt:function(e){return-1*e.replace(/[\(\)]/g,"")}},{match:/^\-?[\d,]+$/,style:63},{match:/^\-?[\d,]+\.\d{2}$/,style:64}];o.ext.buttons.copyHtml5={className:"buttons-copy buttons-html5",text:function(e){return e.i18n("buttons.copy","Copy")},action:function(e,t,l,o){this.processing(true);var r=this;var n=F(t,o);var a=t.buttons.exportInfo(o);var p=I(o);var d=n.str;var i=T("<div/>").css({height:1,width:1,overflow:"hidden",position:"fixed",top:0,left:0});if(a.title){d=a.title+p+p+d}if(a.messageTop){d=a.messageTop+p+p+d}if(a.messageBottom){d=d+p+p+a.messageBottom}if(o.customize){d=o.customize(d,o,t)}var f=T("<textarea readonly/>").val(d).appendTo(i);if(y.queryCommandSupported("copy")){i.appendTo(t.table().container());f[0].focus();f[0].select();try{var s=y.execCommand("copy");i.remove();if(s){t.buttons.info(t.i18n("buttons.copyTitle","Copy to clipboard"),t.i18n("buttons.copySuccess",{1:"Copied one row to clipboard",_:"Copied %d rows to clipboard"},n.rows),2e3);this.processing(false);return}}catch(e){}}var m=T("<span>"+t.i18n("buttons.copyKeys","Press <i>ctrl</i> or <i>⌘</i> + <i>C</i> to copy the table data<br>to your system clipboard.<br><br>"+"To cancel, click this message or press escape.")+"</span>").append(i);t.buttons.info(t.i18n("buttons.copyTitle","Copy to clipboard"),m,0);f[0].focus();f[0].select();var u=T(m).closest(".dt-button-info");var c=function(){u.off("click.buttons-copy");T(y).off(".buttons-copy");t.buttons.info(false)};u.on("click.buttons-copy",c);T(y).on("keydown.buttons-copy",function(e){if(e.keyCode===27){c();r.processing(false)}}).on("copy.buttons-copy cut.buttons-copy",function(){c();r.processing(false)})},exportOptions:{},fieldSeparator:"\t",fieldBoundary:"",header:true,footer:false,title:"*",messageTop:"*",messageBottom:"*"};o.ext.buttons.csvHtml5={bom:false,className:"buttons-csv buttons-html5",available:function(){return e.FileReader!==S&&e.Blob},text:function(e){return e.i18n("buttons.csv","CSV")},action:function(e,t,l,o){this.processing(true);var r=F(t,o).str;var n=t.buttons.exportInfo(o);var a=o.charset;if(o.customize){r=o.customize(r,o,t)}if(a!==false){if(!a){a=y.characterSet||y.charset}if(a){a=";charset="+a}}else{a=""}if(o.bom){r="\ufeff"+r}O(new Blob([r],{type:"text/csv"+a}),n.filename,true);this.processing(false)},filename:"*",extension:".csv",exportOptions:{},fieldSeparator:",",fieldBoundary:'"',escapeChar:'"',charset:null,header:true,footer:false};o.ext.buttons.excelHtml5={className:"buttons-excel buttons-html5",available:function(){return e.FileReader!==S&&N()!==S&&!u()&&c},text:function(e){return e.i18n("buttons.excel","Excel")},action:function(e,t,l,s){this.processing(true);var o=this;var m=0;var r,n;var a=function(e){var t=R[e];return T.parseXML(t)};var u=a("xl/worksheets/sheet1.xml");var c=u.getElementsByTagName("sheetData")[0];var p={_rels:{".rels":a("_rels/.rels")},xl:{_rels:{"workbook.xml.rels":a("xl/_rels/workbook.xml.rels")},"workbook.xml":a("xl/workbook.xml"),"styles.xml":a("xl/styles.xml"),worksheets:{"sheet1.xml":u}},"[Content_Types].xml":a("[Content_Types].xml")};var d=t.buttons.exportData(s.exportOptions);var y,I;var i=function(e){y=m+1;I=_(u,"row",{attr:{r:y}});for(var t=0,l=e.length;t<l;t++){var o=A(t)+""+y;var r=null;if(e[t]===null||e[t]===S||e[t]===""){if(s.createEmptyCells===true){e[t]=""}else{continue}}var n=e[t];e[t]=T.trim(e[t]);for(var a=0,p=$.length;a<p;a++){var d=$[a];if(e[t].match&&!e[t].match(/^0\d+/)&&e[t].match(d.match)){var i=e[t].replace(/[^\d\.\-]/g,"");if(d.fmt){i=d.fmt(i)}if(e[t].match(/^\d{15,}$/)){r=_(u,"c",{attr:{t:"inlineStr",r:o},children:{row:_(u,"is",{children:{row:_(u,"t",{text:i})}})}})}else{r=_(u,"c",{attr:{r:o,s:d.style},children:[_(u,"v",{text:i})]})}break}}if(!r){if(typeof e[t]==="number"||e[t].match&&e[t].match(/^-?\d+(\.\d+)?$/)&&!e[t].match(/^0\d+/)){r=_(u,"c",{attr:{t:"n",r:o},children:[_(u,"v",{text:e[t]})]})}else{var f=!n.replace?n:n.replace(/[\x00-\x09\x0B\x0C\x0E-\x1F\x7F-\x9F]/g,"");r=_(u,"c",{attr:{t:"inlineStr",r:o},children:{row:_(u,"is",{children:{row:_(u,"t",{text:f,attr:{"xml:space":"preserve"}})}})}})}}I.appendChild(r)}c.appendChild(I);m++};if(s.customizeData){s.customizeData(d)}var f=function(e,t){var l=T("mergeCells",u);l[0].appendChild(_(u,"mergeCell",{attr:{ref:"A"+e+":"+A(t)+e}}));l.attr("count",parseFloat(l.attr("count"))+1);T("row:eq("+(e-1)+") c",u).attr("s","51")};var F=t.buttons.exportInfo(s);if(F.title){i([F.title],m);f(m,d.header.length-1)}if(F.messageTop){i([F.messageTop],m);f(m,d.header.length-1)}if(s.header){i(d.header,m);T("row:last c",u).attr("s","2")}r=m;for(var x=0,h=d.body.length;x<h;x++){i(d.body[x],m)}n=m;if(s.footer&&d.footer){i(d.footer,m);T("row:last c",u).attr("s","2")}if(F.messageBottom){i([F.messageBottom],m);f(m,d.header.length-1)}var b=_(u,"cols");T("worksheet",u).prepend(b);for(var g=0,v=d.header.length;g<v;g++){b.appendChild(_(u,"col",{attr:{min:g+1,max:g+1,width:E(d,g),customWidth:1}}))}var w=p.xl["workbook.xml"];T("sheets sheet",w).attr("name",z(s));if(s.autoFilter){T("mergeCells",u).before(_(u,"autoFilter",{attr:{ref:"A"+r+":"+A(d.header.length-1)+n}}));T("definedNames",w).append(_(w,"definedName",{attr:{name:"_xlnm._FilterDatabase",localSheetId:"0",hidden:1},text:z(s)+"!$A$"+r+":"+A(d.header.length-1)+n}))}if(s.customize){s.customize(p,s,t)}if(T("mergeCells",u).children().length===0){T("mergeCells",u).remove()}var B=N();var C=new B;var k={type:"blob",mimeType:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"};D(C,p);if(C.generateAsync){C.generateAsync(k).then(function(e){O(e,F.filename);o.processing(false)})}else{O(C.generate(k),F.filename);this.processing(false)}},filename:"*",extension:".xlsx",exportOptions:{},header:true,footer:false,title:"*",messageTop:"*",messageBottom:"*",createEmptyCells:false,autoFilter:false,sheetName:""};o.ext.buttons.pdfHtml5={className:"buttons-pdf buttons-html5",available:function(){return e.FileReader!==S&&m()},text:function(e){return e.i18n("buttons.pdf","PDF")},action:function(e,t,l,o){this.processing(true);var r=this;var n=t.buttons.exportData(o.exportOptions);var a=t.buttons.exportInfo(o);var p=[];if(o.header){p.push(T.map(n.header,function(e){return{text:typeof e==="string"?e:e+"",style:"tableHeader"}}))}for(var d=0,i=n.body.length;d<i;d++){p.push(T.map(n.body[d],function(e){if(e===null||e===S){e=""}return{text:typeof e==="string"?e:e+"",style:d%2?"tableBodyEven":"tableBodyOdd"}}))}if(o.footer&&n.footer){p.push(T.map(n.footer,function(e){return{text:typeof e==="string"?e:e+"",style:"tableFooter"}}))}var f={pageSize:o.pageSize,pageOrientation:o.orientation,content:[{table:{headerRows:1,body:p},layout:"noBorders"}],styles:{tableHeader:{bold:true,fontSize:11,color:"white",fillColor:"#2d4154",alignment:"center"},tableBodyEven:{},tableBodyOdd:{fillColor:"#f3f3f3"},tableFooter:{bold:true,fontSize:11,color:"white",fillColor:"#2d4154"},title:{alignment:"center",fontSize:15},message:{}},defaultStyle:{fontSize:10}};if(a.messageTop){f.content.unshift({text:a.messageTop,style:"message",margin:[0,0,0,12]})}if(a.messageBottom){f.content.push({text:a.messageBottom,style:"message",margin:[0,0,0,12]})}if(a.title){f.content.unshift({text:a.title,style:"title",margin:[0,0,0,12]})}if(o.customize){o.customize(f,o,t)}var s=m().createPdf(f);if(o.download==="open"&&!u()){s.open()}else{s.download(a.filename)}this.processing(false)},title:"*",filename:"*",extension:".pdf",exportOptions:{},orientation:"portrait",pageSize:"A4",header:true,footer:false,messageTop:"*",messageBottom:"*",customize:null,download:"download"};return o.Buttons});
/*!
 Column visibility buttons for Buttons and DataTables.
 2016 SpryMedia Ltd - datatables.net/license
*/
(function(f){"function"===typeof define&&define.amd?define(["jquery","datatables.net","datatables.net-buttons"],function(c){return f(c,window,document)}):"object"===typeof exports?module.exports=function(c,e){c||(c=window);e&&e.fn.dataTable||(e=require("datatables.net")(c,e).$);e.fn.dataTable.Buttons||require("datatables.net-buttons")(c,e);return f(e,c,c.document)}:f(jQuery,window,document)})(function(f,c,e,h){c=f.fn.dataTable;f.extend(c.ext.buttons,{colvis:function(a,b){return{extend:"collection",
text:function(b){return b.i18n("buttons.colvis","Column visibility")},className:"buttons-colvis",buttons:[{extend:"columnsToggle",columns:b.columns,columnText:b.columnText}]}},columnsToggle:function(a,b){return a.columns(b.columns).indexes().map(function(a){return{extend:"columnToggle",columns:a,columnText:b.columnText}}).toArray()},columnToggle:function(a,b){return{extend:"columnVisibility",columns:b.columns,columnText:b.columnText}},columnsVisibility:function(a,b){return a.columns(b.columns).indexes().map(function(a){return{extend:"columnVisibility",
columns:a,visibility:b.visibility,columnText:b.columnText}}).toArray()},columnVisibility:{columns:h,text:function(a,b,d){return d._columnText(a,d)},className:"buttons-columnVisibility",action:function(a,b,d,g){a=b.columns(g.columns);b=a.visible();a.visible(g.visibility!==h?g.visibility:!(b.length&&b[0]))},init:function(a,b,d){var g=this;b.attr("data-cv-idx",d.columns);a.on("column-visibility.dt"+d.namespace,function(b,c){c.bDestroying||c.nTable!=a.settings()[0].nTable||g.active(a.column(d.columns).visible())}).on("column-reorder.dt"+
d.namespace,function(c,e,f){1===a.columns(d.columns).count()&&(b.text(d._columnText(a,d)),g.active(a.column(d.columns).visible()))});this.active(a.column(d.columns).visible())},destroy:function(a,b,d){a.off("column-visibility.dt"+d.namespace).off("column-reorder.dt"+d.namespace)},_columnText:function(a,b){var d=a.column(b.columns).index(),c=a.settings()[0].aoColumns[d].sTitle.replace(/\n/g," ").replace(/<br\s*\/?>/gi," ").replace(/<select(.*?)<\/select>/g,"").replace(/<!\-\-.*?\-\->/g,"").replace(/<.*?>/g,
"").replace(/^\s+|\s+$/g,"");return b.columnText?b.columnText(a,d,c):c}},colvisRestore:{className:"buttons-colvisRestore",text:function(a){return a.i18n("buttons.colvisRestore","Restore visibility")},init:function(a,b,d){d._visOriginal=a.columns().indexes().map(function(b){return a.column(b).visible()}).toArray()},action:function(a,b,d,c){b.columns().every(function(a){a=b.colReorder&&b.colReorder.transpose?b.colReorder.transpose(a,"toOriginal"):a;this.visible(c._visOriginal[a])})}},colvisGroup:{className:"buttons-colvisGroup",
action:function(a,b,d,c){b.columns(c.show).visible(!0,!1);b.columns(c.hide).visible(!1,!1);b.columns.adjust()},show:[],hide:[]}});return c.Buttons});
