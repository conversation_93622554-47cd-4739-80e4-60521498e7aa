(function (c, m) {
  typeof exports == "object" && typeof module < "u"
    ? m(exports)
    : typeof define == "function" && define.amd
    ? define(["exports"], m)
    : ((c = typeof globalThis < "u" ? globalThis : c || self),
      m((c["roadmap-renderer"] = {})));
})(this, function (c) {
  "use strict";

  const markers = [];

  function m(f) {
    let e = (f >> 16) & 255,
      t = (f >> 8) & 255,
      r = f & 255;
    return `rgb(${e},${t},${r})`;
  }
  function l(f, e = {}, t) {
    let r = document.createElementNS("http://www.w3.org/2000/svg", f);
    for (let a in e) e.hasOwnProperty(a) && r.setAttribute(a, e[a]);
    return t && t.appendChild(r), r;
  }
  const u = 2.7,
    $ = 4,
    I = 2,
    w = {
      black: ["#000"],
      gray: ["#000", "#333", "#666", "#999", "#ccc", "#ddd", "#eee"],
      white: ["#fff"],
      red: ["#cf2a27", "#ea9999", "#eo6666", "#cc0000", "#990000", "#660000"],
      orange: [
        "#ff9900",
        "#f9cb9c",
        "#f6b26b",
        "#e69138",
        "#b45f06",
        "#783f04",
      ],
      yellow: [
        "#ffff00",
        "#ffe599",
        "#ffd966",
        "#f1c232",
        "#bf9000",
        "#7f6000",
      ],
      green: ["#009e0f", "#b6d7a8", "#93c47d", "#6aa84f", "#38761d", "#274e13"],
      cyan: ["#00ffff", "#a2c4c9", "#76a5af", "#45818e", "#134f5c", "#0c343d"],
      blue: ["#2b78e4", "#9fc5f8", "#6fa8dc", "#597eaa", "#085394", "#073763"], //
      purple: [
        "#9900ff",
        "#b4a7d6",
        "#8e7cc3",
        "#674ea7",
        "#351c75",
        "#20124d",
      ],
      pink: ["#ff00ff", "#d5a6bd", "#c27ba0", "#a64d79", "#741b47", "#4c1130"],
    };
  class b {
    constructor(e, t) {
      (this.svgRoot = e),
        (this.fontFamily = t),
        (this.canvasRenderingContext2D = document
          .createElement("canvas")
          .getContext("2d"));
    }
    render(e, t) {
      let r = e.typeID;
      r in this
        ? this[r](e, t)
        : console.log(`'${r}' control type not implemented`);
    }
    parseColor(e, t) {
      return e === void 0 ? `rgb(${t})` : m(e);
    }
    parseFontProperties(e) {
      var t, r, a;
      return {
        style: (t = e.properties) != null && t.italic ? "italic" : "normal",
        weight: (r = e.properties) != null && r.bold ? "bold" : "normal",
        size:
          (a = e.properties) != null && a.size
            ? e.properties.size + "px"
            : "13px",
        family: this.fontFamily,
      };
    }
    measureText(e, t) {
      return (
        (this.canvasRenderingContext2D.font = t),
        this.canvasRenderingContext2D.measureText(e)
      );
    }
    drawRectangle(e, t) {
      var r, a, s;
      l(
        "rect",
        {
          x: parseInt(e.x) + u / 2,
          y: parseInt(e.y) + u / 2,
          width: parseInt(e.w ?? e.measuredW) - u,
          height: parseInt(e.h ?? e.measuredH) - u,
          rx: (e.properties && e.properties.borderStyle === "roundedSolid") ? 15 : I, // 增加圆角判定
          fill: this.parseColor(
            (r = e.properties) == null ? void 0 : r.color,
            "255,255,255"
          ),
          "fill-opacity":
            ((a = e.properties) == null ? void 0 : a.backgroundAlpha) ?? 1,
          stroke: this.parseColor(
            (s = e.properties) == null ? void 0 : s.borderColor,
            "0,0,0"
          ),
          "stroke-width": u,
        },
        t
      );
    }
    addText(e, t, r, a) {
      let s = e.properties.text ?? "",
        i = parseInt(e.x),
        p = parseInt(e.y),
        n = this.parseFontProperties(e),
        d = this.measureText(s, `${n.style} ${n.weight} ${n.size} ${n.family}`),
        x = a === "center" ? i + (e.w ?? e.measuredW) / 2 - d.width / 2 : i,
        g = p + e.measuredH / 2 + d.actualBoundingBoxAscent / 2,
        h = l(
          "text",
          {
            x,
            y: g,
            fill: r,
            "font-style": n.style,
            "font-weight": n.weight,
            "font-size": n.size,
          },
          t
        );
      if (!s.includes("{color:")) {
        let y = l("tspan", {}, h);
        y.textContent = s;
        return;
      }
      s.split(/{color:|{color}/).forEach((y) => {
        if (y.includes("}")) {
          let [o, C] = y.split("}");
          if (!o.startsWith("#")) {
            let k = parseInt(o.slice(-1));
            o = isNaN(k) ? w[o][0] : w[o][k];
          }
          let T = l("tspan", { fill: o }, h);
          T.textContent = C;
        } else {
          let o = l("tspan", {}, h);
          o.textContent = y;
        }
      });
    }
    TextArea(e, t) {
      this.drawRectangle(e, t);
    }
    Canvas(e, t) {
      this.drawRectangle(e, t);
    }
    Label(e, t) {
      var r;
      this.addText(
        e,
        t,
        this.parseColor((r = e.properties) == null ? void 0 : r.color, "0,0,0"),
        "left"
      );
    }
    TextInput(e, t) {
      var r;
      this.drawRectangle(e, t),
        this.addText(
          e,
          t,
          this.parseColor(
            (r = e.properties) == null ? void 0 : r.textColor,
            "0,0,0"
          ),
          "center"
        );
    }
    Arrow(e, t) {
      var x, g, h;
      let r = parseInt(e.x),
        a = parseInt(e.y),
        { p0: s, p1: i, p2: p } = e.properties,
        n;
      ((x = e.properties) == null ? void 0 : x.stroke) === "dotted"
        ? (n = "0.8 12")
        : ((g = e.properties) == null ? void 0 : g.stroke) === "dashed" &&
          (n = "28 46");
      let d = { x: (p.x - s.x) * i.x, y: (p.y - s.y) * i.x };

      /* 增加箭头 START */
      let markerStart = "url(#)", markerEnd = "url(#)";

      if (e.properties.color !== null) {
        if (!markers.includes(e.properties.color)) {
          markers.push(e.properties.color);
        }
        if (e.properties.leftArrow == null || e.properties.leftArrow === "true") {
          markerStart = "url(#arrowhead_" + e.properties.color + ")";
        }
        if (e.properties.rightArrow == null || e.properties.rightArrow === "true") {
          markerEnd = "url(#arrowhead_" + e.properties.color + ")";
        }
      }
      /* 增加箭头 END */

      l(
        "path",
        {
          d: `M${r + s.x} ${a + s.y}Q${r + s.x + d.x + d.y * i.y * 3.6} ${
            a + s.y + d.y + -d.x * i.y * 3.6
          } ${r + p.x} ${a + p.y}`,
          fill: "none",
          stroke: this.parseColor(
            (h = e.properties) == null ? void 0 : h.color,
            "0,0,0"
          ),
          "marker-start": markerStart,
          "marker-end": markerEnd,
          "stroke-width": $,
          "stroke-linecap": "round",
          "stroke-linejoin": "round",
          "stroke-dasharray": n,
        },
        t
      );
    }
    Icon(e, t) {
      var i;
      let r = parseInt(e.x),
        a = parseInt(e.y),
        s = 10;
      l(
        "circle",
        {
          cx: r + s,
          cy: a + s,
          r: s,
          fill: this.parseColor(
            (i = e.properties) == null ? void 0 : i.color,
            "0,0,0"
          ),
        },
        t
      ),
        e.properties.icon.ID === "check-circle" &&
          l(
            "path",
            {
              d: `M${r + 4.5} ${a + s}L${r + 8.5} ${a + s + 4} ${r + 15} ${
                a + s - 2.5
              }`,
              fill: "none",
              stroke: "#fff",
              "stroke-width": 3.5,
              "stroke-linecap": "round",
              "stroke-linejoin": "round",
            },
            t
          );
    }
    HRule(e, t) {
      var i, p, n;
      let r = parseInt(e.x),
        a = parseInt(e.y),
        s;
      ((i = e.properties) == null ? void 0 : i.stroke) === "dotted"
        ? (s = "0.8, 8")
        : ((p = e.properties) == null ? void 0 : p.stroke) === "dashed" &&
          (s = "18, 30"),
        l(
          "path",
          {
            d: `M${r} ${a}L${r + parseInt(e.w ?? e.measuredW)} ${a}`,
            fill: "none",
            stroke: this.parseColor(
              (n = e.properties) == null ? void 0 : n.color,
              "0,0,0"
            ),
            "stroke-width": u,
            "stroke-linecap": "round",
            "stroke-linejoin": "round",
            "stroke-dasharray": s,
          },
          t
        );
    }
    __group__(e, t) {
      var s;
      const r =
        ((s = e == null ? void 0 : e.properties) == null
          ? void 0
          : s.controlName) || "";
      let a = l(
        "g",
        { ...(r ? { class: "clickable-group", "data-group-id": r } : {}) },
        t
      );
      e.children.controls.control
        .sort((i, p) => i.zOrder - p.zOrder)
        .forEach((i) => {
          (i.x = parseInt(i.x, 10) + parseInt(e.x, 10)),
            (i.y = parseInt(i.y, 10) + parseInt(e.y, 10)),
            this.render(i, a);
        });
    }
  }
  async function R(f, e = {}) {
    if (
      (e = {
        padding: 5,
        // fontFamily: "balsamiq",
        // fontURL:
        //   "../fonts/balsamiqsans.woff2",
        ...e,
      })
    ) {
      // let d = new FontFace(e.fontFamily, `url(${e.fontURL})`);
      // await d.load(), document.fonts.add && document.fonts.add(d);
    }
    let t = f.mockup,
      r = t.measuredW - t.mockupW - e.padding,
      a = t.measuredH - t.mockupH - e.padding,
      s = parseInt(t.mockupW) + e.padding * 2,
      i = parseInt(t.mockupH) + e.padding * 2,
      p = l("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
        viewBox: `${r} ${a} ${s} ${i}`,
        style: "font-family: balsamiq, Yozai",
      }),
      defs = l("defs", {}, p);

    let n = new b(p, e.fontFamily);

    t.controls.control
      .sort((d, x) => d.zOrder - x.zOrder)
      .forEach((d) => {
        n.render(d, p);
      });

    markers.forEach((marker) => {
      l(
        "polyline",
        {
          "stroke-linecap": "round",
          "stroke-linejoin": "round",
          points: "-5,-4 0,0 -5,4 -5,-4",
          style:
            "stroke: " +
            m(parseInt(marker, 10)) +
            "; fill: " +
            m(parseInt(marker, 10)) +
            "; stroke-width: 3;",
        },
        l(
          "marker",
          {
            id: "arrowhead_" + marker,
            markerWidth: "6",
            markerHeight: "6",
            viewBox: "-10 -10 20 20",
            markerUnits: "strokeWidth",
            orient: "auto-start-reverse",
            refX: "0",
            refY: "0",
          },
          defs
        )
      );
    });

    return p;
  }
  (c.wireframeJSONToSVG = R),
    Object.defineProperty(c, Symbol.toStringTag, { value: "Module" });
});
