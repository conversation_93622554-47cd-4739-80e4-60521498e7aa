<@p.page title="基础数据">

  <@p.menu module="base">
  </@p.menu>

  <div class="row">
    <div class="col">
      <#-- 专业 -->
      <#assign majors = sqlt.sqlQueryForList("obe.getMajors")>
      <#assign coreMajors = majors?filter(x -> !x.majcode?has_content)>
      <#assign title><b>专业</b><span class="tag tag-rounded ml-4">共 ${majors?size} 个<#if coreMajors?size lt majors?size>，其中核心专业 ${coreMajors?size} 个</#if></span></#assign>
      <@p.card title=title>
        <table class="table table-bordered table-vcenter table-sm table-hover">
          <thead>
            <tr>
              <th class="text-center text-nowrap">专业代码</th>
              <th class="text-center text-nowrap">专业名称</th>
            </tr>
          </thead>
          <tbody>
            <#list majors as item>
              <tr <#if !item.majcode?has_content>class="font-weight-bold"</#if>>
                <td class="text-center text-nowrap">${item.majid}</td>
                <td class="text-nowrap px-3">${item.majname}</td>
              </tr>
            </#list>
          </tbody>
        </table>
      </@p.card>

      <#-- 课程类型 -->
      <#assign courseTypes = sqlt.sqlQueryForList("obe.getCourseTypes")>
      <#assign stdCourseTypes = courseTypes?filter(x -> x.typeseq?has_content)>
      <#assign title><b>课程类型</b><span class="tag tag-rounded ml-4">共 ${courseTypes?size} 项<#if stdCourseTypes?size lt courseTypes?size>，其中标准类型 ${stdCourseTypes?size} 项，自定义类型 ${courseTypes?size - stdCourseTypes?size} 项</#if></span></#assign>
      <@p.card title=title>
        <table class="table table-bordered table-vcenter table-sm table-hover">
          <thead>
            <tr>
              <th class="text-center text-nowrap">序号</th>
              <th class="text-center text-nowrap">类型名称</th>
            </tr>
          </thead>
          <tbody>
            <#list courseTypes as item>
              <tr>
                <td class="text-center text-nowrap">${item.typeseq!}</td>
                <td class="text-center text-nowrap">${item.typename}</td>
              </tr>
            </#list>
          </tbody>
        </table>
      </@p.card>
    </div>
    <div class="col">
      <#-- 开课单位 -->
      <#assign depts = sqlt.sqlQueryForList("obe.getDepts")>
      <#assign deptsHasCourse = depts?filter(x -> x.coursenum gt 0)>
      <#assign title><b>开课单位</b><span class="tag tag-rounded ml-4">共 ${sqlt.sqlQueryForList("obe.getDepts")?size} 个<#if deptsHasCourse?size lt depts?size>，其中已开课 ${deptsHasCourse?size} 个</#if></span></#assign>
      <@p.card title=title>
        <table class="table table-bordered table-vcenter table-sm table-hover">
          <thead>
            <tr>
              <th class="text-center text-nowrap">单位代码</th>
              <th class="text-center text-nowrap">单位名称</th>
              <th class="text-center text-nowrap">是否开课</th>
            </tr>
          </thead>
          <tbody>
            <#list depts as item>
              <tr>
                <td class="text-center text-nowrap">${item.deptid}</td>
                <td class="text-center text-nowrap">
                  ${item.deptname}
                  <#if item.deptnote?has_content><span class="text-muted" data-toggle="tooltip" data-placement="top" title="${item.deptnote}"><@p.icon name="fe-help-circle" /></span></#if>
                </td>
                <td class="text-center text-nowrap">${(item.coursenum > 0)?then("是", "")}</td>
              </tr>
            </#list>
          </tbody>
        </table>
        <@app.noticeInfo id="depts" />
      </@p.card>
    </div>
  </div>

</@p.page>
