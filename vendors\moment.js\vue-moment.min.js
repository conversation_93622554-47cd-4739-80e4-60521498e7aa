!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e(t.vueMoment={})}(this,function(t){"use strict";var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function n(){throw new Error("Dynamic requires are not currently supported by rollup-plugin-commonjs")}var i,s=(function(t,i){var s,r,a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};s=e,r=function(){var e,i;function s(){return e.apply(null,arguments)}function r(t){return t instanceof Array||"[object Array]"===Object.prototype.toString.call(t)}function o(t){return null!=t&&"[object Object]"===Object.prototype.toString.call(t)}function u(t){return void 0===t}function l(t){return"number"==typeof t||"[object Number]"===Object.prototype.toString.call(t)}function d(t){return t instanceof Date||"[object Date]"===Object.prototype.toString.call(t)}function h(t,e){var n,i=[];for(n=0;n<t.length;++n)i.push(e(t[n],n));return i}function c(t,e){return Object.prototype.hasOwnProperty.call(t,e)}function f(t,e){for(var n in e)c(e,n)&&(t[n]=e[n]);return c(e,"toString")&&(t.toString=e.toString),c(e,"valueOf")&&(t.valueOf=e.valueOf),t}function m(t,e,n,i){return We(t,e,n,i,!0).utc()}function _(t){return null==t._pf&&(t._pf={empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1,parsedDateParts:[],meridiem:null,rfc2822:!1,weekdayMismatch:!1}),t._pf}function y(t){if(null==t._isValid){var e=_(t),n=i.call(e.parsedDateParts,function(t){return null!=t}),s=!isNaN(t._d.getTime())&&e.overflow<0&&!e.empty&&!e.invalidMonth&&!e.invalidWeekday&&!e.weekdayMismatch&&!e.nullInput&&!e.invalidFormat&&!e.userInvalidated&&(!e.meridiem||e.meridiem&&n);if(t._strict&&(s=s&&0===e.charsLeftOver&&0===e.unusedTokens.length&&void 0===e.bigHour),null!=Object.isFrozen&&Object.isFrozen(t))return s;t._isValid=s}return t._isValid}function p(t){var e=m(NaN);return null!=t?f(_(e),t):_(e).userInvalidated=!0,e}i=Array.prototype.some?Array.prototype.some:function(t){for(var e=Object(this),n=e.length>>>0,i=0;i<n;i++)if(i in e&&t.call(this,e[i],i,e))return!0;return!1};var g=s.momentProperties=[];function v(t,e){var n,i,s;if(u(e._isAMomentObject)||(t._isAMomentObject=e._isAMomentObject),u(e._i)||(t._i=e._i),u(e._f)||(t._f=e._f),u(e._l)||(t._l=e._l),u(e._strict)||(t._strict=e._strict),u(e._tzm)||(t._tzm=e._tzm),u(e._isUTC)||(t._isUTC=e._isUTC),u(e._offset)||(t._offset=e._offset),u(e._pf)||(t._pf=_(e)),u(e._locale)||(t._locale=e._locale),g.length>0)for(n=0;n<g.length;n++)u(s=e[i=g[n]])||(t[i]=s);return t}var w=!1;function S(t){v(this,t),this._d=new Date(null!=t._d?t._d.getTime():NaN),this.isValid()||(this._d=new Date(NaN)),!1===w&&(w=!0,s.updateOffset(this),w=!1)}function M(t){return t instanceof S||null!=t&&null!=t._isAMomentObject}function k(t){return t<0?Math.ceil(t)||0:Math.floor(t)}function D(t){var e=+t,n=0;return 0!==e&&isFinite(e)&&(n=k(e)),n}function Y(t,e,n){var i,s=Math.min(t.length,e.length),r=Math.abs(t.length-e.length),a=0;for(i=0;i<s;i++)(n&&t[i]!==e[i]||!n&&D(t[i])!==D(e[i]))&&a++;return a+r}function O(t){!1===s.suppressDeprecationWarnings&&"undefined"!=typeof console&&console.warn&&console.warn("Deprecation warning: "+t)}function b(t,e){var n=!0;return f(function(){if(null!=s.deprecationHandler&&s.deprecationHandler(null,t),n){for(var i,r=[],o=0;o<arguments.length;o++){if(i="","object"===a(arguments[o])){for(var u in i+="\n["+o+"] ",arguments[0])i+=u+": "+arguments[0][u]+", ";i=i.slice(0,-2)}else i=arguments[o];r.push(i)}O(t+"\nArguments: "+Array.prototype.slice.call(r).join("")+"\n"+(new Error).stack),n=!1}return e.apply(this,arguments)},e)}var T,x={};function P(t,e){null!=s.deprecationHandler&&s.deprecationHandler(t,e),x[t]||(O(e),x[t]=!0)}function W(t){return t instanceof Function||"[object Function]"===Object.prototype.toString.call(t)}function C(t,e){var n,i=f({},t);for(n in e)c(e,n)&&(o(t[n])&&o(e[n])?(i[n]={},f(i[n],t[n]),f(i[n],e[n])):null!=e[n]?i[n]=e[n]:delete i[n]);for(n in t)c(t,n)&&!c(e,n)&&o(t[n])&&(i[n]=f({},i[n]));return i}function H(t){null!=t&&this.set(t)}s.suppressDeprecationWarnings=!1,s.deprecationHandler=null,T=Object.keys?Object.keys:function(t){var e,n=[];for(e in t)c(t,e)&&n.push(e);return n};var R={};function F(t,e){var n=t.toLowerCase();R[n]=R[n+"s"]=R[e]=t}function U(t){return"string"==typeof t?R[t]||R[t.toLowerCase()]:void 0}function L(t){var e,n,i={};for(n in t)c(t,n)&&(e=U(n))&&(i[e]=t[n]);return i}var V={};function N(t,e){V[t]=e}function G(t,e,n){var i=""+Math.abs(t),s=e-i.length;return(t>=0?n?"+":"":"-")+Math.pow(10,Math.max(0,s)).toString().substr(1)+i}var A=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|YYYYYY|YYYYY|YYYY|YY|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,E=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,j={},I={};function z(t,e,n,i){var s=i;"string"==typeof i&&(s=function(){return this[i]()}),t&&(I[t]=s),e&&(I[e[0]]=function(){return G(s.apply(this,arguments),e[1],e[2])}),n&&(I[n]=function(){return this.localeData().ordinal(s.apply(this,arguments),t)})}function Z(t,e){return t.isValid()?(e=$(e,t.localeData()),j[e]=j[e]||function(t){var e,n,i,s=t.match(A);for(e=0,n=s.length;e<n;e++)I[s[e]]?s[e]=I[s[e]]:s[e]=(i=s[e]).match(/\[[\s\S]/)?i.replace(/^\[|\]$/g,""):i.replace(/\\/g,"");return function(e){var i,r="";for(i=0;i<n;i++)r+=W(s[i])?s[i].call(e,t):s[i];return r}}(e),j[e](t)):t.localeData().invalidDate()}function $(t,e){var n=5;function i(t){return e.longDateFormat(t)||t}for(E.lastIndex=0;n>=0&&E.test(t);)t=t.replace(E,i),E.lastIndex=0,n-=1;return t}var q=/\d/,J=/\d\d/,B=/\d{3}/,Q=/\d{4}/,X=/[+-]?\d{6}/,K=/\d\d?/,tt=/\d\d\d\d?/,et=/\d\d\d\d\d\d?/,nt=/\d{1,3}/,it=/\d{1,4}/,st=/[+-]?\d{1,6}/,rt=/\d+/,at=/[+-]?\d+/,ot=/Z|[+-]\d\d:?\d\d/gi,ut=/Z|[+-]\d\d(?::?\d\d)?/gi,lt=/[0-9]{0,256}['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFF07\uFF10-\uFFEF]{1,256}|[\u0600-\u06FF\/]{1,256}(\s*?[\u0600-\u06FF]{1,256}){1,2}/i,dt={};function ht(t,e,n){dt[t]=W(e)?e:function(t,i){return t&&n?n:e}}function ct(t,e){return c(dt,t)?dt[t](e._strict,e._locale):new RegExp(ft(t.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,function(t,e,n,i,s){return e||n||i||s})))}function ft(t){return t.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}var mt={};function _t(t,e){var n,i=e;for("string"==typeof t&&(t=[t]),l(e)&&(i=function(t,n){n[e]=D(t)}),n=0;n<t.length;n++)mt[t[n]]=i}function yt(t,e){_t(t,function(t,n,i,s){i._w=i._w||{},e(t,i._w,i,s)})}function pt(t,e,n){null!=e&&c(mt,t)&&mt[t](e,n._a,n,t)}var gt=0,vt=1,wt=2,St=3,Mt=4,kt=5,Dt=6,Yt=7,Ot=8;function bt(t){return Tt(t)?366:365}function Tt(t){return t%4==0&&t%100!=0||t%400==0}z("Y",0,0,function(){var t=this.year();return t<=9999?""+t:"+"+t}),z(0,["YY",2],0,function(){return this.year()%100}),z(0,["YYYY",4],0,"year"),z(0,["YYYYY",5],0,"year"),z(0,["YYYYYY",6,!0],0,"year"),F("year","y"),N("year",1),ht("Y",at),ht("YY",K,J),ht("YYYY",it,Q),ht("YYYYY",st,X),ht("YYYYYY",st,X),_t(["YYYYY","YYYYYY"],gt),_t("YYYY",function(t,e){e[gt]=2===t.length?s.parseTwoDigitYear(t):D(t)}),_t("YY",function(t,e){e[gt]=s.parseTwoDigitYear(t)}),_t("Y",function(t,e){e[gt]=parseInt(t,10)}),s.parseTwoDigitYear=function(t){return D(t)+(D(t)>68?1900:2e3)};var xt,Pt=Wt("FullYear",!0);function Wt(t,e){return function(n){return null!=n?(Ht(this,t,n),s.updateOffset(this,e),this):Ct(this,t)}}function Ct(t,e){return t.isValid()?t._d["get"+(t._isUTC?"UTC":"")+e]():NaN}function Ht(t,e,n){t.isValid()&&!isNaN(n)&&("FullYear"===e&&Tt(t.year())&&1===t.month()&&29===t.date()?t._d["set"+(t._isUTC?"UTC":"")+e](n,t.month(),Rt(n,t.month())):t._d["set"+(t._isUTC?"UTC":"")+e](n))}function Rt(t,e){if(isNaN(t)||isNaN(e))return NaN;var n,i=(e%(n=12)+n)%n;return t+=(e-i)/12,1===i?Tt(t)?29:28:31-i%7%2}xt=Array.prototype.indexOf?Array.prototype.indexOf:function(t){var e;for(e=0;e<this.length;++e)if(this[e]===t)return e;return-1},z("M",["MM",2],"Mo",function(){return this.month()+1}),z("MMM",0,0,function(t){return this.localeData().monthsShort(this,t)}),z("MMMM",0,0,function(t){return this.localeData().months(this,t)}),F("month","M"),N("month",8),ht("M",K),ht("MM",K,J),ht("MMM",function(t,e){return e.monthsShortRegex(t)}),ht("MMMM",function(t,e){return e.monthsRegex(t)}),_t(["M","MM"],function(t,e){e[vt]=D(t)-1}),_t(["MMM","MMMM"],function(t,e,n,i){var s=n._locale.monthsParse(t,i,n._strict);null!=s?e[vt]=s:_(n).invalidMonth=t});var Ft=/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?/,Ut="January_February_March_April_May_June_July_August_September_October_November_December".split("_");var Lt="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_");function Vt(t,e){var n;if(!t.isValid())return t;if("string"==typeof e)if(/^\d+$/.test(e))e=D(e);else if(!l(e=t.localeData().monthsParse(e)))return t;return n=Math.min(t.date(),Rt(t.year(),e)),t._d["set"+(t._isUTC?"UTC":"")+"Month"](e,n),t}function Nt(t){return null!=t?(Vt(this,t),s.updateOffset(this,!0),this):Ct(this,"Month")}var Gt=lt;var At=lt;function Et(){function t(t,e){return e.length-t.length}var e,n,i=[],s=[],r=[];for(e=0;e<12;e++)n=m([2e3,e]),i.push(this.monthsShort(n,"")),s.push(this.months(n,"")),r.push(this.months(n,"")),r.push(this.monthsShort(n,""));for(i.sort(t),s.sort(t),r.sort(t),e=0;e<12;e++)i[e]=ft(i[e]),s[e]=ft(s[e]);for(e=0;e<24;e++)r[e]=ft(r[e]);this._monthsRegex=new RegExp("^("+r.join("|")+")","i"),this._monthsShortRegex=this._monthsRegex,this._monthsStrictRegex=new RegExp("^("+s.join("|")+")","i"),this._monthsShortStrictRegex=new RegExp("^("+i.join("|")+")","i")}function jt(t){var e;if(t<100&&t>=0){var n=Array.prototype.slice.call(arguments);n[0]=t+400,e=new Date(Date.UTC.apply(null,n)),isFinite(e.getUTCFullYear())&&e.setUTCFullYear(t)}else e=new Date(Date.UTC.apply(null,arguments));return e}function It(t,e,n){var i=7+e-n;return-((7+jt(t,0,i).getUTCDay()-e)%7)+i-1}function zt(t,e,n,i,s){var r,a,o=1+7*(e-1)+(7+n-i)%7+It(t,i,s);return o<=0?a=bt(r=t-1)+o:o>bt(t)?(r=t+1,a=o-bt(t)):(r=t,a=o),{year:r,dayOfYear:a}}function Zt(t,e,n){var i,s,r=It(t.year(),e,n),a=Math.floor((t.dayOfYear()-r-1)/7)+1;return a<1?i=a+$t(s=t.year()-1,e,n):a>$t(t.year(),e,n)?(i=a-$t(t.year(),e,n),s=t.year()+1):(s=t.year(),i=a),{week:i,year:s}}function $t(t,e,n){var i=It(t,e,n),s=It(t+1,e,n);return(bt(t)-i+s)/7}z("w",["ww",2],"wo","week"),z("W",["WW",2],"Wo","isoWeek"),F("week","w"),F("isoWeek","W"),N("week",5),N("isoWeek",5),ht("w",K),ht("ww",K,J),ht("W",K),ht("WW",K,J),yt(["w","ww","W","WW"],function(t,e,n,i){e[i.substr(0,1)]=D(t)});function qt(t,e){return t.slice(e,7).concat(t.slice(0,e))}z("d",0,"do","day"),z("dd",0,0,function(t){return this.localeData().weekdaysMin(this,t)}),z("ddd",0,0,function(t){return this.localeData().weekdaysShort(this,t)}),z("dddd",0,0,function(t){return this.localeData().weekdays(this,t)}),z("e",0,0,"weekday"),z("E",0,0,"isoWeekday"),F("day","d"),F("weekday","e"),F("isoWeekday","E"),N("day",11),N("weekday",11),N("isoWeekday",11),ht("d",K),ht("e",K),ht("E",K),ht("dd",function(t,e){return e.weekdaysMinRegex(t)}),ht("ddd",function(t,e){return e.weekdaysShortRegex(t)}),ht("dddd",function(t,e){return e.weekdaysRegex(t)}),yt(["dd","ddd","dddd"],function(t,e,n,i){var s=n._locale.weekdaysParse(t,i,n._strict);null!=s?e.d=s:_(n).invalidWeekday=t}),yt(["d","e","E"],function(t,e,n,i){e[i]=D(t)});var Jt="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_");var Bt="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_");var Qt="Su_Mo_Tu_We_Th_Fr_Sa".split("_");var Xt=lt;var Kt=lt;var te=lt;function ee(){function t(t,e){return e.length-t.length}var e,n,i,s,r,a=[],o=[],u=[],l=[];for(e=0;e<7;e++)n=m([2e3,1]).day(e),i=this.weekdaysMin(n,""),s=this.weekdaysShort(n,""),r=this.weekdays(n,""),a.push(i),o.push(s),u.push(r),l.push(i),l.push(s),l.push(r);for(a.sort(t),o.sort(t),u.sort(t),l.sort(t),e=0;e<7;e++)o[e]=ft(o[e]),u[e]=ft(u[e]),l[e]=ft(l[e]);this._weekdaysRegex=new RegExp("^("+l.join("|")+")","i"),this._weekdaysShortRegex=this._weekdaysRegex,this._weekdaysMinRegex=this._weekdaysRegex,this._weekdaysStrictRegex=new RegExp("^("+u.join("|")+")","i"),this._weekdaysShortStrictRegex=new RegExp("^("+o.join("|")+")","i"),this._weekdaysMinStrictRegex=new RegExp("^("+a.join("|")+")","i")}function ne(){return this.hours()%12||12}function ie(t,e){z(t,0,0,function(){return this.localeData().meridiem(this.hours(),this.minutes(),e)})}function se(t,e){return e._meridiemParse}z("H",["HH",2],0,"hour"),z("h",["hh",2],0,ne),z("k",["kk",2],0,function(){return this.hours()||24}),z("hmm",0,0,function(){return""+ne.apply(this)+G(this.minutes(),2)}),z("hmmss",0,0,function(){return""+ne.apply(this)+G(this.minutes(),2)+G(this.seconds(),2)}),z("Hmm",0,0,function(){return""+this.hours()+G(this.minutes(),2)}),z("Hmmss",0,0,function(){return""+this.hours()+G(this.minutes(),2)+G(this.seconds(),2)}),ie("a",!0),ie("A",!1),F("hour","h"),N("hour",13),ht("a",se),ht("A",se),ht("H",K),ht("h",K),ht("k",K),ht("HH",K,J),ht("hh",K,J),ht("kk",K,J),ht("hmm",tt),ht("hmmss",et),ht("Hmm",tt),ht("Hmmss",et),_t(["H","HH"],St),_t(["k","kk"],function(t,e,n){var i=D(t);e[St]=24===i?0:i}),_t(["a","A"],function(t,e,n){n._isPm=n._locale.isPM(t),n._meridiem=t}),_t(["h","hh"],function(t,e,n){e[St]=D(t),_(n).bigHour=!0}),_t("hmm",function(t,e,n){var i=t.length-2;e[St]=D(t.substr(0,i)),e[Mt]=D(t.substr(i)),_(n).bigHour=!0}),_t("hmmss",function(t,e,n){var i=t.length-4,s=t.length-2;e[St]=D(t.substr(0,i)),e[Mt]=D(t.substr(i,2)),e[kt]=D(t.substr(s)),_(n).bigHour=!0}),_t("Hmm",function(t,e,n){var i=t.length-2;e[St]=D(t.substr(0,i)),e[Mt]=D(t.substr(i))}),_t("Hmmss",function(t,e,n){var i=t.length-4,s=t.length-2;e[St]=D(t.substr(0,i)),e[Mt]=D(t.substr(i,2)),e[kt]=D(t.substr(s))});var re,ae=Wt("Hours",!0),oe={calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},longDateFormat:{LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},invalidDate:"Invalid date",ordinal:"%d",dayOfMonthOrdinalParse:/\d{1,2}/,relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},months:Ut,monthsShort:Lt,week:{dow:0,doy:6},weekdays:Jt,weekdaysMin:Qt,weekdaysShort:Bt,meridiemParse:/[ap]\.?m?\.?/i},ue={},le={};function de(t){return t?t.toLowerCase().replace("_","-"):t}function he(e){var i=null;if(!ue[e]&&t&&t.exports)try{i=re._abbr,n("./locale/"+e),ce(i)}catch(t){}return ue[e]}function ce(t,e){var n;return t&&((n=u(e)?me(t):fe(t,e))?re=n:"undefined"!=typeof console&&console.warn&&console.warn("Locale "+t+" not found. Did you forget to load it?")),re._abbr}function fe(t,e){if(null!==e){var n,i=oe;if(e.abbr=t,null!=ue[t])P("defineLocaleOverride","use moment.updateLocale(localeName, config) to change an existing locale. moment.defineLocale(localeName, config) should only be used for creating a new locale See http://momentjs.com/guides/#/warnings/define-locale/ for more info."),i=ue[t]._config;else if(null!=e.parentLocale)if(null!=ue[e.parentLocale])i=ue[e.parentLocale]._config;else{if(null==(n=he(e.parentLocale)))return le[e.parentLocale]||(le[e.parentLocale]=[]),le[e.parentLocale].push({name:t,config:e}),null;i=n._config}return ue[t]=new H(C(i,e)),le[t]&&le[t].forEach(function(t){fe(t.name,t.config)}),ce(t),ue[t]}return delete ue[t],null}function me(t){var e;if(t&&t._locale&&t._locale._abbr&&(t=t._locale._abbr),!t)return re;if(!r(t)){if(e=he(t))return e;t=[t]}return function(t){for(var e,n,i,s,r=0;r<t.length;){for(e=(s=de(t[r]).split("-")).length,n=(n=de(t[r+1]))?n.split("-"):null;e>0;){if(i=he(s.slice(0,e).join("-")))return i;if(n&&n.length>=e&&Y(s,n,!0)>=e-1)break;e--}r++}return re}(t)}function _e(t){var e,n=t._a;return n&&-2===_(t).overflow&&(e=n[vt]<0||n[vt]>11?vt:n[wt]<1||n[wt]>Rt(n[gt],n[vt])?wt:n[St]<0||n[St]>24||24===n[St]&&(0!==n[Mt]||0!==n[kt]||0!==n[Dt])?St:n[Mt]<0||n[Mt]>59?Mt:n[kt]<0||n[kt]>59?kt:n[Dt]<0||n[Dt]>999?Dt:-1,_(t)._overflowDayOfYear&&(e<gt||e>wt)&&(e=wt),_(t)._overflowWeeks&&-1===e&&(e=Yt),_(t)._overflowWeekday&&-1===e&&(e=Ot),_(t).overflow=e),t}function ye(t,e,n){return null!=t?t:null!=e?e:n}function pe(t){var e,n,i,r,a,o=[];if(!t._d){for(i=function(t){var e=new Date(s.now());return t._useUTC?[e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate()]:[e.getFullYear(),e.getMonth(),e.getDate()]}(t),t._w&&null==t._a[wt]&&null==t._a[vt]&&function(t){var e,n,i,s,r,a,o,u;if(null!=(e=t._w).GG||null!=e.W||null!=e.E)r=1,a=4,n=ye(e.GG,t._a[gt],Zt(Ce(),1,4).year),i=ye(e.W,1),((s=ye(e.E,1))<1||s>7)&&(u=!0);else{r=t._locale._week.dow,a=t._locale._week.doy;var l=Zt(Ce(),r,a);n=ye(e.gg,t._a[gt],l.year),i=ye(e.w,l.week),null!=e.d?((s=e.d)<0||s>6)&&(u=!0):null!=e.e?(s=e.e+r,(e.e<0||e.e>6)&&(u=!0)):s=r}i<1||i>$t(n,r,a)?_(t)._overflowWeeks=!0:null!=u?_(t)._overflowWeekday=!0:(o=zt(n,i,s,r,a),t._a[gt]=o.year,t._dayOfYear=o.dayOfYear)}(t),null!=t._dayOfYear&&(a=ye(t._a[gt],i[gt]),(t._dayOfYear>bt(a)||0===t._dayOfYear)&&(_(t)._overflowDayOfYear=!0),n=jt(a,0,t._dayOfYear),t._a[vt]=n.getUTCMonth(),t._a[wt]=n.getUTCDate()),e=0;e<3&&null==t._a[e];++e)t._a[e]=o[e]=i[e];for(;e<7;e++)t._a[e]=o[e]=null==t._a[e]?2===e?1:0:t._a[e];24===t._a[St]&&0===t._a[Mt]&&0===t._a[kt]&&0===t._a[Dt]&&(t._nextDay=!0,t._a[St]=0),t._d=(t._useUTC?jt:function(t,e,n,i,s,r,a){var o;return t<100&&t>=0?(o=new Date(t+400,e,n,i,s,r,a),isFinite(o.getFullYear())&&o.setFullYear(t)):o=new Date(t,e,n,i,s,r,a),o}).apply(null,o),r=t._useUTC?t._d.getUTCDay():t._d.getDay(),null!=t._tzm&&t._d.setUTCMinutes(t._d.getUTCMinutes()-t._tzm),t._nextDay&&(t._a[St]=24),t._w&&void 0!==t._w.d&&t._w.d!==r&&(_(t).weekdayMismatch=!0)}}var ge=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?$/,ve=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?$/,we=/Z|[+-]\d\d(?::?\d\d)?/,Se=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/]],Me=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]],ke=/^\/?Date\((\-?\d+)/i;function De(t){var e,n,i,s,r,a,o=t._i,u=ge.exec(o)||ve.exec(o);if(u){for(_(t).iso=!0,e=0,n=Se.length;e<n;e++)if(Se[e][1].exec(u[1])){s=Se[e][0],i=!1!==Se[e][2];break}if(null==s)return void(t._isValid=!1);if(u[3]){for(e=0,n=Me.length;e<n;e++)if(Me[e][1].exec(u[3])){r=(u[2]||" ")+Me[e][0];break}if(null==r)return void(t._isValid=!1)}if(!i&&null!=r)return void(t._isValid=!1);if(u[4]){if(!we.exec(u[4]))return void(t._isValid=!1);a="Z"}t._f=s+(r||"")+(a||""),xe(t)}else t._isValid=!1}var Ye=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\d{4}))$/;function Oe(t,e,n,i,s,r){var a=[function(t){var e=parseInt(t,10);if(e<=49)return 2e3+e;if(e<=999)return 1900+e;return e}(t),Lt.indexOf(e),parseInt(n,10),parseInt(i,10),parseInt(s,10)];return r&&a.push(parseInt(r,10)),a}var be={UT:0,GMT:0,EDT:-240,EST:-300,CDT:-300,CST:-360,MDT:-360,MST:-420,PDT:-420,PST:-480};function Te(t){var e=Ye.exec(t._i.replace(/\([^)]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").replace(/^\s\s*/,"").replace(/\s\s*$/,""));if(e){var n=Oe(e[4],e[3],e[2],e[5],e[6],e[7]);if(!function(t,e,n){return!t||Bt.indexOf(t)===new Date(e[0],e[1],e[2]).getDay()||(_(n).weekdayMismatch=!0,n._isValid=!1,!1)}(e[1],n,t))return;t._a=n,t._tzm=function(t,e,n){if(t)return be[t];if(e)return 0;var i=parseInt(n,10),s=i%100;return(i-s)/100*60+s}(e[8],e[9],e[10]),t._d=jt.apply(null,t._a),t._d.setUTCMinutes(t._d.getUTCMinutes()-t._tzm),_(t).rfc2822=!0}else t._isValid=!1}function xe(t){if(t._f!==s.ISO_8601)if(t._f!==s.RFC_2822){t._a=[],_(t).empty=!0;var e,n,i,r,a,o=""+t._i,u=o.length,l=0;for(i=$(t._f,t._locale).match(A)||[],e=0;e<i.length;e++)r=i[e],(n=(o.match(ct(r,t))||[])[0])&&((a=o.substr(0,o.indexOf(n))).length>0&&_(t).unusedInput.push(a),o=o.slice(o.indexOf(n)+n.length),l+=n.length),I[r]?(n?_(t).empty=!1:_(t).unusedTokens.push(r),pt(r,n,t)):t._strict&&!n&&_(t).unusedTokens.push(r);_(t).charsLeftOver=u-l,o.length>0&&_(t).unusedInput.push(o),t._a[St]<=12&&!0===_(t).bigHour&&t._a[St]>0&&(_(t).bigHour=void 0),_(t).parsedDateParts=t._a.slice(0),_(t).meridiem=t._meridiem,t._a[St]=function(t,e,n){var i;if(null==n)return e;return null!=t.meridiemHour?t.meridiemHour(e,n):null!=t.isPM?((i=t.isPM(n))&&e<12&&(e+=12),i||12!==e||(e=0),e):e}(t._locale,t._a[St],t._meridiem),pe(t),_e(t)}else Te(t);else De(t)}function Pe(t){var e=t._i,n=t._f;return t._locale=t._locale||me(t._l),null===e||void 0===n&&""===e?p({nullInput:!0}):("string"==typeof e&&(t._i=e=t._locale.preparse(e)),M(e)?new S(_e(e)):(d(e)?t._d=e:r(n)?function(t){var e,n,i,s,r;if(0===t._f.length)return _(t).invalidFormat=!0,void(t._d=new Date(NaN));for(s=0;s<t._f.length;s++)r=0,e=v({},t),null!=t._useUTC&&(e._useUTC=t._useUTC),e._f=t._f[s],xe(e),y(e)&&(r+=_(e).charsLeftOver,r+=10*_(e).unusedTokens.length,_(e).score=r,(null==i||r<i)&&(i=r,n=e));f(t,n||e)}(t):n?xe(t):function(t){var e=t._i;u(e)?t._d=new Date(s.now()):d(e)?t._d=new Date(e.valueOf()):"string"==typeof e?function(t){var e=ke.exec(t._i);null===e?(De(t),!1===t._isValid&&(delete t._isValid,Te(t),!1===t._isValid&&(delete t._isValid,s.createFromInputFallback(t)))):t._d=new Date(+e[1])}(t):r(e)?(t._a=h(e.slice(0),function(t){return parseInt(t,10)}),pe(t)):o(e)?function(t){if(!t._d){var e=L(t._i);t._a=h([e.year,e.month,e.day||e.date,e.hour,e.minute,e.second,e.millisecond],function(t){return t&&parseInt(t,10)}),pe(t)}}(t):l(e)?t._d=new Date(e):s.createFromInputFallback(t)}(t),y(t)||(t._d=null),t))}function We(t,e,n,i,s){var a,u={};return!0!==n&&!1!==n||(i=n,n=void 0),(o(t)&&function(t){if(Object.getOwnPropertyNames)return 0===Object.getOwnPropertyNames(t).length;var e;for(e in t)if(t.hasOwnProperty(e))return!1;return!0}(t)||r(t)&&0===t.length)&&(t=void 0),u._isAMomentObject=!0,u._useUTC=u._isUTC=s,u._l=n,u._i=t,u._f=e,u._strict=i,(a=new S(_e(Pe(u))))._nextDay&&(a.add(1,"d"),a._nextDay=void 0),a}function Ce(t,e,n,i){return We(t,e,n,i,!1)}s.createFromInputFallback=b("value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are discouraged and will be removed in an upcoming major release. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.",function(t){t._d=new Date(t._i+(t._useUTC?" UTC":""))}),s.ISO_8601=function(){},s.RFC_2822=function(){};var He=b("moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var t=Ce.apply(null,arguments);return this.isValid()&&t.isValid()?t<this?this:t:p()}),Re=b("moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var t=Ce.apply(null,arguments);return this.isValid()&&t.isValid()?t>this?this:t:p()});function Fe(t,e){var n,i;if(1===e.length&&r(e[0])&&(e=e[0]),!e.length)return Ce();for(n=e[0],i=1;i<e.length;++i)e[i].isValid()&&!e[i][t](n)||(n=e[i]);return n}var Ue=["year","quarter","month","week","day","hour","minute","second","millisecond"];function Le(t){var e=L(t),n=e.year||0,i=e.quarter||0,s=e.month||0,r=e.week||e.isoWeek||0,a=e.day||0,o=e.hour||0,u=e.minute||0,l=e.second||0,d=e.millisecond||0;this._isValid=function(t){for(var e in t)if(-1===xt.call(Ue,e)||null!=t[e]&&isNaN(t[e]))return!1;for(var n=!1,i=0;i<Ue.length;++i)if(t[Ue[i]]){if(n)return!1;parseFloat(t[Ue[i]])!==D(t[Ue[i]])&&(n=!0)}return!0}(e),this._milliseconds=+d+1e3*l+6e4*u+1e3*o*60*60,this._days=+a+7*r,this._months=+s+3*i+12*n,this._data={},this._locale=me(),this._bubble()}function Ve(t){return t instanceof Le}function Ne(t){return t<0?-1*Math.round(-1*t):Math.round(t)}function Ge(t,e){z(t,0,0,function(){var t=this.utcOffset(),n="+";return t<0&&(t=-t,n="-"),n+G(~~(t/60),2)+e+G(~~t%60,2)})}Ge("Z",":"),Ge("ZZ",""),ht("Z",ut),ht("ZZ",ut),_t(["Z","ZZ"],function(t,e,n){n._useUTC=!0,n._tzm=Ee(ut,t)});var Ae=/([\+\-]|\d\d)/gi;function Ee(t,e){var n=(e||"").match(t);if(null===n)return null;var i=((n[n.length-1]||[])+"").match(Ae)||["-",0,0],s=60*i[1]+D(i[2]);return 0===s?0:"+"===i[0]?s:-s}function je(t,e){var n,i;return e._isUTC?(n=e.clone(),i=(M(t)||d(t)?t.valueOf():Ce(t).valueOf())-n.valueOf(),n._d.setTime(n._d.valueOf()+i),s.updateOffset(n,!1),n):Ce(t).local()}function Ie(t){return 15*-Math.round(t._d.getTimezoneOffset()/15)}function ze(){return!!this.isValid()&&(this._isUTC&&0===this._offset)}s.updateOffset=function(){};var Ze=/^(\-|\+)?(?:(\d*)[. ])?(\d+)\:(\d+)(?:\:(\d+)(\.\d*)?)?$/,$e=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;function qe(t,e){var n,i,s,r=t,o=null;return Ve(t)?r={ms:t._milliseconds,d:t._days,M:t._months}:l(t)?(r={},e?r[e]=t:r.milliseconds=t):(o=Ze.exec(t))?(n="-"===o[1]?-1:1,r={y:0,d:D(o[wt])*n,h:D(o[St])*n,m:D(o[Mt])*n,s:D(o[kt])*n,ms:D(Ne(1e3*o[Dt]))*n}):(o=$e.exec(t))?(n="-"===o[1]?-1:1,r={y:Je(o[2],n),M:Je(o[3],n),w:Je(o[4],n),d:Je(o[5],n),h:Je(o[6],n),m:Je(o[7],n),s:Je(o[8],n)}):null==r?r={}:"object"===(void 0===r?"undefined":a(r))&&("from"in r||"to"in r)&&(s=function(t,e){var n;if(!t.isValid()||!e.isValid())return{milliseconds:0,months:0};e=je(e,t),t.isBefore(e)?n=Be(t,e):((n=Be(e,t)).milliseconds=-n.milliseconds,n.months=-n.months);return n}(Ce(r.from),Ce(r.to)),(r={}).ms=s.milliseconds,r.M=s.months),i=new Le(r),Ve(t)&&c(t,"_locale")&&(i._locale=t._locale),i}function Je(t,e){var n=t&&parseFloat(t.replace(",","."));return(isNaN(n)?0:n)*e}function Be(t,e){var n={};return n.months=e.month()-t.month()+12*(e.year()-t.year()),t.clone().add(n.months,"M").isAfter(e)&&--n.months,n.milliseconds=+e-+t.clone().add(n.months,"M"),n}function Qe(t,e){return function(n,i){var s;return null===i||isNaN(+i)||(P(e,"moment()."+e+"(period, number) is deprecated. Please use moment()."+e+"(number, period). See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info."),s=n,n=i,i=s),Xe(this,qe(n="string"==typeof n?+n:n,i),t),this}}function Xe(t,e,n,i){var r=e._milliseconds,a=Ne(e._days),o=Ne(e._months);t.isValid()&&(i=null==i||i,o&&Vt(t,Ct(t,"Month")+o*n),a&&Ht(t,"Date",Ct(t,"Date")+a*n),r&&t._d.setTime(t._d.valueOf()+r*n),i&&s.updateOffset(t,a||o))}qe.fn=Le.prototype,qe.invalid=function(){return qe(NaN)};var Ke=Qe(1,"add"),tn=Qe(-1,"subtract");function en(t,e){var n=12*(e.year()-t.year())+(e.month()-t.month()),i=t.clone().add(n,"months");return-(n+(e-i<0?(e-i)/(i-t.clone().add(n-1,"months")):(e-i)/(t.clone().add(n+1,"months")-i)))||0}function nn(t){var e;return void 0===t?this._locale._abbr:(null!=(e=me(t))&&(this._locale=e),this)}s.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",s.defaultFormatUtc="YYYY-MM-DDTHH:mm:ss[Z]";var sn=b("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",function(t){return void 0===t?this.localeData():this.locale(t)});function rn(){return this._locale}var an=1e3,on=60*an,un=60*on,ln=3506328*un;function dn(t,e){return(t%e+e)%e}function hn(t,e,n){return t<100&&t>=0?new Date(t+400,e,n)-ln:new Date(t,e,n).valueOf()}function cn(t,e,n){return t<100&&t>=0?Date.UTC(t+400,e,n)-ln:Date.UTC(t,e,n)}function fn(t,e){z(0,[t,t.length],0,e)}function mn(t,e,n,i,s){var r;return null==t?Zt(this,i,s).year:(e>(r=$t(t,i,s))&&(e=r),function(t,e,n,i,s){var r=zt(t,e,n,i,s),a=jt(r.year,0,r.dayOfYear);return this.year(a.getUTCFullYear()),this.month(a.getUTCMonth()),this.date(a.getUTCDate()),this}.call(this,t,e,n,i,s))}z(0,["gg",2],0,function(){return this.weekYear()%100}),z(0,["GG",2],0,function(){return this.isoWeekYear()%100}),fn("gggg","weekYear"),fn("ggggg","weekYear"),fn("GGGG","isoWeekYear"),fn("GGGGG","isoWeekYear"),F("weekYear","gg"),F("isoWeekYear","GG"),N("weekYear",1),N("isoWeekYear",1),ht("G",at),ht("g",at),ht("GG",K,J),ht("gg",K,J),ht("GGGG",it,Q),ht("gggg",it,Q),ht("GGGGG",st,X),ht("ggggg",st,X),yt(["gggg","ggggg","GGGG","GGGGG"],function(t,e,n,i){e[i.substr(0,2)]=D(t)}),yt(["gg","GG"],function(t,e,n,i){e[i]=s.parseTwoDigitYear(t)}),z("Q",0,"Qo","quarter"),F("quarter","Q"),N("quarter",7),ht("Q",q),_t("Q",function(t,e){e[vt]=3*(D(t)-1)}),z("D",["DD",2],"Do","date"),F("date","D"),N("date",9),ht("D",K),ht("DD",K,J),ht("Do",function(t,e){return t?e._dayOfMonthOrdinalParse||e._ordinalParse:e._dayOfMonthOrdinalParseLenient}),_t(["D","DD"],wt),_t("Do",function(t,e){e[wt]=D(t.match(K)[0])});var _n=Wt("Date",!0);z("DDD",["DDDD",3],"DDDo","dayOfYear"),F("dayOfYear","DDD"),N("dayOfYear",4),ht("DDD",nt),ht("DDDD",B),_t(["DDD","DDDD"],function(t,e,n){n._dayOfYear=D(t)}),z("m",["mm",2],0,"minute"),F("minute","m"),N("minute",14),ht("m",K),ht("mm",K,J),_t(["m","mm"],Mt);var yn=Wt("Minutes",!1);z("s",["ss",2],0,"second"),F("second","s"),N("second",15),ht("s",K),ht("ss",K,J),_t(["s","ss"],kt);var pn,gn=Wt("Seconds",!1);for(z("S",0,0,function(){return~~(this.millisecond()/100)}),z(0,["SS",2],0,function(){return~~(this.millisecond()/10)}),z(0,["SSS",3],0,"millisecond"),z(0,["SSSS",4],0,function(){return 10*this.millisecond()}),z(0,["SSSSS",5],0,function(){return 100*this.millisecond()}),z(0,["SSSSSS",6],0,function(){return 1e3*this.millisecond()}),z(0,["SSSSSSS",7],0,function(){return 1e4*this.millisecond()}),z(0,["SSSSSSSS",8],0,function(){return 1e5*this.millisecond()}),z(0,["SSSSSSSSS",9],0,function(){return 1e6*this.millisecond()}),F("millisecond","ms"),N("millisecond",16),ht("S",nt,q),ht("SS",nt,J),ht("SSS",nt,B),pn="SSSS";pn.length<=9;pn+="S")ht(pn,rt);function vn(t,e){e[Dt]=D(1e3*("0."+t))}for(pn="S";pn.length<=9;pn+="S")_t(pn,vn);var wn=Wt("Milliseconds",!1);z("z",0,0,"zoneAbbr"),z("zz",0,0,"zoneName");var Sn=S.prototype;function Mn(t){return t}Sn.add=Ke,Sn.calendar=function(t,e){var n=t||Ce(),i=je(n,this).startOf("day"),r=s.calendarFormat(this,i)||"sameElse",a=e&&(W(e[r])?e[r].call(this,n):e[r]);return this.format(a||this.localeData().calendar(r,this,Ce(n)))},Sn.clone=function(){return new S(this)},Sn.diff=function(t,e,n){var i,s,r;if(!this.isValid())return NaN;if(!(i=je(t,this)).isValid())return NaN;switch(s=6e4*(i.utcOffset()-this.utcOffset()),e=U(e)){case"year":r=en(this,i)/12;break;case"month":r=en(this,i);break;case"quarter":r=en(this,i)/3;break;case"second":r=(this-i)/1e3;break;case"minute":r=(this-i)/6e4;break;case"hour":r=(this-i)/36e5;break;case"day":r=(this-i-s)/864e5;break;case"week":r=(this-i-s)/6048e5;break;default:r=this-i}return n?r:k(r)},Sn.endOf=function(t){var e;if(void 0===(t=U(t))||"millisecond"===t||!this.isValid())return this;var n=this._isUTC?cn:hn;switch(t){case"year":e=n(this.year()+1,0,1)-1;break;case"quarter":e=n(this.year(),this.month()-this.month()%3+3,1)-1;break;case"month":e=n(this.year(),this.month()+1,1)-1;break;case"week":e=n(this.year(),this.month(),this.date()-this.weekday()+7)-1;break;case"isoWeek":e=n(this.year(),this.month(),this.date()-(this.isoWeekday()-1)+7)-1;break;case"day":case"date":e=n(this.year(),this.month(),this.date()+1)-1;break;case"hour":e=this._d.valueOf(),e+=un-dn(e+(this._isUTC?0:this.utcOffset()*on),un)-1;break;case"minute":e=this._d.valueOf(),e+=on-dn(e,on)-1;break;case"second":e=this._d.valueOf(),e+=an-dn(e,an)-1}return this._d.setTime(e),s.updateOffset(this,!0),this},Sn.format=function(t){t||(t=this.isUtc()?s.defaultFormatUtc:s.defaultFormat);var e=Z(this,t);return this.localeData().postformat(e)},Sn.from=function(t,e){return this.isValid()&&(M(t)&&t.isValid()||Ce(t).isValid())?qe({to:this,from:t}).locale(this.locale()).humanize(!e):this.localeData().invalidDate()},Sn.fromNow=function(t){return this.from(Ce(),t)},Sn.to=function(t,e){return this.isValid()&&(M(t)&&t.isValid()||Ce(t).isValid())?qe({from:this,to:t}).locale(this.locale()).humanize(!e):this.localeData().invalidDate()},Sn.toNow=function(t){return this.to(Ce(),t)},Sn.get=function(t){return W(this[t=U(t)])?this[t]():this},Sn.invalidAt=function(){return _(this).overflow},Sn.isAfter=function(t,e){var n=M(t)?t:Ce(t);return!(!this.isValid()||!n.isValid())&&("millisecond"===(e=U(e)||"millisecond")?this.valueOf()>n.valueOf():n.valueOf()<this.clone().startOf(e).valueOf())},Sn.isBefore=function(t,e){var n=M(t)?t:Ce(t);return!(!this.isValid()||!n.isValid())&&("millisecond"===(e=U(e)||"millisecond")?this.valueOf()<n.valueOf():this.clone().endOf(e).valueOf()<n.valueOf())},Sn.isBetween=function(t,e,n,i){var s=M(t)?t:Ce(t),r=M(e)?e:Ce(e);return!!(this.isValid()&&s.isValid()&&r.isValid())&&("("===(i=i||"()")[0]?this.isAfter(s,n):!this.isBefore(s,n))&&(")"===i[1]?this.isBefore(r,n):!this.isAfter(r,n))},Sn.isSame=function(t,e){var n,i=M(t)?t:Ce(t);return!(!this.isValid()||!i.isValid())&&("millisecond"===(e=U(e)||"millisecond")?this.valueOf()===i.valueOf():(n=i.valueOf(),this.clone().startOf(e).valueOf()<=n&&n<=this.clone().endOf(e).valueOf()))},Sn.isSameOrAfter=function(t,e){return this.isSame(t,e)||this.isAfter(t,e)},Sn.isSameOrBefore=function(t,e){return this.isSame(t,e)||this.isBefore(t,e)},Sn.isValid=function(){return y(this)},Sn.lang=sn,Sn.locale=nn,Sn.localeData=rn,Sn.max=Re,Sn.min=He,Sn.parsingFlags=function(){return f({},_(this))},Sn.set=function(t,e){if("object"===(void 0===t?"undefined":a(t)))for(var n=function(t){var e=[];for(var n in t)e.push({unit:n,priority:V[n]});return e.sort(function(t,e){return t.priority-e.priority}),e}(t=L(t)),i=0;i<n.length;i++)this[n[i].unit](t[n[i].unit]);else if(W(this[t=U(t)]))return this[t](e);return this},Sn.startOf=function(t){var e;if(void 0===(t=U(t))||"millisecond"===t||!this.isValid())return this;var n=this._isUTC?cn:hn;switch(t){case"year":e=n(this.year(),0,1);break;case"quarter":e=n(this.year(),this.month()-this.month()%3,1);break;case"month":e=n(this.year(),this.month(),1);break;case"week":e=n(this.year(),this.month(),this.date()-this.weekday());break;case"isoWeek":e=n(this.year(),this.month(),this.date()-(this.isoWeekday()-1));break;case"day":case"date":e=n(this.year(),this.month(),this.date());break;case"hour":e=this._d.valueOf(),e-=dn(e+(this._isUTC?0:this.utcOffset()*on),un);break;case"minute":e=this._d.valueOf(),e-=dn(e,on);break;case"second":e=this._d.valueOf(),e-=dn(e,an)}return this._d.setTime(e),s.updateOffset(this,!0),this},Sn.subtract=tn,Sn.toArray=function(){var t=this;return[t.year(),t.month(),t.date(),t.hour(),t.minute(),t.second(),t.millisecond()]},Sn.toObject=function(){var t=this;return{years:t.year(),months:t.month(),date:t.date(),hours:t.hours(),minutes:t.minutes(),seconds:t.seconds(),milliseconds:t.milliseconds()}},Sn.toDate=function(){return new Date(this.valueOf())},Sn.toISOString=function(t){if(!this.isValid())return null;var e=!0!==t,n=e?this.clone().utc():this;return n.year()<0||n.year()>9999?Z(n,e?"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYYYY-MM-DD[T]HH:mm:ss.SSSZ"):W(Date.prototype.toISOString)?e?this.toDate().toISOString():new Date(this.valueOf()+60*this.utcOffset()*1e3).toISOString().replace("Z",Z(n,"Z")):Z(n,e?"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYY-MM-DD[T]HH:mm:ss.SSSZ")},Sn.inspect=function(){if(!this.isValid())return"moment.invalid(/* "+this._i+" */)";var t="moment",e="";this.isLocal()||(t=0===this.utcOffset()?"moment.utc":"moment.parseZone",e="Z");var n="["+t+'("]',i=0<=this.year()&&this.year()<=9999?"YYYY":"YYYYYY",s=e+'[")]';return this.format(n+i+"-MM-DD[T]HH:mm:ss.SSS"+s)},Sn.toJSON=function(){return this.isValid()?this.toISOString():null},Sn.toString=function(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")},Sn.unix=function(){return Math.floor(this.valueOf()/1e3)},Sn.valueOf=function(){return this._d.valueOf()-6e4*(this._offset||0)},Sn.creationData=function(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}},Sn.year=Pt,Sn.isLeapYear=function(){return Tt(this.year())},Sn.weekYear=function(t){return mn.call(this,t,this.week(),this.weekday(),this.localeData()._week.dow,this.localeData()._week.doy)},Sn.isoWeekYear=function(t){return mn.call(this,t,this.isoWeek(),this.isoWeekday(),1,4)},Sn.quarter=Sn.quarters=function(t){return null==t?Math.ceil((this.month()+1)/3):this.month(3*(t-1)+this.month()%3)},Sn.month=Nt,Sn.daysInMonth=function(){return Rt(this.year(),this.month())},Sn.week=Sn.weeks=function(t){var e=this.localeData().week(this);return null==t?e:this.add(7*(t-e),"d")},Sn.isoWeek=Sn.isoWeeks=function(t){var e=Zt(this,1,4).week;return null==t?e:this.add(7*(t-e),"d")},Sn.weeksInYear=function(){var t=this.localeData()._week;return $t(this.year(),t.dow,t.doy)},Sn.isoWeeksInYear=function(){return $t(this.year(),1,4)},Sn.date=_n,Sn.day=Sn.days=function(t){if(!this.isValid())return null!=t?this:NaN;var e=this._isUTC?this._d.getUTCDay():this._d.getDay();return null!=t?(t=function(t,e){return"string"!=typeof t?t:isNaN(t)?"number"==typeof(t=e.weekdaysParse(t))?t:null:parseInt(t,10)}(t,this.localeData()),this.add(t-e,"d")):e},Sn.weekday=function(t){if(!this.isValid())return null!=t?this:NaN;var e=(this.day()+7-this.localeData()._week.dow)%7;return null==t?e:this.add(t-e,"d")},Sn.isoWeekday=function(t){if(!this.isValid())return null!=t?this:NaN;if(null!=t){var e=function(t,e){return"string"==typeof t?e.weekdaysParse(t)%7||7:isNaN(t)?null:t}(t,this.localeData());return this.day(this.day()%7?e:e-7)}return this.day()||7},Sn.dayOfYear=function(t){var e=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return null==t?e:this.add(t-e,"d")},Sn.hour=Sn.hours=ae,Sn.minute=Sn.minutes=yn,Sn.second=Sn.seconds=gn,Sn.millisecond=Sn.milliseconds=wn,Sn.utcOffset=function(t,e,n){var i,r=this._offset||0;if(!this.isValid())return null!=t?this:NaN;if(null!=t){if("string"==typeof t){if(null===(t=Ee(ut,t)))return this}else Math.abs(t)<16&&!n&&(t*=60);return!this._isUTC&&e&&(i=Ie(this)),this._offset=t,this._isUTC=!0,null!=i&&this.add(i,"m"),r!==t&&(!e||this._changeInProgress?Xe(this,qe(t-r,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,s.updateOffset(this,!0),this._changeInProgress=null)),this}return this._isUTC?r:Ie(this)},Sn.utc=function(t){return this.utcOffset(0,t)},Sn.local=function(t){return this._isUTC&&(this.utcOffset(0,t),this._isUTC=!1,t&&this.subtract(Ie(this),"m")),this},Sn.parseZone=function(){if(null!=this._tzm)this.utcOffset(this._tzm,!1,!0);else if("string"==typeof this._i){var t=Ee(ot,this._i);null!=t?this.utcOffset(t):this.utcOffset(0,!0)}return this},Sn.hasAlignedHourOffset=function(t){return!!this.isValid()&&(t=t?Ce(t).utcOffset():0,(this.utcOffset()-t)%60==0)},Sn.isDST=function(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()},Sn.isLocal=function(){return!!this.isValid()&&!this._isUTC},Sn.isUtcOffset=function(){return!!this.isValid()&&this._isUTC},Sn.isUtc=ze,Sn.isUTC=ze,Sn.zoneAbbr=function(){return this._isUTC?"UTC":""},Sn.zoneName=function(){return this._isUTC?"Coordinated Universal Time":""},Sn.dates=b("dates accessor is deprecated. Use date instead.",_n),Sn.months=b("months accessor is deprecated. Use month instead",Nt),Sn.years=b("years accessor is deprecated. Use year instead",Pt),Sn.zone=b("moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",function(t,e){return null!=t?("string"!=typeof t&&(t=-t),this.utcOffset(t,e),this):-this.utcOffset()}),Sn.isDSTShifted=b("isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",function(){if(!u(this._isDSTShifted))return this._isDSTShifted;var t={};if(v(t,this),(t=Pe(t))._a){var e=t._isUTC?m(t._a):Ce(t._a);this._isDSTShifted=this.isValid()&&Y(t._a,e.toArray())>0}else this._isDSTShifted=!1;return this._isDSTShifted});var kn=H.prototype;function Dn(t,e,n,i){var s=me(),r=m().set(i,e);return s[n](r,t)}function Yn(t,e,n){if(l(t)&&(e=t,t=void 0),t=t||"",null!=e)return Dn(t,e,n,"month");var i,s=[];for(i=0;i<12;i++)s[i]=Dn(t,i,n,"month");return s}function On(t,e,n,i){"boolean"==typeof t?(l(e)&&(n=e,e=void 0),e=e||""):(n=e=t,t=!1,l(e)&&(n=e,e=void 0),e=e||"");var s,r=me(),a=t?r._week.dow:0;if(null!=n)return Dn(e,(n+a)%7,i,"day");var o=[];for(s=0;s<7;s++)o[s]=Dn(e,(s+a)%7,i,"day");return o}kn.calendar=function(t,e,n){var i=this._calendar[t]||this._calendar.sameElse;return W(i)?i.call(e,n):i},kn.longDateFormat=function(t){var e=this._longDateFormat[t],n=this._longDateFormat[t.toUpperCase()];return e||!n?e:(this._longDateFormat[t]=n.replace(/MMMM|MM|DD|dddd/g,function(t){return t.slice(1)}),this._longDateFormat[t])},kn.invalidDate=function(){return this._invalidDate},kn.ordinal=function(t){return this._ordinal.replace("%d",t)},kn.preparse=Mn,kn.postformat=Mn,kn.relativeTime=function(t,e,n,i){var s=this._relativeTime[n];return W(s)?s(t,e,n,i):s.replace(/%d/i,t)},kn.pastFuture=function(t,e){var n=this._relativeTime[t>0?"future":"past"];return W(n)?n(e):n.replace(/%s/i,e)},kn.set=function(t){var e,n;for(n in t)W(e=t[n])?this[n]=e:this["_"+n]=e;this._config=t,this._dayOfMonthOrdinalParseLenient=new RegExp((this._dayOfMonthOrdinalParse.source||this._ordinalParse.source)+"|"+/\d{1,2}/.source)},kn.months=function(t,e){return t?r(this._months)?this._months[t.month()]:this._months[(this._months.isFormat||Ft).test(e)?"format":"standalone"][t.month()]:r(this._months)?this._months:this._months.standalone},kn.monthsShort=function(t,e){return t?r(this._monthsShort)?this._monthsShort[t.month()]:this._monthsShort[Ft.test(e)?"format":"standalone"][t.month()]:r(this._monthsShort)?this._monthsShort:this._monthsShort.standalone},kn.monthsParse=function(t,e,n){var i,s,r;if(this._monthsParseExact)return function(t,e,n){var i,s,r,a=t.toLocaleLowerCase();if(!this._monthsParse)for(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[],i=0;i<12;++i)r=m([2e3,i]),this._shortMonthsParse[i]=this.monthsShort(r,"").toLocaleLowerCase(),this._longMonthsParse[i]=this.months(r,"").toLocaleLowerCase();return n?"MMM"===e?-1!==(s=xt.call(this._shortMonthsParse,a))?s:null:-1!==(s=xt.call(this._longMonthsParse,a))?s:null:"MMM"===e?-1!==(s=xt.call(this._shortMonthsParse,a))?s:-1!==(s=xt.call(this._longMonthsParse,a))?s:null:-1!==(s=xt.call(this._longMonthsParse,a))?s:-1!==(s=xt.call(this._shortMonthsParse,a))?s:null}.call(this,t,e,n);for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),i=0;i<12;i++){if(s=m([2e3,i]),n&&!this._longMonthsParse[i]&&(this._longMonthsParse[i]=new RegExp("^"+this.months(s,"").replace(".","")+"$","i"),this._shortMonthsParse[i]=new RegExp("^"+this.monthsShort(s,"").replace(".","")+"$","i")),n||this._monthsParse[i]||(r="^"+this.months(s,"")+"|^"+this.monthsShort(s,""),this._monthsParse[i]=new RegExp(r.replace(".",""),"i")),n&&"MMMM"===e&&this._longMonthsParse[i].test(t))return i;if(n&&"MMM"===e&&this._shortMonthsParse[i].test(t))return i;if(!n&&this._monthsParse[i].test(t))return i}},kn.monthsRegex=function(t){return this._monthsParseExact?(c(this,"_monthsRegex")||Et.call(this),t?this._monthsStrictRegex:this._monthsRegex):(c(this,"_monthsRegex")||(this._monthsRegex=At),this._monthsStrictRegex&&t?this._monthsStrictRegex:this._monthsRegex)},kn.monthsShortRegex=function(t){return this._monthsParseExact?(c(this,"_monthsRegex")||Et.call(this),t?this._monthsShortStrictRegex:this._monthsShortRegex):(c(this,"_monthsShortRegex")||(this._monthsShortRegex=Gt),this._monthsShortStrictRegex&&t?this._monthsShortStrictRegex:this._monthsShortRegex)},kn.week=function(t){return Zt(t,this._week.dow,this._week.doy).week},kn.firstDayOfYear=function(){return this._week.doy},kn.firstDayOfWeek=function(){return this._week.dow},kn.weekdays=function(t,e){var n=r(this._weekdays)?this._weekdays:this._weekdays[t&&!0!==t&&this._weekdays.isFormat.test(e)?"format":"standalone"];return!0===t?qt(n,this._week.dow):t?n[t.day()]:n},kn.weekdaysMin=function(t){return!0===t?qt(this._weekdaysMin,this._week.dow):t?this._weekdaysMin[t.day()]:this._weekdaysMin},kn.weekdaysShort=function(t){return!0===t?qt(this._weekdaysShort,this._week.dow):t?this._weekdaysShort[t.day()]:this._weekdaysShort},kn.weekdaysParse=function(t,e,n){var i,s,r;if(this._weekdaysParseExact)return function(t,e,n){var i,s,r,a=t.toLocaleLowerCase();if(!this._weekdaysParse)for(this._weekdaysParse=[],this._shortWeekdaysParse=[],this._minWeekdaysParse=[],i=0;i<7;++i)r=m([2e3,1]).day(i),this._minWeekdaysParse[i]=this.weekdaysMin(r,"").toLocaleLowerCase(),this._shortWeekdaysParse[i]=this.weekdaysShort(r,"").toLocaleLowerCase(),this._weekdaysParse[i]=this.weekdays(r,"").toLocaleLowerCase();return n?"dddd"===e?-1!==(s=xt.call(this._weekdaysParse,a))?s:null:"ddd"===e?-1!==(s=xt.call(this._shortWeekdaysParse,a))?s:null:-1!==(s=xt.call(this._minWeekdaysParse,a))?s:null:"dddd"===e?-1!==(s=xt.call(this._weekdaysParse,a))?s:-1!==(s=xt.call(this._shortWeekdaysParse,a))?s:-1!==(s=xt.call(this._minWeekdaysParse,a))?s:null:"ddd"===e?-1!==(s=xt.call(this._shortWeekdaysParse,a))?s:-1!==(s=xt.call(this._weekdaysParse,a))?s:-1!==(s=xt.call(this._minWeekdaysParse,a))?s:null:-1!==(s=xt.call(this._minWeekdaysParse,a))?s:-1!==(s=xt.call(this._weekdaysParse,a))?s:-1!==(s=xt.call(this._shortWeekdaysParse,a))?s:null}.call(this,t,e,n);for(this._weekdaysParse||(this._weekdaysParse=[],this._minWeekdaysParse=[],this._shortWeekdaysParse=[],this._fullWeekdaysParse=[]),i=0;i<7;i++){if(s=m([2e3,1]).day(i),n&&!this._fullWeekdaysParse[i]&&(this._fullWeekdaysParse[i]=new RegExp("^"+this.weekdays(s,"").replace(".","\\.?")+"$","i"),this._shortWeekdaysParse[i]=new RegExp("^"+this.weekdaysShort(s,"").replace(".","\\.?")+"$","i"),this._minWeekdaysParse[i]=new RegExp("^"+this.weekdaysMin(s,"").replace(".","\\.?")+"$","i")),this._weekdaysParse[i]||(r="^"+this.weekdays(s,"")+"|^"+this.weekdaysShort(s,"")+"|^"+this.weekdaysMin(s,""),this._weekdaysParse[i]=new RegExp(r.replace(".",""),"i")),n&&"dddd"===e&&this._fullWeekdaysParse[i].test(t))return i;if(n&&"ddd"===e&&this._shortWeekdaysParse[i].test(t))return i;if(n&&"dd"===e&&this._minWeekdaysParse[i].test(t))return i;if(!n&&this._weekdaysParse[i].test(t))return i}},kn.weekdaysRegex=function(t){return this._weekdaysParseExact?(c(this,"_weekdaysRegex")||ee.call(this),t?this._weekdaysStrictRegex:this._weekdaysRegex):(c(this,"_weekdaysRegex")||(this._weekdaysRegex=Xt),this._weekdaysStrictRegex&&t?this._weekdaysStrictRegex:this._weekdaysRegex)},kn.weekdaysShortRegex=function(t){return this._weekdaysParseExact?(c(this,"_weekdaysRegex")||ee.call(this),t?this._weekdaysShortStrictRegex:this._weekdaysShortRegex):(c(this,"_weekdaysShortRegex")||(this._weekdaysShortRegex=Kt),this._weekdaysShortStrictRegex&&t?this._weekdaysShortStrictRegex:this._weekdaysShortRegex)},kn.weekdaysMinRegex=function(t){return this._weekdaysParseExact?(c(this,"_weekdaysRegex")||ee.call(this),t?this._weekdaysMinStrictRegex:this._weekdaysMinRegex):(c(this,"_weekdaysMinRegex")||(this._weekdaysMinRegex=te),this._weekdaysMinStrictRegex&&t?this._weekdaysMinStrictRegex:this._weekdaysMinRegex)},kn.isPM=function(t){return"p"===(t+"").toLowerCase().charAt(0)},kn.meridiem=function(t,e,n){return t>11?n?"pm":"PM":n?"am":"AM"},ce("en",{dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(t){var e=t%10;return t+(1===D(t%100/10)?"th":1===e?"st":2===e?"nd":3===e?"rd":"th")}}),s.lang=b("moment.lang is deprecated. Use moment.locale instead.",ce),s.langData=b("moment.langData is deprecated. Use moment.localeData instead.",me);var bn=Math.abs;function Tn(t,e,n,i){var s=qe(e,n);return t._milliseconds+=i*s._milliseconds,t._days+=i*s._days,t._months+=i*s._months,t._bubble()}function xn(t){return t<0?Math.floor(t):Math.ceil(t)}function Pn(t){return 4800*t/146097}function Wn(t){return 146097*t/4800}function Cn(t){return function(){return this.as(t)}}var Hn=Cn("ms"),Rn=Cn("s"),Fn=Cn("m"),Un=Cn("h"),Ln=Cn("d"),Vn=Cn("w"),Nn=Cn("M"),Gn=Cn("Q"),An=Cn("y");function En(t){return function(){return this.isValid()?this._data[t]:NaN}}var jn=En("milliseconds"),In=En("seconds"),zn=En("minutes"),Zn=En("hours"),$n=En("days"),qn=En("months"),Jn=En("years");var Bn=Math.round,Qn={ss:44,s:45,m:45,h:22,d:26,M:11};var Xn=Math.abs;function Kn(t){return(t>0)-(t<0)||+t}function ti(){if(!this.isValid())return this.localeData().invalidDate();var t,e,n=Xn(this._milliseconds)/1e3,i=Xn(this._days),s=Xn(this._months);e=k((t=k(n/60))/60),n%=60,t%=60;var r=k(s/12),a=s%=12,o=i,u=e,l=t,d=n?n.toFixed(3).replace(/\.?0+$/,""):"",h=this.asSeconds();if(!h)return"P0D";var c=h<0?"-":"",f=Kn(this._months)!==Kn(h)?"-":"",m=Kn(this._days)!==Kn(h)?"-":"",_=Kn(this._milliseconds)!==Kn(h)?"-":"";return c+"P"+(r?f+r+"Y":"")+(a?f+a+"M":"")+(o?m+o+"D":"")+(u||l||d?"T":"")+(u?_+u+"H":"")+(l?_+l+"M":"")+(d?_+d+"S":"")}var ei=Le.prototype;return ei.isValid=function(){return this._isValid},ei.abs=function(){var t=this._data;return this._milliseconds=bn(this._milliseconds),this._days=bn(this._days),this._months=bn(this._months),t.milliseconds=bn(t.milliseconds),t.seconds=bn(t.seconds),t.minutes=bn(t.minutes),t.hours=bn(t.hours),t.months=bn(t.months),t.years=bn(t.years),this},ei.add=function(t,e){return Tn(this,t,e,1)},ei.subtract=function(t,e){return Tn(this,t,e,-1)},ei.as=function(t){if(!this.isValid())return NaN;var e,n,i=this._milliseconds;if("month"===(t=U(t))||"quarter"===t||"year"===t)switch(e=this._days+i/864e5,n=this._months+Pn(e),t){case"month":return n;case"quarter":return n/3;case"year":return n/12}else switch(e=this._days+Math.round(Wn(this._months)),t){case"week":return e/7+i/6048e5;case"day":return e+i/864e5;case"hour":return 24*e+i/36e5;case"minute":return 1440*e+i/6e4;case"second":return 86400*e+i/1e3;case"millisecond":return Math.floor(864e5*e)+i;default:throw new Error("Unknown unit "+t)}},ei.asMilliseconds=Hn,ei.asSeconds=Rn,ei.asMinutes=Fn,ei.asHours=Un,ei.asDays=Ln,ei.asWeeks=Vn,ei.asMonths=Nn,ei.asQuarters=Gn,ei.asYears=An,ei.valueOf=function(){return this.isValid()?this._milliseconds+864e5*this._days+this._months%12*2592e6+31536e6*D(this._months/12):NaN},ei._bubble=function(){var t,e,n,i,s,r=this._milliseconds,a=this._days,o=this._months,u=this._data;return r>=0&&a>=0&&o>=0||r<=0&&a<=0&&o<=0||(r+=864e5*xn(Wn(o)+a),a=0,o=0),u.milliseconds=r%1e3,t=k(r/1e3),u.seconds=t%60,e=k(t/60),u.minutes=e%60,n=k(e/60),u.hours=n%24,o+=s=k(Pn(a+=k(n/24))),a-=xn(Wn(s)),i=k(o/12),o%=12,u.days=a,u.months=o,u.years=i,this},ei.clone=function(){return qe(this)},ei.get=function(t){return t=U(t),this.isValid()?this[t+"s"]():NaN},ei.milliseconds=jn,ei.seconds=In,ei.minutes=zn,ei.hours=Zn,ei.days=$n,ei.weeks=function(){return k(this.days()/7)},ei.months=qn,ei.years=Jn,ei.humanize=function(t){if(!this.isValid())return this.localeData().invalidDate();var e=this.localeData(),n=function(t,e,n){var i=qe(t).abs(),s=Bn(i.as("s")),r=Bn(i.as("m")),a=Bn(i.as("h")),o=Bn(i.as("d")),u=Bn(i.as("M")),l=Bn(i.as("y")),d=s<=Qn.ss&&["s",s]||s<Qn.s&&["ss",s]||r<=1&&["m"]||r<Qn.m&&["mm",r]||a<=1&&["h"]||a<Qn.h&&["hh",a]||o<=1&&["d"]||o<Qn.d&&["dd",o]||u<=1&&["M"]||u<Qn.M&&["MM",u]||l<=1&&["y"]||["yy",l];return d[2]=e,d[3]=+t>0,d[4]=n,function(t,e,n,i,s){return s.relativeTime(e||1,!!n,t,i)}.apply(null,d)}(this,!t,e);return t&&(n=e.pastFuture(+this,n)),e.postformat(n)},ei.toISOString=ti,ei.toString=ti,ei.toJSON=ti,ei.locale=nn,ei.localeData=rn,ei.toIsoString=b("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",ti),ei.lang=sn,z("X",0,0,"unix"),z("x",0,0,"valueOf"),ht("x",at),ht("X",/[+-]?\d+(\.\d{1,3})?/),_t("X",function(t,e,n){n._d=new Date(1e3*parseFloat(t,10))}),_t("x",function(t,e,n){n._d=new Date(D(t))}),s.version="2.24.0",e=Ce,s.fn=Sn,s.min=function(){return Fe("isBefore",[].slice.call(arguments,0))},s.max=function(){return Fe("isAfter",[].slice.call(arguments,0))},s.now=function(){return Date.now?Date.now():+new Date},s.utc=m,s.unix=function(t){return Ce(1e3*t)},s.months=function(t,e){return Yn(t,e,"months")},s.isDate=d,s.locale=ce,s.invalid=p,s.duration=qe,s.isMoment=M,s.weekdays=function(t,e,n){return On(t,e,n,"weekdays")},s.parseZone=function(){return Ce.apply(null,arguments).parseZone()},s.localeData=me,s.isDuration=Ve,s.monthsShort=function(t,e){return Yn(t,e,"monthsShort")},s.weekdaysMin=function(t,e,n){return On(t,e,n,"weekdaysMin")},s.defineLocale=fe,s.updateLocale=function(t,e){if(null!=e){var n,i,s=oe;null!=(i=he(t))&&(s=i._config),(n=new H(e=C(s,e))).parentLocale=ue[t],ue[t]=n,ce(t)}else null!=ue[t]&&(null!=ue[t].parentLocale?ue[t]=ue[t].parentLocale:null!=ue[t]&&delete ue[t]);return ue[t]},s.locales=function(){return T(ue)},s.weekdaysShort=function(t,e,n){return On(t,e,n,"weekdaysShort")},s.normalizeUnits=U,s.relativeTimeRounding=function(t){return void 0===t?Bn:"function"==typeof t&&(Bn=t,!0)},s.relativeTimeThreshold=function(t,e){return void 0!==Qn[t]&&(void 0===e?Qn[t]:(Qn[t]=e,"s"===t&&(Qn.ss=e-1),!0))},s.calendarFormat=function(t,e){var n=t.diff(e,"days",!0);return n<-6?"sameElse":n<-1?"lastWeek":n<0?"lastDay":n<1?"sameDay":n<2?"nextDay":n<7?"nextWeek":"sameElse"},s.prototype=Sn,s.HTML5_FMT={DATETIME_LOCAL:"YYYY-MM-DDTHH:mm",DATETIME_LOCAL_SECONDS:"YYYY-MM-DDTHH:mm:ss",DATETIME_LOCAL_MS:"YYYY-MM-DDTHH:mm:ss.SSS",DATE:"YYYY-MM-DD",TIME:"HH:mm",TIME_SECONDS:"HH:mm:ss",TIME_MS:"HH:mm:ss.SSS",WEEK:"GGGG-[W]WW",MONTH:"YYYY-MM"},s},"object"===a(i)?t.exports=r():s.moment=r()}(i={exports:{}},i.exports),i.exports),r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};function a(t){if(Array.isArray(t)){for(var e=0,n=Array(t.length);e<t.length;e++)n[e]=t[e];return n}return Array.from(t)}var o={install:function(t,e){var n=e&&e.moment?e.moment:s;Object.defineProperties(t.prototype,{$moment:{get:function(){return n}}}),t.moment=n,t.filter("moment",function(){for(var t=arguments,e=arguments.length,i=Array(e),s=0;s<e;s++)i[s]=t[s];var a=(i=Array.prototype.slice.call(i)).shift(),o=void 0;if(o=Array.isArray(a)&&"string"==typeof a[0]?n(a[0],a[1],!0):"number"==typeof a&&a.toString().length<12?n.unix(a):n(a),!a||!o.isValid())return console.warn("Could not build a valid `moment` object from input."),a;function u(){for(var t=arguments,e=arguments.length,i=Array(e),s=0;s<e;s++)i[s]=t[s];var a=(i=Array.prototype.slice.call(i)).shift();switch(a){case"add":for(var l=i.shift().split(",").map(Function.prototype.call,String.prototype.trim),d={},h=0;h<l.length;h++){var c=l[h].split(" ");d[c[1]]=c[0]}o.add(d);break;case"subtract":for(var f=i.shift().split(",").map(Function.prototype.call,String.prototype.trim),m={},_=0;_<f.length;_++){var y=f[_].split(" ");m[y[1]]=y[0]}o.subtract(m);break;case"from":var p="now",g=!1;"now"===i[0]&&i.shift(),n(i[0]).isValid()&&(p=n(i.shift())),!0===i[0]&&(i.shift(),g=!0),o="now"!==p?o.from(p,g):o.fromNow(g);break;case"diff":var v=n(),w="",S=!1;n(i[0]).isValid()?v=n(i.shift()):null!==i[0]&&"now"!==i[0]||i.shift(),i[0]&&(w=i.shift()),!0===i[0]&&(S=i.shift()),o=o.diff(v,w,S);break;case"calendar":var M=n(),k={};n(i[0]).isValid()?M=n(i.shift()):null!==i[0]&&"now"!==i[0]||i.shift(),"object"===r(i[0])&&(k=i.shift()),o=o.calendar(M,k);break;case"utc":o.utc();break;case"timezone":o.tz(i.shift());break;default:var D=a;o=o.format(D)}i.length&&u.apply(u,i)}return u.apply(u,i),o}),t.filter("duration",function(){for(var t=arguments,e=arguments.length,i=Array(e),s=0;s<e;s++)i[s]=t[s];var r=(i=Array.prototype.slice.call(i)).shift(),o=i.shift();function u(t){Array.isArray(t)||(t=[t]);var e=n.duration.apply(n,a(t));return e.isValid()||console.warn("Could not build a valid `duration` object from input."),e}var l=u(r);if("add"===o||"subtract"===o){var d=u(i);l[o](d)}else if(l&&l[o]){var h;l=(h=l)[o].apply(h,a(i))}return l})}},u=o.install;t.default=o,t.install=u,Object.defineProperty(t,"__esModule",{value:!0})});
//# sourceMappingURL=vue-moment.min.js.map
