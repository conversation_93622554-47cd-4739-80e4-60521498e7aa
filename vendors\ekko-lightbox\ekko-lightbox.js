(function() {
  "use strict";
  var $, EkkoLightbox;

  $ = jQuery;

  EkkoLightbox = function(element, options) {
    var content, footer, header;
    this.options = $.extend({
      title: null,
      footer: null,
      remote: null
    }, $.fn.ekkoLightbox.defaults, options || {});
    this.$element = $(element);
    content = '';
    this.modal_id = this.options.modal_id ? this.options.modal_id : 'ekkoLightbox-' + Math.floor((Math.random() * 1000) + 1);
    header = '<div class="modal-header"' + (this.options.title || this.options.always_show_close ? '' : ' style="display:none"') + '><h5 class="modal-title">' + (this.options.title || "&nbsp;") + '</h5><button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button></div>';
    footer = '<div class="modal-footer"' + (this.options.footer ? '' : ' style="display:none"') + '>' + this.options.footer + '</div>';
    $(document.body).append('<div id="' + this.modal_id + '" class="ekko-lightbox modal fade" tabindex="-1"><div class="modal-dialog' + (this.options.centered ? ' modal-dialog-centered' : '') + '"><div class="modal-content">' + header + '<div class="modal-body"><div class="ekko-lightbox-container"><div></div></div></div>' + footer + '</div></div></div>');
    this.modal = $('#' + this.modal_id);
    this.modal_dialog = this.modal.find('.modal-dialog').first();
    this.modal_content = this.modal.find('.modal-content').first();
    this.modal_body = this.modal.find('.modal-body').first();
    this.modal_header = this.modal.find('.modal-header').first();
    this.modal_footer = this.modal.find('.modal-footer').first();
    this.lightbox_container = this.modal_body.find('.ekko-lightbox-container').first();
    this.lightbox_body = this.lightbox_container.find('> div:first-child').first();
    this.showLoading();
    this.modal_arrows = null;
    this.border = {
      top: parseFloat(this.modal_dialog.css('border-top-width')) + parseFloat(this.modal_content.css('border-top-width')) + parseFloat(this.modal_body.css('border-top-width')),
      right: parseFloat(this.modal_dialog.css('border-right-width')) + parseFloat(this.modal_content.css('border-right-width')) + parseFloat(this.modal_body.css('border-right-width')),
      bottom: parseFloat(this.modal_dialog.css('border-bottom-width')) + parseFloat(this.modal_content.css('border-bottom-width')) + parseFloat(this.modal_body.css('border-bottom-width')),
      left: parseFloat(this.modal_dialog.css('border-left-width')) + parseFloat(this.modal_content.css('border-left-width')) + parseFloat(this.modal_body.css('border-left-width'))
    };
    this.padding = {
      top: parseFloat(this.modal_dialog.css('padding-top')) + parseFloat(this.modal_content.css('padding-top')) + parseFloat(this.modal_body.css('padding-top')),
      right: parseFloat(this.modal_dialog.css('padding-right')) + parseFloat(this.modal_content.css('padding-right')) + parseFloat(this.modal_body.css('padding-right')),
      bottom: parseFloat(this.modal_dialog.css('padding-bottom')) + parseFloat(this.modal_content.css('padding-bottom')) + parseFloat(this.modal_body.css('padding-bottom')),
      left: parseFloat(this.modal_dialog.css('padding-left')) + parseFloat(this.modal_content.css('padding-left')) + parseFloat(this.modal_body.css('padding-left'))
    };
    this.modal.on('show.bs.modal', this.options.onShow.bind(this)).on('shown.bs.modal', (function(_this) {
      return function() {
        _this.modal_shown();
        return _this.options.onShown.call(_this);
      };
    })(this)).on('hide.bs.modal', this.options.onHide.bind(this)).on('hidden.bs.modal', (function(_this) {
      return function() {
        if (_this.gallery) {
          $(document).off('keydown.ekkoLightbox');
        }
        _this.modal.remove();
        return _this.options.onHidden.call(_this);
      };
    })(this)).modal('show', options);
    return this.modal;
  };

  EkkoLightbox.prototype = {
    modal_shown: function() {
      if (!this.options.remote) {
        return this.error('No remote target given');
      } else {
        this.gallery = this.$element.data('gallery');
        if (this.gallery) {
          if (this.options.gallery_parent_selector === 'document.body' || this.options.gallery_parent_selector === '') {
            this.gallery_items = $(document.body).find('*[data-gallery="' + this.gallery + '"]');
          } else {
            this.gallery_items = this.$element.parents(this.options.gallery_parent_selector).first().find('*[data-gallery="' + this.gallery + '"]');
          }
          this.gallery_index = this.gallery_items.index(this.$element);
          $(document).on('keydown.ekkoLightbox', this.navigate.bind(this));
          if (this.options.directional_arrows && this.gallery_items.length > 1) {
            this.lightbox_container.append('<div class="ekko-lightbox-nav-overlay"><a href="#" class="' + this.strip_stops(this.options.left_arrow_class) + '"></a><a href="#" class="' + this.strip_stops(this.options.right_arrow_class) + '"></a></div>');
            this.modal_arrows = this.lightbox_container.find('div.ekko-lightbox-nav-overlay').first();
            this.lightbox_container.find('a' + this.strip_spaces(this.options.left_arrow_class)).on('click', (function(_this) {
              return function(event) {
                event.preventDefault();
                return _this.navigate_left();
              };
            })(this));
            this.lightbox_container.find('a' + this.strip_spaces(this.options.right_arrow_class)).on('click', (function(_this) {
              return function(event) {
                event.preventDefault();
                return _this.navigate_right();
              };
            })(this));
          }
        }
        if (this.options.type) {
          if (this.options.type === 'image') {
            return this.preloadImage(this.options.remote, true);
          } else if (this.options.type === 'url') {
            return this.loadRemoteContent(this.options.remote);
          } else if (this.options.type === 'video') {
            return this.showVideoIframe(this.options.remote);
          } else if (this.options.type === 'iframe') {
            return this.showCommonIframe(this.options.remote);
          } else {
            return this.error("Could not detect remote target type. Force the type using data-type=\"image|url|video\"");
          }
        } else {
          return this.detectRemoteType(this.options.remote);
        }
      }
    },
    strip_stops: function(str) {
      return str.replace(/\./g, '');
    },
    strip_spaces: function(str) {
      return str.replace(/\s/g, '');
    },
    isImage: function(str) {
      return str.match(/(^data:image\/.*,)|(\.(jp(e|g|eg)|gif|png|bmp|webp|svg)((\?|#).*)?$)/i);
    },
    isSwf: function(str) {
      return str.match(/\.(swf)((\?|#).*)?$/i);
    },
    navigate: function(event) {
      event = event || window.event;
      if (event.keyCode === 39 || event.keyCode === 37) {
        if (event.keyCode === 39) {
          return this.navigate_right();
        } else if (event.keyCode === 37) {
          return this.navigate_left();
        }
      }
    },
    navigateTo: function(index) {
      var next, src;
      if (index < 0 || index > this.gallery_items.length - 1) {
        return this;
      }
      this.showLoading();
      this.gallery_index = index;
      this.$element = $(this.gallery_items.get(this.gallery_index));
      this.updateTitleAndFooter();
      src = this.$element.attr('data-remote') || this.$element.attr('href');
      this.detectRemoteType(src, this.$element.attr('data-type') || false);
      if (this.gallery_index + 1 < this.gallery_items.length) {
        next = $(this.gallery_items.get(this.gallery_index + 1), false);
        src = next.attr('data-remote') || next.attr('href');
        if (next.attr('data-type') === 'image' || this.isImage(src)) {
          return this.preloadImage(src, false);
        }
      }
    },
    navigate_left: function() {
      if (this.gallery_items.length === 1) {
        return;
      }
      if (this.gallery_index === 0) {
        this.gallery_index = this.gallery_items.length - 1;
      } else {
        this.gallery_index--;
      }
      this.options.onNavigate.call(this, 'left', this.gallery_index);
      return this.navigateTo(this.gallery_index);
    },
    navigate_right: function() {
      if (this.gallery_items.length === 1) {
        return;
      }
      if (this.gallery_index === this.gallery_items.length - 1) {
        this.gallery_index = 0;
      } else {
        this.gallery_index++;
      }
      this.options.onNavigate.call(this, 'right', this.gallery_index);
      return this.navigateTo(this.gallery_index);
    },
    detectRemoteType: function(src, type) {
      type = type || false;
      if (type === 'image' || this.isImage(src)) {
        this.options.type = 'image';
        return this.preloadImage(src, true);
      } else if (type === 'video') {
        this.options.type = 'video';
        return this.showVideoIframe(src);
      } else if (type === 'iframe') {
        this.options.type = 'iframe';
        return this.showCommonIframe(src);
      } else {
        this.options.type = 'url';
        return this.loadRemoteContent(src);
      }
    },
    updateTitleAndFooter: function() {
      var caption, footer, header, title;
      header = this.modal_content.find('.modal-header');
      footer = this.modal_content.find('.modal-footer');
      title = this.$element.data('title') || "";
      caption = this.$element.data('footer') || "";
      if (title || this.options.always_show_close) {
        header.css('display', '').find('.modal-title').html(title || "&nbsp;");
      } else {
        header.css('display', 'none');
      }
      if (caption) {
        footer.css('display', '').html(caption);
      } else {
        footer.css('display', 'none');
      }
      return this;
    },
    showLoading: function() {
      this.lightbox_body.html('<div class="modal-loading">' + this.options.loadingMessage + '</div>');
      return this;
    },
    showVideoIframe: function(url) {
      var width, height, maxHeight;
      width = this.$element.data('width') || 500;
      maxHeight = this.getMaxHeight();
      height = Math.round(width * 9 / 16);
      if (height > maxHeight) {
        height = maxHeight;
      }
      this.lightbox_container.css('height', height);
      this.modal_body.css('padding', '10');
      this.modal_dialog.css('width', 'auto').css('max-width', width + 20);
      var iframeName = 'iframe-' + this.modal_id;
      this.lightbox_body.html('<iframe id="' + iframeName + '" name="' + iframeName + '" src="' + url + '" frameborder="no"  allowfullscreen="true" mozallowfullscreen="true" webkitallowfullscreen="true"  allowtransparency="true" scrolling="no" style="width: 100%; height: ' + height + 'px; visibility: hidden"></iframe>');
      var $lightbox_container = this.lightbox_container;
      var frame = document.getElementById(iframeName);
      frame.onload = frame.onreadystatechange = function () {
        try {
          var frame$ = window.frames[iframeName].$;
          if (frame$) {
            frame$('html,body').css("height", "auto");
            var $framebody = frame$('body');
            var ajustFrameSize = function () {
              var player;
              if (frame$("video").length > 0 && window.frames[iframeName].videojs) {
                player = window.frames[iframeName].videojs(frame$("video")[0]);
                player.width(width);

                var height = $framebody.outerHeight();
                if (height > maxHeight) {
                  height = maxHeight;
                  player.height(height);
                }
                $lightbox_container.css('height', height);
                $(frame).css('height', height);
              }
              $(frame).css("visibility", "visible");
            };

            setTimeout(ajustFrameSize, 100);
          }
        } catch (e) {}
      }
      setTimeout(function () {
        $(frame).css("visibility", "visible");
      }, 1000);
      this.options.onContentLoaded.call(this);
      if (this.modal_arrows) {
        this.modal_arrows.css('display', 'none');
      }
      return this;
    },
    showCommonIframe: function(url) {
      var width, height, maxHeight;
      width = this.$element.data('width') || 500;
      maxHeight = this.getMaxHeight();
      height = 69;
      if (height > maxHeight) {
        height = maxHeight;
      }
      this.lightbox_container.css('height', height);
      this.modal_body.css('padding', '0');
      this.modal_dialog.css('width', 'auto').css('max-width', width + 20);
      var iframeName = 'iframe-' + this.modal_id;
      this.lightbox_body.html('<iframe id="' + iframeName + '" name="' + iframeName + '" src="' + url + '" frameborder="no"  allowfullscreen="true" mozallowfullscreen="true" webkitallowfullscreen="true"  allowtransparency="true" scrolling="auto" style="width: 100%; height: ' + height + 'px; visibility: hidden; border-bottom-left-radius: 8px; border-bottom-right-radius: 8px"></iframe>');
      var $lightbox_container = this.lightbox_container;
      var frame = document.getElementById(iframeName);
      frame.onload = frame.onreadystatechange = function () {
        try {
          var frame$ = window.frames[iframeName].$;
          if (frame$) {
            frame$('html,body').css("height", "auto");
            var $framebody = frame$('body');
            var ajustFrameSize = function () {
              var height = $framebody.outerHeight();
              if (height > maxHeight) {
                height = maxHeight;
              } else {
                $(frame).attr("scrolling", "no");
              }
              $lightbox_container.css('height', height);
              $(frame).css('height', height).css("visibility", "visible");
            };

            setTimeout(ajustFrameSize, 100);
          }
        } catch (e) {}
      }
      setTimeout(function () {
        $(frame).css("visibility", "visible");
      }, 1000);
      this.options.onContentLoaded.call(this);
      if (this.modal_arrows) {
        this.modal_arrows.css('display', 'none');
      }
      return this;
    },
    loadRemoteContent: function(url) {
      var disableExternalCheck, width, height;
      width = this.$element.data('width') || 500;
      height = this.$element.data('height') || this.getMaxHeight();
      this.resize(width);
      disableExternalCheck = this.$element.data('disableExternalCheck') || false;
      if (!disableExternalCheck && !this.isExternal(url)) {
        this.lightbox_body.load(url, $.proxy((function(_this) {
          return function() {
            return _this.$element.trigger('loaded.bs.modal');
          };
        })(this)));
      } else {
        this.modal_body.css('padding', 0);
        this.lightbox_body.html('<iframe width="100%" height="' + height + '" src="' + url + '" frameborder="0" allowfullscreen></iframe>');
        this.options.onContentLoaded.call(this);
      }
      if (this.modal_arrows) {
        this.modal_arrows.css('display', 'none');
      }
      return this;
    },
    isExternal: function(url) {
      var match;
      match = url.match(/^([^:\/?#]+:)?(?:\/\/([^\/?#]*))?([^?#]+)?(\?[^#]*)?(#.*)?/);
      if (typeof match[1] === "string" && match[1].length > 0 && match[1].toLowerCase() !== location.protocol) {
        return true;
      }
      if (typeof match[2] === "string" && match[2].length > 0 && match[2].replace(new RegExp(":(" + {
        "http:": 80,
        "https:": 443
      }[location.protocol] + ")?$"), "") !== location.host) {
        return true;
      }
      return false;
    },
    error: function(message) {
      this.lightbox_body.html(message);
      return this;
    },
    preloadImage: function(src, onLoadShowImage) {
      var img;
      img = new Image();
      if ((onLoadShowImage == null) || onLoadShowImage === true) {
        img.onload = (function(_this) {
          return function() {
            var image;
            image = $('<img />');
            image.attr('src', img.src);
            image.addClass('img-responsive');
            _this.lightbox_body.html(image);
            if (_this.modal_arrows) {
              _this.modal_arrows.css('display', 'block');
            }
            return image.load(function() {
              if (_this.options.scale_height) {
                _this.scaleHeight(img.height, img.width);
              } else {
                _this.resize(img.width);
              }
              return _this.options.onContentLoaded.call(_this);
            });
          };
        })(this);
        img.onerror = (function(_this) {
          return function() {
            return _this.error('Failed to load image: ' + src);
          };
        })(this);
      }
      img.src = src;
      return img;
    },
    scaleHeight: function(height, width) {
      var border_padding, factor, footer_height, header_height, margins, max_height;
      header_height = this.modal_header.outerHeight(true) || 0;
      footer_height = this.modal_footer.outerHeight(true) || 0;
      if (!this.modal_footer.is(':visible')) {
        footer_height = 0;
      }
      if (!this.modal_header.is(':visible')) {
        header_height = 0;
      }
      border_padding = this.border.top + this.border.bottom + this.padding.top + this.padding.bottom;
      margins = parseFloat(this.modal_dialog.css('margin-top')) + parseFloat(this.modal_dialog.css('margin-bottom'));
      max_height = $(window).height() - border_padding - margins - header_height - footer_height;
      factor = Math.min(max_height / height, 1);
      this.modal_dialog.css('height', 'auto').css('max-height', max_height);
      return this.resize(factor * width);
    },
    resize: function(width) {
      var width_total;
      width_total = width + this.border.left + this.padding.left + this.padding.right + this.border.right;
      this.modal_dialog.css('width', 'auto').css('max-width', width_total);
      this.lightbox_container.find('a').css('line-height', function() {
        return $(this).parent().height() + 'px';
      });
      return this;
    },
    checkDimensions: function(width) {
      var body_width, width_total;
      width_total = width + this.border.left + this.padding.left + this.padding.right + this.border.right;
      body_width = document.body.clientWidth;
      if (width_total > body_width) {
        width = this.modal_body.width();
      }
      return width;
    },
    close: function() {
      return this.modal.modal('hide');
    },
    addTrailingSlash: function(url) {
      if (url.substr(-1) !== '/') {
        url += '/';
      }
      return url;
    },
    getMaxHeight: function() {
      var border_padding, footer_height, header_height, margins, max_height;
      header_height = this.modal_header.outerHeight(true) || 0;
      footer_height = this.modal_footer.outerHeight(true) || 0;
      if (!this.modal_footer.is(':visible')) {
        footer_height = 0;
      }
      if (!this.modal_header.is(':visible')) {
        header_height = 0;
      }
      border_padding = this.border.top + this.border.bottom + this.padding.top + this.padding.bottom;
      margins = parseFloat(this.modal_dialog.css('margin-top')) + parseFloat(this.modal_dialog.css('margin-bottom'));
      max_height = $(window).height() - border_padding - margins - header_height - footer_height;
      return max_height;
    }
  };

  $.fn.ekkoLightbox = function(options) {
    return this.each(function() {
      var $this;
      $this = $(this);
      options = $.extend({
        remote: $this.attr('data-remote') || $this.attr('href'),
        gallery_parent_selector: $this.attr('data-parent'),
        type: $this.attr('data-type')
      }, options, $this.data());
      new EkkoLightbox(this, options);
      return this;
    });
  };

  $.fn.ekkoLightbox.defaults = {
    gallery_parent_selector: 'document.body',
    left_arrow_class: '.fe .fe-chevron-left',
    right_arrow_class: '.fe .fe-chevron-right',
    directional_arrows: true,
    type: null,
    always_show_close: false,
    no_related: false,
    scale_height: true,
    loadingMessage: '正在加载 ……',
    onShow: function() {},
    onShown: function() {},
    onHide: function() {},
    onHidden: function() {},
    onNavigate: function() {},
    onContentLoaded: function() {}
  };

}).call(this);
